schema:
  - "graphql/schema/types/base.graphql"
  - "graphql/schema/types/*.graphql"

generates:

  graphql/generated/resolvers/index.js:
    plugins:
      - "typescript"
      - "typescript-resolvers"
    config:
      useIndexSignature: true
      contextType: "../../context#Context"
      mappers:
        # 更新 mappers 以匹配 Mongoose 模型
        Order: "../../models/order.model#IOrder"  # IOrder 为 Mongoose 接口类型

  graphql/generated/client/index.js:
    preset: near-operation-file
    presetConfig:
      extension: .js
      baseTypesPath: ../types.js
    plugins:
      - add:
          content: "/* eslint-disable */"


hooks:
  afterAllFileWrite:
  #  - babel graphql/generated --out-dir graphql/generated --extensions ".ts"