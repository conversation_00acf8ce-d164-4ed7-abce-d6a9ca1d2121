# 餐厅Android App退款接口使用指导

## 概述

本文档为餐厅Android App开发者提供退款功能的完整接口使用指导。退款系统支持全额退款（取消订单）和部分退款两种模式。

## 认证要求

所有退款相关的API调用都需要餐厅管理员身份验证。请确保在请求头中包含有效的认证token。

```
Authorization: Bearer <restaurant_token>
```

## 退款类型

### 1. 全额退款（取消订单）
- **用途**：完全取消订单并退还全部金额
- **结果**：订单状态变为 `CANCELLED`
- **适用场景**：整个订单无法完成

### 2. 部分退款
- **用途**：退还部分金额，订单继续进行
- **结果**：订单状态变为 `PARTIALLY_REFUNDED`
- **适用场景**：部分商品缺货、价格调整等

## GraphQL接口

### 全额退款（取消订单）

#### Mutation
```graphql
mutation CancelOrder($orderId: String!, $reason: RefundReason!) {
  cancelOrder(_id: $orderId, reason: $reason) {
    _id
    orderId
    orderStatus
    refundStatus
    totalRefunded
    refunds {
      _id
      refundId
      finalRefundAmount
      status
      reason
    }
  }
}
```

#### 参数说明
- `orderId`: 订单ID（必填）
- `reason`: 退款原因（必填，枚举值）

#### 退款原因枚举
```
MERCHANT_OUT_OF_STOCK      # 商家-缺货
MERCHANT_CANNOT_DELIVER    # 商家-无法配送
MERCHANT_OTHER            # 商家-其它
CUSTOMER_CANCELLED        # 客户-取消订单
```

#### 示例请求
```json
{
  "query": "mutation CancelOrder($orderId: String!, $reason: RefundReason!) { cancelOrder(_id: $orderId, reason: $reason) { _id orderId orderStatus refundStatus totalRefunded } }",
  "variables": {
    "orderId": "507f1f77bcf86cd799439011",
    "reason": "MERCHANT_OUT_OF_STOCK"
  }
}
```

### 部分退款

#### Mutation
```graphql
mutation RefundOrder(
  $orderId: String!
  $amount: Float!
  $reason: RefundReason!
  $reasonText: String
) {
  refundOrder(
    _id: $orderId
    amount: $amount
    reason: $reason
    reasonText: $reasonText
  ) {
    success
    message
    refund {
      _id
      refundId
      finalRefundAmount
      status
      reason
      reasonText
      feeBearer
      transactionFee
    }
    order {
      _id
      orderId
      orderStatus
      refundStatus
      totalRefunded
    }
  }
}
```

#### 参数说明
- `orderId`: 订单ID（必填）
- `amount`: 退款金额（必填）
- `reason`: 退款原因（必填）
- `reasonText`: 退款原因文本（可选，建议在部分退款时提供详细说明）

#### 示例请求
```json
{
  "query": "mutation RefundOrder($orderId: String!, $amount: Float!, $reason: RefundReason!, $reasonText: String) { refundOrder(_id: $orderId, amount: $amount, reason: $reason, reasonText: $reasonText) { success message refund { refundId finalRefundAmount status feeBearer } order { orderId orderStatus refundStatus totalRefunded } } }",
  "variables": {
    "orderId": "507f1f77bcf86cd799439011",
    "amount": 15.50,
    "reason": "MERCHANT_OTHER",
    "reasonText": "部分商品缺货，退还2个商品的费用"
  }
}
```

## 查询接口

### 查询退款记录

#### 查询单个退款
```graphql
query GetRefund($refundId: String!) {
  getRefund(refundId: $refundId) {
    _id
    refundId
    orderId
    refundType
    requestAmount
    finalRefundAmount
    reason
    reasonText
    feeBearer
    transactionFee
    status
    createdAt
    processedAt
    completedAt
  }
}
```

#### 查询订单的所有退款
```graphql
query GetOrderRefunds($orderId: String!) {
  getOrderRefunds(orderId: $orderId) {
    _id
    refundId
    refundType
    finalRefundAmount
    reason
    reasonText
    status
    createdAt
    completedAt
  }
}
```

## 手续费规则

### 商家原因退款
- **适用情况**：`MERCHANT_OUT_OF_STOCK`、`MERCHANT_CANNOT_DELIVER`、`MERCHANT_OTHER`
- **手续费承担**：商家承担
- **客户收到**：全额退款金额
- **商家扣除**：退款金额 + 交易手续费

### 客户原因退款
- **适用情况**：`CUSTOMER_CANCELLED`
- **手续费承担**：客户承担
- **客户收到**：退款金额 - 按比例分摊的手续费
- **商家扣除**：退款金额 - 按比例分摊的手续费

## 退款状态

### 退款记录状态
- `PENDING`: 退款请求已创建，等待处理
- `PROCESSING`: 正在处理退款
- `SUCCEEDED`: 退款成功
- `FAILED`: 退款失败

### 订单退款状态
- `NONE`: 无退款
- `PARTIAL`: 部分退款
- `FULL`: 全额退款

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 订单或退款记录不存在
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "errors": [
    {
      "message": "Refund amount cannot exceed available amount: 25.50",
      "extensions": {
        "code": "INVALID_REFUND_AMOUNT"
      }
    }
  ]
}
```

## 最佳实践

### 1. 退款前验证
- 检查订单状态是否允许退款
- 验证退款金额不超过可退款余额
- 确认退款原因的准确性

### 2. 用户体验
- 在发起退款前显示预览信息
- 清楚说明手续费承担方
- 提供退款进度跟踪

### 3. 错误处理
- 实现重试机制处理网络错误
- 提供用户友好的错误信息
- 记录详细的错误日志

### 4. 状态同步
- 定期查询退款状态
- 实现实时状态更新（如果支持WebSocket）
- 处理异步状态变更

## 测试建议

### 1. 功能测试
- 测试全额退款流程
- 测试部分退款流程
- 测试各种退款原因
- 测试边界条件（如超额退款）

### 2. 错误场景测试
- 网络异常处理
- 无效参数处理
- 权限验证
- 重复退款请求

### 3. 集成测试
- 与Stripe的集成测试
- Webhook事件处理测试
- 状态同步测试

## 支持与联系

如有技术问题或需要进一步支持，请联系：
- 技术支持邮箱：<EMAIL>
- 开发者文档：https://docs.firespoon.com
- API状态页面：https://status.firespoon.com
