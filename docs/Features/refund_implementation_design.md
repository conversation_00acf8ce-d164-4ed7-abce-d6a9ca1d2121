# 退款系统实现设计文档

## 概述

本文档描述了Firespoon API中退款系统的完整实现设计，支持全额退款和部分退款两种模式。

## 设计原则

1. **双操作模式**：
   - `cancelOrder`：全额退款 + 取消订单
   - `refundOrder`：部分退款 + 订单继续

2. **手续费承担规则**：
   - 商家原因：商家承担手续费，客户收到全额退款
   - 客户原因：客户承担手续费，从退款中扣除

3. **状态管理**：
   - 退款记录独立追踪
   - 订单状态反映退款情况

## 数据模型设计

### 1. Refund模型 (models/refund.js)

```javascript
const refundSchema = {
  refundId: String,              // 退款唯一标识
  orderId: String,               // 关联订单ID
  originalOrderId: ObjectId,     // 原始订单ObjectId
  refundType: String,            // FULL, PARTIAL
  requestAmount: Number,         // 请求退款金额
  finalRefundAmount: Number,     // 实际退款金额
  reason: String,                // 退款原因枚举
  reasonText: String,            // 部分退款原因文本（可选）
  feeBearer: String,             // MERCHANT, CUSTOMER
  transactionFee: Number,        // 交易手续费
  status: String,                // PENDING, PROCESSING, SUCCEEDED, FAILED
  stripeRefundId: String,        // Stripe退款ID
  requestedBy: ObjectId,         // 发起人
  createdAt: Date,
  processedAt: Date,
  completedAt: Date
}
```

### 2. 扩展Order模型

```javascript
// 新增字段
refunds: [{ type: ObjectId, ref: 'Refund' }],  // 关联退款记录
totalRefunded: { type: Number, default: 0 },   // 已退款总额
refundStatus: String                            // NONE, PARTIAL, FULL
```

### 3. 枚举定义更新 (helpers/enum.js)

```javascript
exports.REFUND_REASON = {
  MERCHANT_OUT_OF_STOCK: 'MERCHANT_OUT_OF_STOCK',
  MERCHANT_CANNOT_DELIVER: 'MERCHANT_CANNOT_DELIVER', 
  MERCHANT_OTHER: 'MERCHANT_OTHER',
  CUSTOMER_CANCELLED: 'CUSTOMER_CANCELLED'
};

exports.REFUND_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCEEDED: 'SUCCEEDED',
  FAILED: 'FAILED'
};

exports.REFUND_TYPE = {
  FULL: 'FULL',
  PARTIAL: 'PARTIAL'
};

// 扩展订单状态
exports.ORDER_STATUS = {
  // ... 现有状态
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  REFUNDED: 'REFUNDED'
};
```

## GraphQL Schema设计

### 1. 类型定义 (graphql/schema/types/order.graphql)

```graphql
enum RefundReason {
  MERCHANT_OUT_OF_STOCK
  MERCHANT_CANNOT_DELIVER
  MERCHANT_OTHER
  CUSTOMER_CANCELLED
}

enum RefundType {
  FULL
  PARTIAL
}

enum RefundStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
}

type Refund {
  _id: ID!
  refundId: String!
  orderId: String!
  refundType: RefundType!
  requestAmount: Float!
  finalRefundAmount: Float!
  reason: RefundReason!
  reasonText: String
  feeBearer: String!
  transactionFee: Float!
  status: RefundStatus!
  stripeRefundId: String
  createdAt: DateTime!
  processedAt: DateTime
  completedAt: DateTime
}

type RefundResponse {
  success: Boolean!
  refund: Refund!
  order: Order!
  message: String
}

extend type Order {
  refunds: [Refund!]!
  totalRefunded: Float!
  refundStatus: String!
}

extend type Mutation {
  # 全额退款（取消订单）
  cancelOrder(_id: String!, reason: RefundReason!): Order!
  
  # 部分退款
  refundOrder(
    _id: String!
    amount: Float!
    reason: RefundReason!
    reasonText: String
  ): RefundResponse!
}

extend type Query {
  # 查询退款记录
  getRefund(refundId: String!): Refund
  getOrderRefunds(orderId: String!): [Refund!]!
}
```

## 业务逻辑设计

### 1. 退款计算服务 (services/refundCalculationService.js)

```javascript
class RefundCalculationService {
  calculateRefundAmount(order, requestAmount, reason) {
    const isMerchantFault = reason.startsWith('MERCHANT_');
    const transactionFee = this.getTransactionFee(order);
    
    if (isMerchantFault) {
      // 商家承担手续费
      return {
        customerReceives: requestAmount,
        merchantPays: requestAmount + transactionFee,
        feeBearer: 'MERCHANT',
        transactionFee
      };
    } else {
      // 客户承担手续费
      const proportionalFee = this.calculateProportionalFee(order, requestAmount);
      return {
        customerReceives: requestAmount - proportionalFee,
        merchantPays: requestAmount - proportionalFee,
        feeBearer: 'CUSTOMER',
        transactionFee: proportionalFee
      };
    }
  }
}
```

### 2. 退款服务 (services/refundService.js)

```javascript
class RefundService {
  async initiateRefund(orderId, amount, reason, reasonText, refundType) {
    // 1. 验证订单和金额
    // 2. 计算退款金额和手续费
    // 3. 创建退款记录
    // 4. 调用Stripe API
    // 5. 更新订单状态
    // 6. 发送通知
  }
  
  async processRefundWebhook(stripeRefundId, status) {
    // 处理Stripe webhook回调
  }
}
```

## API接口设计

### 1. GraphQL Mutations

#### cancelOrder（增强版）
- **输入**：订单ID、退款原因
- **输出**：更新后的订单
- **逻辑**：全额退款 + 取消订单

#### refundOrder（新增）
- **输入**：订单ID、退款金额、退款原因、原因文本（可选）
- **输出**：退款响应（包含退款记录和订单）
- **逻辑**：部分退款 + 订单继续

### 2. REST Endpoints (routes/stripe.js)

```javascript
// 新增端点
POST /stripe/refund/:orderId     // 发起退款
GET /stripe/refund/:refundId     // 查询退款状态
POST /stripe/webhook             // 处理退款webhook（扩展现有）
```

## 调用流程

### 全额退款流程
```
餐厅App → cancelOrder mutation
→ 验证权限和订单状态
→ 计算全额退款金额
→ 创建退款记录 (FULL)
→ 调用Stripe退款API
→ 更新订单状态 → CANCELLED
→ 发送通知给客户
```

### 部分退款流程
```
餐厅App → refundOrder mutation
→ 验证权限和退款金额
→ 计算部分退款金额
→ 创建退款记录 (PARTIAL)
→ 调用Stripe退款API
→ 更新订单退款信息
→ 订单状态 → PARTIALLY_REFUNDED
→ 发送通知给客户
```

### Webhook处理流程
```
Stripe → POST /stripe/webhook
→ 验证签名
→ 根据stripeRefundId查找退款记录
→ 更新退款状态 (SUCCEEDED/FAILED)
→ 更新订单状态
→ 发送最终通知
```

## 通知系统

### 1. 客户通知模板
- 退款成功：包含实际退款金额和到账时间
- 退款失败：包含失败原因和后续处理方式

### 2. 餐厅通知
- 退款状态更新
- 手续费扣除明细

## 错误处理

### 1. 验证错误
- 订单不存在或无权限
- 退款金额超过可退款余额
- 订单状态不允许退款

### 2. 业务错误
- Stripe API调用失败
- 重复退款请求
- 网络超时处理

### 3. 数据一致性
- 退款记录与订单状态同步
- 金额计算精度处理
- 并发操作防护

## 安全考虑

1. **权限验证**：只有餐厅管理员可以发起退款
2. **金额验证**：防止超额退款和负数退款
3. **重复防护**：防止重复提交退款请求
4. **审计日志**：记录所有退款操作
