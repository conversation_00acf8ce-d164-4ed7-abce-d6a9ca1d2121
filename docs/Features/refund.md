
### **退款流程概览 (High-Level Flow)**

1.  **发起 (Initiation):** 餐厅管理员在 **Android App** 中选择一个订单，发起退款请求。
2.  **处理 (Processing):** **Firespoon 服务器** 接收请求，根据退款原因计算最终金额，并向 **Stripe/PayPal** 发出指令。
3.  **执行 (Execution):** **Stripe/PayPal** 执行实际的资金退回操作。
4.  **确认 (Confirmation):** **Stripe/PayPal** 通过 Webhook 通知 **Firespoon 服务器** 退款结果（成功/失败）。
5.  **通知 (Notification):** **Firespoon 服务器** 在确认成功后，更新系统数据，并通过短信/WhatsApp 通知 **顾客**，同时更新 **餐厅 Android App** 的订单状态。

---

### **各方职责与流程分解**

#### **1. 餐厅 Android App (界面与操作层)**

**餐厅管理员是退款流程的唯一决策者。App 的核心任务是提供一个清晰、无歧义的操作界面。**

*   **查找与启动：**
    *   管理员在订单列表或订单详情页找到需要处理的订单。
    *   点击【退款】按钮。

*   **提供退款信息（退款界面）：**
    *   **步骤 1: 选择退款类型**
        *   提供【全额退款】和【部分退款】两个选项。
        *   如果选择【部分退款】，则显示一个输入框让管理员填写退款金额。
    *   **步骤 2: 选择退款原因（必选）**
        *   提供一个下拉菜单，包含以下固定选项：
            *   `商家-缺货`
            *   `商家-无法配送`
            *   `商家-其它`
            *   `客户-取消订单`
    *   **步骤 3: 财务影响预览（核心交互！）**
        *   **这是最关键的一步，系统根据选择的`原因`自动计算并清晰展示后果，无需管理员手动计算。**
        *   **如果选择 `商家-xxx`：**
            *   App 界面会显示：“您将承担交易手续费。顾客将收到 **[退款金额]** 元。您的账户将扣除 **[退款金额 + 手续费]** 元。”
        *   **如果选择 `客户-取消订单`：**
            *   App 界面会显示：“交易手续费将从退款中扣除。顾客将收到 **[退款金额 - 手续费]** 元。您的账户将扣除 **[退款金额 - 手续费]** 元。”
    *   **步骤 4: 确认操作**
        *   提供一个【确认退款】按钮。点击后，App 将退款请求（包含订单ID、金额、原因）发送给 Firespoon 服务器。

*   **状态更新：**
    *   提交后，订单状态在 App 上应暂时显示为“退款处理中...”。
    *   当服务器确认退款最终成功或失败后，App 需刷新并显示最终状态，如“已全额退款”、“已部分退款”或“退款失败”。

#### **2. Firespoon 服务器 / Node.js (核心逻辑与调度中心)**

**服务器是整个流程的大脑，负责验证、计算、与外部服务通信并记录一切。**

*   **接收与验证请求：**
    *   接收来自餐厅 App 的退款请求。
    *   验证请求的合法性：该用户是否有权对该订单操作？订单是否处于可退款状态？部分退款金额是否超过原订单金额？

*   **创建与记录：**
    *   在数据库中创建一条“退款记录”，初始状态为 `PENDING`（处理中）。这条记录包含所有信息：订单ID、请求金额、原因、手续费承担方等。**这是审计和追溯的根本。**

*   **核心计算与决策：**
    *   根据请求中的`原因`，确定手续费由谁承担。
    *   从数据库中查询原始订单的支付信息，包括支付总额和当时支付网关收取的手续费。
    *   计算出 **`finalRefundAmount` (最终需要退还给顾客的金额)**。

*   **与外部服务交互（发起）：**
    *   根据原始订单的支付渠道，调用 **Stripe** 或 **PayPal** 的退款 API，并将 `finalRefundAmount` 作为参数传递过去。

*   **处理异步回调（Webhook 监听）：**
    *   这是最关键的环节。服务器必须有一个专门的端点来接收来自 Stripe/PayPal 的 Webhook 通知。
    *   当收到通知（如`refund.succeeded`或`refund.failed`）时：
        1.  根据通知中的交易ID，找到数据库里对应的“退款记录”。
        2.  更新该记录的状态为 `SUCCEEDED` 或 `FAILED`。
        3.  更新关联的“订单记录”的状态（如 `REFUNDED`）。
        4.  **触发对顾客的通知。**
        5.  **触发对餐厅 App 的状态更新**（可通过 Push Notification 或让 App 定期查询）。

#### **3. 外部支付服务 / Stripe & PayPal (执行层)**

**它们是资金的实际处理者，是流程的执行方，而不是决策方。**

*   **接收指令：**
    *   从 Firespoon 服务器接收一个带有明确金额的退款请求。

*   **执行退款：**
    *   处理实际的资金转移，将钱退回到顾客的原支付账户（信用卡、PayPal 余额等）。这个过程可能需要几天时间。
    *   **它们不会退还原始交易的手续费。** Firespoon 服务器已经在计算 `finalRefundAmount` 时考虑了这一点。

*   **发送通知（Webhook）：**
    *   一旦退款在其系统内处理完成（无论成功或失败），它们会向 Firespoon 服务器预先配置好的 Webhook URL 发送一个事件通知。**这是 Firespoon 服务器得知最终结果的唯一可靠途径。**

#### **4. 顾客 / 短信 & WhatsApp (信息接收方)**

**顾客是流程的终点，需要接收简单、明确的最终结果。**

*   **接收通知：**
    *   **当且仅当** Firespoon 服务器收到来自支付网关的 **成功** Webhook 后，顾客才会收到通知。
    *   通知由 Firespoon 服务器通过 Payemoji API 发出。

*   **通知内容：**
    *   必须清晰、无歧义，避免混淆。
    *   **模板：** “【Firespoon】您好，关于您的订单 [订单号]，一笔金额为 **[最终退款金额] 元** 的退款已处理成功。款项预计在 5-10 个工作日内退回您的原支付账户。”
    *   **关键点：** 通知中的金额是顾客 **实际会收到** 的金额，这已经是由服务器根据手续费规则计算好的结果。