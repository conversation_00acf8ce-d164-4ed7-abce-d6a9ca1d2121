const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// 坐标Schema
const coordinatesSchema = new Schema({
  longitude: { type: Number, required: true },
  latitude: { type: Number, required: true }
});

// 地址Schema
const addressSchema = new Schema({
  addressId: { type: Schema.Types.ObjectId, required: true },
  placeId: { type: String, required: true },
  formattedAddress: { type: String, required: true },
  streetAddress: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String },
  postcode: { type: String, required: true },
  country: { type: String, required: true },
  coordinates: { type: coordinatesSchema, required: true },

  // Customer-specific fields
  recipientName: { type: String },
  phone: { type: String },
  isDefault: { type: Boolean, default: false },
  tag: { type: String },
  createdAt: { type: Date },
  updatedAt: { type: Date }
});

// 订单简要信息Schema
const orderBriefSchema = new Schema({
  orderId: { type: Schema.Types.ObjectId, required: true, ref: 'Order' },
  amount: { type: Number, required: true },
  restaurantId: { type: Schema.Types.ObjectId, required: true, ref: 'Restaurant' },
  restaurantName: { type: String, required: true },
  restaurantBrandId: { type: String, required: true },
  restaurantBrand: { type: String, required: true },
  orderStatus: {
    type: String,
    required: true,
    enum: ['PENDING', 'PREPARING', 'PICKED', 'DELIVERED', 'CANCELLED']
  },
  orderDate: { type: Date, required: true }
});

// 客户Schema
const customerSchema = new Schema(
  {
    customerId: { type: Schema.Types.ObjectId, required: true },
    name: { type: String },
    email: { type: String },
    phone: { type: String, required: true },
    isActive: { type: Boolean, required: true, default: true },

    // 地址相关
    addressCount: { type: Number, default: 0 },
    addresses: [addressSchema], // 最多20个地址

    // 订单相关
    totalOrders: { type: Number, default: 0 },
    totalAmount: { type: Number, default: 0 },
    orderHistory: [orderBriefSchema],
    brandOrderCount: { type: Number, default: 0 },
    hasMore: { type: Boolean, default: false },

    // 标签
    customerTag: [{ type: String }]
  },
  {
    timestamps: true // 自动管理createdAt和updatedAt
  }
);

// 索引
customerSchema.index({ phone: 1 }, { unique: true });

// 中间件
customerSchema.pre('save', function (next) {
  // 更新地址计数
  this.addressCount = this.addresses.length;

  // 限制地址数量为20
  if (this.addresses.length > 20) {
    this.addresses = this.addresses.slice(0, 20);
  }

  // 更新订单统计
  if (this.orderHistory) {
    this.totalOrders = this.orderHistory.length;
    this.totalAmount = this.orderHistory.reduce((sum, order) => sum + order.amount, 0);
  }

  next();
});

// 虚拟字段
customerSchema.virtual('fullName').get(function () {
  return this.name || 'Anonymous Customer';
});

// 实例方法
customerSchema.methods.addAddress = function (address) {
  this.addresses.push(address);
  return this.save();
};

customerSchema.methods.removeAddress = function (addressId) {
  this.addresses = this.addresses.filter(addr => addr.addressId.toString() !== addressId.toString());
  return this.save();
};

customerSchema.methods.addOrder = function (order) {
  this.orderHistory.push(order);
  return this.save();
};

// 静态方法
customerSchema.statics.findByPhone = function (phone) {
  return this.findOne({ phone });
};

customerSchema.statics.findByEmail = function (email) {
  return this.findOne({ email });
};

customerSchema.statics.findById = function (id) {
  return this.findOne({ _id: id });
};
module.exports = mongoose.model('Customer', customerSchema);
