const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const brandSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      index: true
    },
    website: {
      type: String
    },
    email: {
      type: String,
      required: true,
      index: true
    },
    phone: {
      type: String,
      required: true,
      index: true
    },
    brandWhatsappId: {
      type: String,
      index: true
    },
    logo: {
      type: String
    },
    slug: {
      type: String,
      index: true
    },
    welcomeMessage: {
      type: String,
      default: 'Welcome to our restaurant!'
    },
    orderURL: {
      type: String,
      required: true
    },
    paymentURL: {
      type: String,
      required: true
    },
    restaurants: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Restaurant'
      }
    ],
    ownerId: {
      type: Schema.Types.ObjectId,
      ref: 'Owner',
      required: true,
      index: true
    }
  },
  {
    timestamps: true
  }
);

// Virtual for total restaurants
brandSchema.virtual('totalRestaurants').get(function () {
  return this.restaurants.length;
});

// Ensure virtuals are included in JSON output
brandSchema.set('toJSON', { virtuals: true });
brandSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Brand', brandSchema);
