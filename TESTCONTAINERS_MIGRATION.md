# Testcontainers 集成测试迁移

## 概述

本次迁移将集成测试从使用本地/云数据库改为使用 Testcontainers 管理的容器化数据库服务。这确保了测试环境的一致性和隔离性。

## 主要更改

### 1. 全局测试设置 (`test/config/globalSetup.js`)

**之前**: 使用 MongoDB Atlas 云数据库 + Redis testcontainers
**现在**: 使用 MongoDB 和 Redis 的 testcontainers

主要更改：
- 添加了 MongoDB 7.0 容器配置
- 配置了测试用户凭据 (`testuser:testpass`)
- 统一了数据库容器管理方式

```javascript
// MongoDB 容器配置
const mongoContainer = await new GenericContainer('mongo:7.0')
  .withExposedPorts(27017)
  .withEnvironment({
    'MONGO_INITDB_ROOT_USERNAME': 'testuser',
    'MONGO_INITDB_ROOT_PASSWORD': 'testpass',
    'MONGO_INITDB_DATABASE': 'firespoon_test'
  })
  .start();
```

### 2. 全局测试清理 (`test/config/globalTeardown.js`)

**更改**: 添加了 MongoDB 容器的停止逻辑

```javascript
// 停止 MongoDB 容器
if (global.__MONGO_CONTAINER__) {
  console.log('Stopping MongoDB container...');
  await global.__MONGO_CONTAINER__.stop();
}
```

### 3. 数据库助手 (`test/helpers/dbHelper.js`)

**更改**: 更新了连接日志信息，从 "MongoDB Atlas" 改为 "MongoDB test container"

### 4. 配置管理 (`config.js`)

**重要更改**: 修改了环境变量验证和配置逻辑

- 在测试环境中跳过 `CONNECTION_STRING`、`REDIS_URL`、`REDIS_ENABLE_TLS` 的验证
- 在测试环境中使用全局变量中的数据库连接信息

```javascript
// 测试环境中使用 testcontainers 提供的连接信息
CONNECTION_STRING: process.env.NODE_ENV === 'test' ? global.__MONGO_URI__ : process.env.CONNECTION_STRING,
REDIS_URL: process.env.NODE_ENV === 'test' ? global.__REDIS_URI__ : process.env.REDIS_URL,
REDIS_ENABLE_TLS: process.env.NODE_ENV === 'test' ? false : process.env.REDIS_ENABLE_TLS === 'true',
```

### 5. 环境配置文件

**`.env.test`**: 移除了硬编码的数据库连接配置
**`.env.test.example`**: 更新了说明，指出数据库由 testcontainers 管理

### 6. 测试文档 (`test/README.md`)

**更新**: 说明了新的 testcontainers 配置和要求

## 优势

### 1. **环境一致性**
- 每次测试运行都使用相同版本的数据库
- 消除了本地环境差异

### 2. **测试隔离**
- 每次测试运行都有独立的数据库实例
- 避免了测试之间的数据污染

### 3. **简化设置**
- 开发者只需要安装 Docker
- 无需手动配置数据库连接

### 4. **自动清理**
- 测试结束后自动销毁容器
- 无需手动清理测试数据

## 技术规格

### 数据库版本
- **MongoDB**: 7.0
- **Redis**: 7.2.4

### 容器配置
- **MongoDB**: 
  - 端口: 27017 (动态映射)
  - 用户: testuser
  - 密码: testpass
  - 数据库: firespoon_test
- **Redis**:
  - 端口: 6379 (动态映射)
  - 无认证

### 依赖项
- `testcontainers@10.7.2` (已存在)
- Docker (运行时要求)

## 测试结果

✅ **WhatsApp 集成测试**: 3/3 通过
✅ **GraphQL 集成测试**: 4/4 通过
✅ **总计**: 7/7 测试通过

## 使用方法

### 运行集成测试
```bash
# 运行所有集成测试
npm test -- --testPathPattern="test/integration"

# 运行特定集成测试
npm test -- --testPathPattern="test/integration/whatsapp"
npm test -- --testPathPattern="test/integration/graphql"
```

### 前提条件
1. 确保 Docker 已安装并运行
2. 无需手动配置数据库连接
3. 测试会自动管理容器生命周期

## 注意事项

1. **Docker 要求**: 测试需要 Docker 运行，否则会失败
2. **端口动态分配**: 容器端口是动态分配的，避免冲突
3. **测试隔离**: 每次测试运行都使用新的容器实例
4. **性能**: 容器启动需要额外时间，但提供了更好的隔离性

## 故障排除

### 常见问题

1. **Docker 未运行**
   ```
   Error: Docker is not running
   ```
   解决方案: 启动 Docker 服务

2. **端口冲突**
   - Testcontainers 自动处理端口分配，通常不会有冲突

3. **容器启动超时**
   - 检查 Docker 资源分配
   - 确保网络连接正常

### 调试

查看容器日志：
```bash
docker logs <container_id>
```

检查运行中的容器：
```bash
docker ps
```
