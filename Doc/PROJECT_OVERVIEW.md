# Firespoon API - 项目概览

## 项目简介

Firespoon API 是一个基于 Node.js 的餐饮配送平台后端系统，提供完整的餐厅管理、订单处理、支付集成和 WhatsApp 消息服务。系统采用 GraphQL API 架构，支持多种客户端应用和第三方服务集成。

## 技术栈

### 核心技术
- **运行时**: Node.js 18+
- **框架**: Express.js + Apollo Server (GraphQL)
- **数据库**: MongoDB (Mongoose ODM)
- **缓存**: Redis
- **认证**: JWT + 自定义认证中间件
- **支付**: Stripe + PayPal
- **消息服务**: WhatsApp (Payemoji API)
- **监控**: Sentry
- **日志**: Winston
- **测试**: Jest + Supertest

### 开发工具
- **代码质量**: ESLint + Prettier
- **Git Hooks**: <PERSON>sky + Lint-staged
- **容器化**: Docker + Docker Compose
- **API 文档**: GraphQL Playground/Voyager

## 系统架构

### 应用层次结构
```
┌─────────────────────────────────────────────────────────────┐
│                    客户端应用层                              │
│  Web App │ Mobile App │ Admin Dashboard │ WhatsApp Bot      │
├─────────────────────────────────────────────────────────────┤
│                    API 网关层                               │
│  GraphQL Endpoint │ REST Routes │ WebSocket Subscriptions   │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                               │
│  Resolvers │ Controllers │ Services │ Middleware            │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                               │
│  Mongoose Models │ Redis Client │ External APIs             │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层                               │
│  MongoDB │ Redis │ Stripe │ PayPal │ Payemoji │ Sentry     │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块

#### 1. GraphQL API 核心
- **Schema**: `graphql/schema/types/*.graphql` - GraphQL 类型定义
- **Resolvers**: `graphql/resolvers/*.js` - 业务逻辑实现
- **Context**: 认证和请求上下文管理

#### 2. 数据模型层
- **Models**: `models/*.js` - Mongoose 数据模型
- **核心实体**: Restaurant, Order, Customer, User, Brand, Food, etc.

#### 3. WhatsApp 集成模块
- **对话管理**: 基于状态机的对话流程
- **消息处理**: 多种消息类型支持
- **会话管理**: Redis 基础的会话存储
- **订单集成**: 与主系统订单流程集成

#### 4. 支付处理模块
- **Stripe**: 信用卡支付处理
- **PayPal**: PayPal 支付集成
- **Webhook**: 支付状态同步

#### 5. 认证与授权
- **JWT**: 标准 JWT 认证
- **WhatsApp Auth**: 基于 token 的 WhatsApp 认证
- **角色管理**: 多角色权限控制

## 主要功能模块

### 餐厅管理
- 餐厅信息管理
- 菜单和分类管理
- 营业时间和配送区域
- 订单接收和处理

### 订单系统
- 订单创建和状态管理
- 实时订单跟踪
- 配送费计算
- 订单历史查询

### 客户管理
- 客户注册和认证
- 地址管理
- 订单历史
- 偏好设置

### 支付集成
- 多种支付方式
- 安全支付处理
- 退款管理
- 支付状态同步

### WhatsApp 服务
- 自动化客户服务
- 订单通知
- 菜单浏览
- 地址管理

## 部署架构

### 生产环境
- **应用服务器**: Node.js 集群
- **数据库**: MongoDB Atlas
- **缓存**: Redis Cloud
- **CDN**: 静态资源分发
- **监控**: Sentry + 自定义日志

### 开发环境
- **本地开发**: Docker Compose
- **测试环境**: Jest + MongoDB Memory Server
- **CI/CD**: GitHub Actions (推荐)

## 安全特性

### 数据安全
- 密码加密存储
- JWT token 管理
- API 请求验证
- 输入数据验证

### 网络安全
- CORS 配置
- Rate Limiting
- HTTPS 强制
- Webhook 签名验证

### 业务安全
- 订单状态验证
- 支付金额验证
- 地址权限检查
- 会话超时管理

## 性能优化

### 数据库优化
- MongoDB 索引优化
- 查询性能监控
- 连接池管理
- 数据分页

### 缓存策略
- Redis 会话缓存
- 餐厅数据缓存
- 查询结果缓存
- 静态资源缓存

### API 优化
- GraphQL 查询优化
- 批量数据加载
- 响应压缩
- 请求去重

## 监控和日志

### 应用监控
- Sentry 错误跟踪
- 性能指标监控
- 实时告警
- 用户行为分析

### 日志管理
- Winston 结构化日志
- 日志轮转
- 错误级别分类
- 调试信息记录

## 开发指南

### 环境设置
1. 安装 Node.js 18+
2. 安装 MongoDB 和 Redis
3. 配置环境变量
4. 运行 `npm install`
5. 启动开发服务器

### 代码规范
- ESLint 代码检查
- Prettier 代码格式化
- Git commit 规范
- 单元测试覆盖

### API 开发
- GraphQL Schema First
- Resolver 模式
- 错误处理标准
- 文档自动生成

## 扩展性设计

### 水平扩展
- 无状态应用设计
- 负载均衡支持
- 数据库分片准备
- 微服务架构兼容

### 功能扩展
- 插件化架构
- 模块化设计
- 配置驱动
- API 版本管理

## 项目结构概览

```
firespoon-api/
├── app.js                 # 应用入口
├── config.js              # 配置管理
├── graphql/               # GraphQL 相关
│   ├── schema/           # Schema 定义
│   └── resolvers/        # Resolver 实现
├── models/               # 数据模型
├── middleware/           # 中间件
├── routes/               # REST 路由
├── whatsapp/             # WhatsApp 集成
├── helpers/              # 工具函数
├── test/                 # 测试文件
└── Doc/                  # 项目文档
```

---

# 详细模块文档

## GraphQL API 模块

### 核心组件

#### Schema 定义 (`graphql/schema/`)
- **base.graphql**: 基础类型和标量定义
- **extra.graphql**: 扩展查询和变更定义
- **各实体类型**: Restaurant, Order, Customer, Food 等

#### Resolvers (`graphql/resolvers/`)

##### 主要 Resolver 模块
- **authResolver**: 用户认证和授权
- **orderResolver**: 订单管理和处理
- **restaurantResolver**: 餐厅信息管理
- **foodResolver**: 菜品管理
- **customerResolvers**: 客户管理
- **addressResolver**: 地址管理
- **brandResolver**: 品牌管理

##### 核心函数调用关系

**订单处理流程**:
```
orderResolver.placeOrder()
├── validateOrderData() - 验证订单数据
├── calculateOrderTotal() - 计算订单总价
├── checkRestaurantAvailability() - 检查餐厅可用性
├── processPayment() - 处理支付
└── createOrderRecord() - 创建订单记录
```

**餐厅查询流程**:
```
restaurantResolver.restaurants()
├── applyLocationFilter() - 应用位置过滤
├── applyAvailabilityFilter() - 应用可用性过滤
├── loadRestaurantData() - 加载餐厅数据
└── transformRestaurantData() - 转换数据格式
```

### 认证和授权

#### JWT 认证流程
```
1. 客户端请求 → isAuthenticated() 中间件
2. 提取 Authorization header
3. 验证 JWT token
4. 解析用户信息 (userId, userType, restaurantId)
5. 设置请求上下文
```

#### WhatsApp 认证流程
```
1. 检查 X-WhatsAppW-Token header
2. processWhatsAppAuth() 处理
3. sessionService.getSessionByToken() 验证
4. 设置 WhatsApp 上下文 (brandref, customerPhone)
```

## 数据模型模块 (`models/`)

### 核心实体模型

#### Restaurant 模型
- **字段**: name, address, categories, options, addons, deliveryBounds
- **索引**: deliveryBounds (2dsphere), keywords
- **关系**: categories[], options[], addons[]

#### Order 模型
- **字段**: customer, restaurant, items, deliveryAddress, paymentStatus
- **状态**: PENDING, CONFIRMED, PREPARING, DISPATCHED, DELIVERED, CANCELLED
- **关系**: customer (ref), restaurant (ref), items[]

#### Customer 模型
- **字段**: phone, name, email, addresses, orderHistory
- **认证**: phone 作为主要标识符
- **关系**: addresses[], orders[]

#### Food 模型
- **字段**: title, description, price, category, restaurant, variations
- **状态**: isActive, inStock
- **关系**: category (ref), restaurant (ref), variations[]

### 数据关系图
```
Brand 1:N Restaurant 1:N Category 1:N Food
  │                     │
  │                     └── 1:N Option
  │                     └── 1:N Addon
  │
  └── 1:N Customer 1:N Order N:1 Restaurant
              │         │
              └── 1:N Address
                        └── N:1 Order (deliveryAddress)
```

## 支付处理模块

### Stripe 集成 (`routes/stripe.js`)

#### 核心函数
- **createPaymentIntent()**: 创建支付意图
- **handleWebhook()**: 处理 Stripe webhook
- **processRefund()**: 处理退款

#### 支付流程
```
1. 客户端请求支付 → createPaymentIntent()
2. 返回 client_secret
3. 客户端确认支付
4. Stripe webhook → handleWebhook()
5. 更新订单状态
```

### PayPal 集成 (`routes/paypal.js`)

#### 核心函数
- **createPayment()**: 创建 PayPal 支付
- **executePayment()**: 执行支付
- **handleIPN()**: 处理即时支付通知

## 工具和辅助模块

### 日志系统 (`helpers/logger.js`)
- **Winston**: 结构化日志记录
- **日志级别**: error, warn, info, debug
- **日志轮转**: 按日期轮转日志文件
- **格式化**: JSON 格式便于分析

### 通知系统 (`helpers/notifications.js`)
- **Firebase**: 推送通知
- **Email**: 邮件通知
- **SMS**: 短信通知 (Twilio)

### 地理位置服务 (`helpers/location.js`)
- **距离计算**: 计算配送距离
- **区域检查**: 检查配送范围
- **地址验证**: 验证地址格式

## 中间件模块 (`middleware/`)

### 认证中间件 (`is-auth.js`)
```javascript
function isAuthenticated(req) {
  // 1. 提取 JWT token
  // 2. 验证 token 有效性
  // 3. 解析用户信息
  // 4. 返回认证状态
}
```

### WhatsApp 认证中间件 (`whatsapp-graphql-auth.js`)
```javascript
async function processWhatsAppAuth(context) {
  // 1. 检查 WhatsApp token
  // 2. 验证会话有效性
  // 3. 设置 WhatsApp 上下文
  // 4. 返回增强的上下文
}
```

## 配置管理

### 环境配置 (`config.js`)
- **数据库连接**: MongoDB 连接字符串
- **Redis 配置**: 缓存和会话存储
- **API 密钥**: 第三方服务密钥
- **CORS 设置**: 跨域请求配置

### 配置分类
```javascript
module.exports = {
  // 数据库配置
  CONNECTION_STRING: process.env.CONNECTION_STRING,
  DB_NAME: process.env.DB_NAME,

  // Redis 配置
  REDIS_URL: process.env.REDIS_URL,

  // 支付配置
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
  PAYPAL_CLIENT_ID: process.env.PAYPAL_CLIENT_ID,

  // WhatsApp 配置
  WS_API_URL: process.env.WS_API_URL,
  WS_CLIENT_ID: process.env.WS_CLIENT_ID,

  // 应用配置
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development'
};
```
