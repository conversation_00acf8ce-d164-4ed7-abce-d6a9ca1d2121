# Firespoon API Testing Framework and Strategy

## 1. Core Testing Philosophy

- **Layered Testing Approach**: Combine unit, integration, and limited end-to-end tests
- **Isolation First**: Use containerization and mocking to isolate test environments
- **Infrastructure as Code**: Automate test environment setup and teardown
- **Maintainability**: Create reusable test utilities and data factories
- **Balance Coverage and Speed**: Optimize for both test coverage and execution time

## 2. Testing Framework Components

### 2.1 Core Testing Stack

| Component | Tool | Purpose |
|-----------|------|---------|
| Test Runner | Jest | Execute tests, assertions, and mocking |
| API Testing | Supertest | Test GraphQL and REST endpoints |
| External Service Mocking | Nock | Mock WhatsApp, Stripe API calls |
| Test Environment | Testcontainers | Isolated MongoDB and Redis instances |
| Data Generation | Faker.js + Custom Factories | Generate test data |

### 2.2 Project Structure

```
firespoon-api/
├── test/
│   ├── config/                # Test configuration
│   │   ├── jest.config.js     # Jest configuration
│   │   ├── globalSetup.js     # Test environment setup
│   │   └── globalTeardown.js  # Test environment cleanup
│   ├── fixtures/              # Static test data
│   │   ├── whatsapp/          # WhatsApp webhook payloads
│   │   └── stripe/            # Stripe webhook payloads
│   ├── factories/             # Test data factories
│   │   ├── userFactory.js
│   │   ├── restaurantFactory.js
│   │   └── orderFactory.js
│   ├── helpers/               # Test utilities
│   │   ├── apiHelper.js       # API request helpers
│   │   ├── dbHelper.js        # Database operations
│   │   ├── redisHelper.js     # Redis operations
│   │   ├── authHelper.js      # Authentication utilities
│   │   ├── mockHelper.js      # External API mocking
│   │   └── waitFor.js         # Async utilities
│   ├── unit/                  # Unit tests
│   │   ├── services/
│   │   ├── models/
│   │   └── utils/
│   ├── integration/           # Integration tests
│   │   ├── graphql/
│   │   ├── rest/
│   │   └── whatsapp/
│   └── e2e/                   # End-to-end tests (limited)
└── jest.setup.js              # Jest global setup
```

## 3. Test Environment Setup

### 3.1 Containerized Test Environment

Using Testcontainers to create isolated test infrastructure:

```javascript
// test/config/globalSetup.js
const { GenericContainer } = require('testcontainers');

module.exports = async () => {
  // Start MongoDB container
  const mongoContainer = await new GenericContainer('mongo:4.4')
    .withExposedPorts(27017)
    .start();
  
  // Start Redis container
  const redisContainer = await new GenericContainer('redis:6')
    .withExposedPorts(6379)
    .start();
  
  // Store connection info in global variables
  global.__MONGO_URI__ = `mongodb://${mongoContainer.getHost()}:${mongoContainer.getMappedPort(27017)}`;
  global.__REDIS_URI__ = `redis://${redisContainer.getHost()}:${redisContainer.getMappedPort(6379)}`;
  
  // Store container references for teardown
  global.__MONGO_CONTAINER__ = mongoContainer;
  global.__REDIS_CONTAINER__ = redisContainer;
};
```

### 3.2 Test Database Helpers

```javascript
// test/helpers/dbHelper.js
const mongoose = require('mongoose');

// Connect to test database
async function connectTestDB() {
  if (mongoose.connection.readyState === 0) {
    await mongoose.connect(global.__MONGO_URI__, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  }
  return mongoose;
}

// Clear specific collections
async function clearCollections(...modelNames) {
  const db = mongoose.connection.db;
  
  for (const modelName of modelNames) {
    const model = mongoose.model(modelName);
    const collectionName = model.collection.name;
    await db.collection(collectionName).deleteMany({});
  }
}

module.exports = {
  connectTestDB,
  clearCollections,
  // Additional helper methods...
};
```

## 4. Test Data Management

### 4.1 Data Factories

```javascript
// test/factories/restaurantFactory.js
const { faker } = require('@faker-js/faker');
const Restaurant = require('../../models/restaurant');

class RestaurantFactory {
  static build(overrides = {}) {
    return {
      name: faker.company.name(),
      description: faker.company.catchPhrase(),
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zipCode: faker.location.zipCode(),
        country: faker.location.country(),
        location: {
          type: 'Point',
          coordinates: [
            parseFloat(faker.location.longitude()),
            parseFloat(faker.location.latitude())
          ]
        }
      },
      contactPhone: faker.phone.number(),
      contactEmail: faker.internet.email(),
      isActive: true,
      ...overrides
    };
  }

  static async create(overrides = {}) {
    const data = this.build(overrides);
    return await Restaurant.create(data);
  }
}

module.exports = RestaurantFactory;
```

## 5. Testing Strategies by Layer

### 5.1 Unit Testing

Focus on testing individual functions, classes, and modules in isolation:

- **Services**: Business logic with mocked dependencies
- **Models**: Mongoose model methods and validations
- **Utilities**: Helper functions and utilities
- **State Machines**: WhatsApp dialog state transitions

### 5.2 Integration Testing

Test interactions between multiple components with real database and Redis:

- **GraphQL API**: Queries and mutations
- **REST Endpoints**: Webhook handlers and payment endpoints
- **WhatsApp Flow**: Dialog state machine with mocked external APIs
- **Database Operations**: Complex queries and transactions

### 5.3 End-to-End Testing (Limited)

Focused on critical user journeys:

- **Order Flow**: Complete order placement and processing
- **WhatsApp Conversation**: Full conversation flow from start to payment
- **Payment Processing**: Order payment and status updates

## 6. WhatsApp Integration Testing

### 6.1 Webhook Testing

```javascript
// test/integration/whatsapp/webhook.test.js
const request = require('supertest');
const app = require('../../../app');
const { generateXMMCSignature } = require('../../helpers/whatsappHelper');
const messageReceivedPayload = require('../../fixtures/whatsapp/message-received.json');

describe('WhatsApp Webhook', () => {
  test('should process incoming message', async () => {
    // Generate valid signature
    const content = JSON.stringify(messageReceivedPayload);
    const signature = generateXMMCSignature(content, process.env.PAYEMOJI_WEBHOOK_SECRET);
    
    // Send webhook request
    const response = await request(app)
      .post('/whatsapp/webhook')
      .set('X-MMC-Signature', signature)
      .set('Content-Type', 'application/json')
      .send(messageReceivedPayload);
    
    // Verify response
    expect(response.status).toBe(202);
    
    // Verify session was created in Redis
    // Verify outbound message was sent (via mocked WhatsApp API)
  });
});
```

### 6.2 Dialog State Machine Testing

```javascript
// test/unit/whatsapp/dialogMachine.test.js
const dialogManager = require('../../../whatsapp/machines/dialog');
const whatsappService = require('../../../whatsapp/services/whatsappService');

// Mock WhatsApp service
jest.mock('../../../whatsapp/services/whatsappService');

describe('Dialog State Machine', () => {
  let dialogService;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create new service instance
    dialogService = interpret(dialogMachine);
    dialogService.start();
  });
  
  afterEach(() => {
    // Stop service
    dialogService.stop();
  });
  
  test('should transition from initial to restaurant selection', () => {
    // Arrange
    const initialContext = {
      session: {
        customerPhone: '+1234567890',
        brandWhatsappId: 'brand-123'
      }
    };
    
    // Act
    dialogService.send({
      type: 'MESSAGE_RECEIVED',
      data: {
        text: 'Hello',
        context: initialContext
      }
    });
    
    // Assert
    expect(dialogService.state.value).toBe('restaurantSelection');
    expect(whatsappService.sendBasicText).toHaveBeenCalled();
  });
});
```

## 7. GraphQL API Testing

```javascript
// test/integration/graphql/orderMutations.test.js
const request = require('supertest');
const app = require('../../../app');
const { generateAuthToken } = require('../../helpers/authHelper');
const { connectTestDB, clearCollections } = require('../../helpers/dbHelper');
const UserFactory = require('../../factories/userFactory');
const RestaurantFactory = require('../../factories/restaurantFactory');

describe('Order Mutations', () => {
  let user, restaurant, authToken;
  
  beforeAll(async () => {
    await connectTestDB();
  });
  
  beforeEach(async () => {
    await clearCollections('User', 'Restaurant', 'Order');
    
    // Create test data
    user = await UserFactory.create();
    restaurant = await RestaurantFactory.create();
    
    // Generate auth token
    authToken = generateAuthToken(user);
  });
  
  test('should create a new order', async () => {
    const orderInput = {
      restaurantId: restaurant._id.toString(),
      orderInput: [
        {
          food: 'food-id',
          variation: 'variation-id',
          quantity: 2,
          addons: []
        }
      ],
      isPickedUp: true,
      instructions: 'Test instructions'
    };
    
    const query = `
      mutation PlaceOrder($input: OrderInput!) {
        placeOrder(orderInput: $input) {
          _id
          status
          orderAmount
        }
      }
    `;
    
    const response = await request(app)
      .post('/graphql')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        query,
        variables: { input: orderInput }
      });
    
    expect(response.status).toBe(200);
    expect(response.body.data.placeOrder).toBeTruthy();
    expect(response.body.data.placeOrder.status).toBe('PENDING');
  });
});
```

## 8. Implementation Plan

1. **Phase 1: Setup Test Infrastructure**
   - Configure Jest with Testcontainers
   - Create core test helpers and utilities
   - Implement data factories

2. **Phase 2: Unit Test Coverage**
   - Test critical service functions
   - Test model validations and methods
   - Test WhatsApp state machine transitions

3. **Phase 3: Integration Test Coverage**
   - Test GraphQL API endpoints
   - Test webhook handlers
   - Test WhatsApp conversation flows

4. **Phase 4: End-to-End Test Coverage**
   - Implement critical user journey tests
   - Test payment processing flows
   - Test order lifecycle management

5. **Phase 5: CI/CD Integration**
   - Configure test runs in CI pipeline
   - Set up test reporting and monitoring
   - Implement test coverage thresholds
