# 环境配置说明

Firespoon API 支持多种运行环境：本地开发环境、测试环境、Render 部署环境和生产环境。每个环境都需要特定的配置。

## 环境类型

### 1. 本地开发环境 (`NODE_ENV=local`)
- **配置文件**: `.env.local`
- **用途**: 本地开发和调试
- **数据库**: 本地 MongoDB 实例或 MongoDB Atlas
- **缓存**: 本地 Redis 实例
- **外部服务**: 测试环境凭据

### 2. 测试环境 (`NODE_ENV=test`)
- **配置文件**: `.env.test`
- **用途**: 自动化测试和 CI/CD
- **数据库**: MongoDB Atlas 测试数据库
- **缓存**: 测试容器化 Redis (Redis 7.2.4)
- **外部服务**: 测试环境凭据

### 3. Render 部署环境 (`NODE_ENV=render`)
- **配置文件**: 无（使用 Render 环境变量）
- **用途**: Render.com 平台部署
- **数据库**: MongoDB Atlas
- **缓存**: Redis Cloud 或 Render Redis
- **外部服务**: 生产或测试环境凭据

### 4. 生产环境 (`NODE_ENV=production`)
- **配置文件**: `.env.prod`
- **用途**: 生产部署
- **数据库**: MongoDB Atlas 生产集群
- **缓存**: Redis Cloud 生产实例
- **外部服务**: 生产环境凭据

## 环境变量验证

应用程序启动时会根据 `NODE_ENV` 验证必需的环境变量。缺少必需变量将导致启动失败。

### 核心必需变量（所有环境）

```bash
# 基础配置
NODE_ENV                       # 运行环境 (local/test/render/production)
LOG_LEVEL                      # 日志级别 (error/warn/info/debug)
SERVER_URL                     # 服务器基础 URL
PORT                          # 服务器端口

# 数据库配置
CONNECTION_STRING             # MongoDB 连接字符串
DB_NAME                       # MongoDB 数据库名称 (可选)

# Redis 配置
REDIS_URL                     # Redis 连接 URL
REDIS_ENABLE_TLS             # Redis TLS 启用 (true/false)
REDIS_PASSWORD               # Redis 密码 (可选)
REDIS_HOST                   # Redis 主机 (可选)
REDIS_PORT                   # Redis 端口 (可选)
```

### Payemoji WhatsApp 集成（必需）

```bash
# Payemoji 认证
PAYEMOJI_CLIENT_ID           # Payemoji 客户端 ID
PAYEMOJI_CLIENT_SECRET       # Payemoji 客户端密钥
PAYEMOJI_WEBHOOK_SECRET      # Webhook 验证密钥
PAYEMOJI_AGENT_ID            # Payemoji 代理 ID

# WhatsApp 服务配置
WS_API_URL                   # WhatsApp API 基础 URL
WS_AUTH_URL                  # WhatsApp 认证 URL
WS_BOUNDARY                  # API 边界标识符
WS_GRANT_TYPE               # OAuth 授权类型
WS_SCOPE                    # OAuth 作用域
WS_B2C_POLICY               # B2C 策略名称
```

### 支付集成（必需）

```bash
# Stripe 配置
STRIPE_CHECKOUT_BASE_URL     # Stripe 结账基础 URL
STRIPE_WEBHOOK_ENDPOINT_SECRET # Stripe Webhook 密钥 (可选)

# PayPal 配置 (可选)
PAYPAL_CLIENT_ID            # PayPal 客户端 ID
PAYPAL_CLIENT_SECRET        # PayPal 客户端密钥
```

### 对话会话配置（必需）

```bash
# 会话管理
DIALOG_SESSION_TOKEN_BYTES   # 会话 token 字节数 (建议: 32)
DIALOG_SESSION_TTL_SECONDS   # 会话 TTL 秒数 (建议: 86400)
SESSION_SECRET              # Express 会话密钥
```

### WhatsApp 模板配置（本地环境必需）

```bash
# 基础模板
WS_TEMPLATE_BASIC_TEXT                    # 基础文本模板
WS_TEMPLATE_QUICK_REPLY_1_BUTTON         # 单按钮快速回复
WS_TEMPLATE_QUICK_REPLY_2_BUTTONS        # 双按钮快速回复
WS_TEMPLATE_QUICK_REPLY_3_BUTTONS        # 三按钮快速回复

# 列表选择模板
WS_TEMPLATE_LIST_PICKER_1_SECTION        # 单节列表选择器
WS_TEMPLATE_LIST_PICKER_2_SECTIONS       # 双节列表选择器
WS_TEMPLATE_LIST_PICKER_3_SECTIONS       # 三节列表选择器

# WhatsApp 模板消息
WS_TEMPLATE_WHATSAPP_1_VARIABLE_NO_IMAGE  # 单变量无图片
WS_TEMPLATE_WHATSAPP_1_VARIABLE_WITH_IMAGE # 单变量有图片
WS_TEMPLATE_WHATSAPP_2_VARIABLES_NO_IMAGE  # 双变量无图片
WS_TEMPLATE_WHATSAPP_2_VARIABLES_WITH_IMAGE # 双变量有图片
WS_TEMPLATE_WHATSAPP_3_VARIABLES_NO_IMAGE  # 三变量无图片
WS_TEMPLATE_WHATSAPP_3_VARIABLES_WITH_IMAGE # 三变量有图片
WS_TEMPLATE_WHATSAPP_4_VARIABLES_NO_IMAGE  # 四变量无图片
WS_TEMPLATE_WHATSAPP_4_VARIABLES_WITH_IMAGE # 四变量有图片
WS_TEMPLATE_WHATSAPP_FLOW_WITH_IMAGE      # 流程模板有图片
WS_TEMPLATE_WHATSAPP_FLOW_FORM           # 流程表单模板
```

### 可选配置变量

```bash
# 日志配置
CONSOLE_LOG_LEVEL            # 控制台日志级别 (可选，默认使用 LOG_LEVEL)
FILE_LOG_LEVEL               # 文件日志级别 (可选，默认使用 LOG_LEVEL)
NODE_DEBUG                   # Node.js 调试模式 (可选)

# 应用 URL 配置
DASHBOARD_URL                # 管理后台 URL
WEB_URL                      # Web 应用 URL
ORDER_DETAIL_WEB_URL         # 订单详情页 URL
RESET_PASSWORD_LINK          # 密码重置链接

# 监控和分析
SENTRY_DSN                   # Sentry 错误监控 DSN
ZAPIER_WEBHOOK_URL           # Zapier Webhook URL

# CORS 配置
CORS_ALLOWED_ORIGINS         # 允许的跨域来源
CORS_ALLOW_CREDENTIALS       # 是否允许凭据 (true/false)
CORS_ALLOWED_METHODS         # 允许的 HTTP 方法
CORS_ALLOWED_HEADERS         # 允许的 HTTP 头

# 货币配置
DEFAULT_CURRENCY             # 默认货币代码 (如: USD)
DEFAULT_CURRENCY_SYMBOL      # 默认货币符号 (如: $)

# 服务 URL 配置
ORDER_MANAGEMENT_URL         # 订单管理服务 URL
ADDRESS_MANAGEMENT_URL       # 地址管理服务 URL
```

## 日志级别配置

Firespoon API 支持为控制台输出和日志文件设置不同的日志级别：

1. `LOG_LEVEL` - 设置默认的日志级别
2. `CONSOLE_LOG_LEVEL` - 设置控制台输出的日志级别（如果未设置，则使用 LOG_LEVEL）
3. `FILE_LOG_LEVEL` - 设置日志文件的日志级别（如果未设置，则使用 LOG_LEVEL）

### 支持的日志级别（从低到高）
- `error` - 仅记录错误
- `warn` - 记录警告和错误
- `info` - 记录信息、警告和错误
- `http` - 记录 HTTP 请求、信息、警告和错误
- `debug` - 记录所有内容，包括调试信息

### 推荐的日志级别配置

| 环境 | LOG_LEVEL | CONSOLE_LOG_LEVEL | FILE_LOG_LEVEL |
|------|-----------|-------------------|----------------|
| 本地开发 | debug | debug | info |
| 测试环境 | info | info | warn |
| Render 环境 | info | info | warn |
| 生产环境 | warn | warn | error |

## 环境配置设置

### 1. 本地开发环境设置

```bash
# 1. 创建本地环境配置文件
cp .env.local.template .env.local

# 2. 编辑配置文件
nano .env.local

# 3. 创建默认环境文件链接
cp .env.local .env

# 4. 启动本地服务
npm run dev
```

### 2. 测试环境设置

```bash
# 1. 创建测试环境配置文件
cp .env.test.template .env.test

# 2. 编辑测试配置
nano .env.test

# 3. 运行测试
NODE_ENV=test npm test
```

### 3. Render 部署设置

Render 环境不使用 `.env` 文件，而是通过 Render 控制台设置环境变量：

1. 登录 Render.com 控制台
2. 选择您的服务
3. 进入 "Environment" 标签页
4. 添加所有必需的环境变量
5. 设置 `NODE_ENV=render`

### 4. 生产环境设置

```bash
# 1. 创建生产环境配置文件
cp .env.prod.template .env.prod

# 2. 使用安全方式编辑生产配置
# 注意：生产凭据应通过安全渠道获取

# 3. 部署到生产环境
NODE_ENV=production npm start
```

## 环境变量验证机制

应用程序在启动时会执行以下验证：

### 1. 环境检测
```javascript
// config.js 中的环境检测逻辑
function loadEnvConfig() {
  const env = process.env.NODE_ENV;

  if (env === 'render') {
    // Render 环境直接使用环境变量
    return;
  }

  // 其他环境加载对应的 .env 文件
  const envFile = `.env.${env}`;
  dotenv.config({ path: envFile });
}
```

### 2. 变量验证
```javascript
// 根据环境验证不同的变量集合
function validateEnvVariables(config) {
  const isRenderEnv = process.env.NODE_ENV === 'render';

  const requiredVars = isRenderEnv
    ? [...coreRequiredVars, ...payemojiRequiredVars, ...stripeRequiredVars]
    : [...coreRequiredVars, ...payemojiRequiredVars, ...whatsappRequiredVars];

  // 验证所有必需变量
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      throw new Error(`Missing required environment variable: ${varName}`);
    }
  }
}
```

## 安全最佳实践

### 1. 凭据管理
- **本地开发**: 使用测试凭据，避免使用生产数据
- **测试环境**: 使用独立的测试服务和数据库
- **生产环境**: 使用强密码和定期轮换的 API 密钥

### 2. 文件安全
```bash
# .gitignore 中应包含
.env
.env.local
.env.test
.env.prod
.env.render
```

### 3. 权限控制
```bash
# 设置环境文件权限
chmod 600 .env.*
```

### 4. 监控和审计
- 定期检查环境变量使用情况
- 监控异常的配置访问
- 记录配置变更历史

## 故障排除

### 常见问题

1. **启动失败 - 缺少环境变量**
   ```
   Error: Missing required environment variable: PAYEMOJI_CLIENT_ID
   ```
   **解决方案**: 检查并设置缺少的环境变量

2. **Redis 连接失败**
   ```
   Redis client error: ECONNREFUSED
   ```
   **解决方案**: 检查 `REDIS_URL` 和 `REDIS_ENABLE_TLS` 配置

3. **MongoDB 连接失败**
   ```
   Error connecting to MongoDB
   ```
   **解决方案**: 验证 `CONNECTION_STRING` 格式和网络连接

4. **WhatsApp API 认证失败**
   ```
   WhatsApp authentication error
   ```
   **解决方案**: 检查 Payemoji 凭据和 API URL 配置

### 调试技巧

1. **启用调试日志**
   ```bash
   LOG_LEVEL=debug npm start
   ```

2. **检查配置加载**
   ```bash
   NODE_DEBUG=config npm start
   ```

3. **验证环境变量**
   ```bash
   node -e "console.log(process.env.NODE_ENV)"
   ```
