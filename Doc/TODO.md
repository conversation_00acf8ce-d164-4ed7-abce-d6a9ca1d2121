# TODO

W24: 
   - Finish Test framework and basic test cases
W25: 
   - Feature: Refund
   - Security improvements
W26: 
   - Operation plan
   - Travel to China
W27:in leave
W28:
   - Payemoji alternative
   - Chinese restaurant menu 
W29:  
   - Menu update process (restauarant app, restaurant website)
   - Multiple restaurant support in on device
W30
  - daily report in restaurant app
  - restuarant app optimization


## WhatsApp: 
- [x] add error handling for message sending
- [x] add hook for message receving
- [ ] alternative to payemoji
- [ ] Keep curent Whatsapp business accounts

## Features
- [ ] process for refund( in restaurant app, full or partial)
   - if reject the order, will refund the customer automatically
   - if partially fulfilled, how to handle the payment
   - if customer cancel the order, how to handle the transaction fee?
   - <PERSON><PERSON> does not refund the original processing fees,  which means the company absorbs the cost of those fee
- [ ] optimize UI of restaurant app: add print of daily summary
- [ ] Check Chinese restaurant menu
- [ ] Menu update process
- [ ] Multiple restaurant support in restaurant app? 
   -  [ ] maximum 5?: use more than 1 app? or same app to handle different restaurants?


## Test
- [x] Test framework: unit
- [x] Test framework: integration
- [ ] Test scripts: unit - ongoing
- [ ] Test scripts: integration - ongoing
- [ ] Test coverage: unit - ongoing
- [ ] Test coverage: integration - ongoing
- [ ] stress test & performance test


## Operation
- [ ] how to monitor the server
- [ ] how to handle log
- [ ] how to handle error
- [ ] how to handle performanc issue
- [ ] how to backup data

## Security
- [ ] Verify the authentication & right of the webhook request and whatsapp request
- [ ] Verify the authentication & access right of the order modification request from app
- [ ] Prevent replay attack

## legal
- [ ] GDPR, and get customer consent