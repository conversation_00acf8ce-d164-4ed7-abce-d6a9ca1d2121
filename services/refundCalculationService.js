const { REFUND_REASON } = require('../helpers/enum');

class RefundCalculationService {
  /**
   * 计算退款金额和手续费承担
   * @param {Object} order - 订单对象
   * @param {Number} requestAmount - 请求退款金额
   * @param {String} reason - 退款原因
   * @returns {Object} 退款计算结果
   */
  calculateRefundAmount(order, requestAmount, reason) {
    const isMerchantFault = this.isMerchantFault(reason);
    const transactionFee = this.getTransactionFee(order);
    
    if (isMerchantFault) {
      // 商家承担手续费，客户收到全额退款
      return {
        customerReceives: requestAmount,
        merchantPays: requestAmount + transactionFee,
        feeBearer: 'MERCHANT',
        transactionFee: transactionFee,
        finalRefundAmount: requestAmount
      };
    } else {
      // 客户承担手续费，从退款中扣除
      const proportionalFee = this.calculateProportionalFee(order, requestAmount);
      const finalAmount = Math.max(0, requestAmount - proportionalFee);
      
      return {
        customerReceives: finalAmount,
        merchantPays: finalAmount,
        feeBearer: 'CUSTOMER',
        transactionFee: proportionalFee,
        finalRefundAmount: finalAmount
      };
    }
  }

  /**
   * 判断是否为商家原因
   * @param {String} reason - 退款原因
   * @returns {Boolean}
   */
  isMerchantFault(reason) {
    return reason.startsWith('MERCHANT_');
  }

  /**
   * 获取订单的交易手续费
   * @param {Object} order - 订单对象
   * @returns {Number} 手续费金额
   */
  getTransactionFee(order) {
    // Stripe手续费计算：2.9% + 0.30 EUR
    const feeRate = 0.029; // 2.9%
    const fixedFee = 0.30; // 0.30 EUR
    
    return (order.orderAmount * feeRate) + fixedFee;
  }

  /**
   * 计算按比例分摊的手续费
   * @param {Object} order - 订单对象
   * @param {Number} refundAmount - 退款金额
   * @returns {Number} 按比例分摊的手续费
   */
  calculateProportionalFee(order, refundAmount) {
    const totalFee = this.getTransactionFee(order);
    const refundRatio = refundAmount / order.orderAmount;
    
    // 按退款比例分摊手续费
    return totalFee * refundRatio;
  }

  /**
   * 验证退款金额
   * @param {Object} order - 订单对象
   * @param {Number} requestAmount - 请求退款金额
   * @returns {Object} 验证结果
   */
  validateRefundAmount(order, requestAmount) {
    const errors = [];
    
    // 检查退款金额是否为正数
    if (requestAmount <= 0) {
      errors.push('Refund amount must be greater than 0');
    }
    
    // 检查是否超过订单金额
    if (requestAmount > order.orderAmount) {
      errors.push('Refund amount cannot exceed order amount');
    }
    
    // 检查是否超过可退款余额
    const availableAmount = order.orderAmount - (order.totalRefunded || 0);
    if (requestAmount > availableAmount) {
      errors.push(`Refund amount cannot exceed available amount: ${availableAmount}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      availableAmount
    };
  }

  /**
   * 计算全额退款金额
   * @param {Object} order - 订单对象
   * @param {String} reason - 退款原因
   * @returns {Object} 全额退款计算结果
   */
  calculateFullRefund(order, reason) {
    const availableAmount = order.orderAmount - (order.totalRefunded || 0);
    return this.calculateRefundAmount(order, availableAmount, reason);
  }

  /**
   * 获取退款预览信息（用于前端显示）
   * @param {Object} order - 订单对象
   * @param {Number} requestAmount - 请求退款金额
   * @param {String} reason - 退款原因
   * @returns {Object} 预览信息
   */
  getRefundPreview(order, requestAmount, reason) {
    const validation = this.validateRefundAmount(order, requestAmount);
    
    if (!validation.isValid) {
      return {
        isValid: false,
        errors: validation.errors
      };
    }
    
    const calculation = this.calculateRefundAmount(order, requestAmount, reason);
    const isMerchantFault = this.isMerchantFault(reason);
    
    return {
      isValid: true,
      calculation,
      preview: {
        customerReceives: calculation.customerReceives,
        merchantPays: calculation.merchantPays,
        feeBearer: calculation.feeBearer,
        message: isMerchantFault 
          ? `您将承担交易手续费。顾客将收到 €${calculation.customerReceives.toFixed(2)}。您的账户将扣除 €${calculation.merchantPays.toFixed(2)}。`
          : `交易手续费将从退款中扣除。顾客将收到 €${calculation.customerReceives.toFixed(2)}。您的账户将扣除 €${calculation.merchantPays.toFixed(2)}。`
      }
    };
  }
}

module.exports = new RefundCalculationService();
