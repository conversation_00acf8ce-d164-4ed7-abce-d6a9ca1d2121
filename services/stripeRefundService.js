const stripeObj = require('stripe');
const config = require('../config');
const logger = require('../helpers/logger');

class StripeRefundService {
  constructor() {
    this.stripe = null;
    this.initializeStripe();
  }

  /**
   * 初始化Stripe
   */
  async initializeStripe() {
    try {
      // 使用与routes/stripe.js相同的配置
      this.stripe = new stripeObj(
        'sk_test_51RC9DgRw6scKj0ZOTdVY2bJAekzcYb3173ewXVnOwfaxceofKBRIK6cpDKNTquKMgM3xEjwg87AgZ6JXS0HEGuSU00BuOEDr1z',
        {
          maxNetworkRetries: 2,
          timeout: 30000
        }
      );
      logger.info('Stripe refund service initialized');
    } catch (error) {
      logger.error('Failed to initialize Stripe refund service', { error: error.message });
      throw error;
    }
  }

  /**
   * 创建退款
   * @param {String} paymentIntentId - 支付意图ID
   * @param {Number} amount - 退款金额（以分为单位）
   * @param {String} reason - 退款原因
   * @param {Object} metadata - 元数据
   * @returns {Object} Stripe退款对象
   */
  async createRefund(paymentIntentId, amount, reason = 'requested_by_customer', metadata = {}) {
    try {
      if (!this.stripe) {
        await this.initializeStripe();
      }

      // 转换金额为分（Stripe要求）
      const amountInCents = Math.round(amount * 100);

      const refundParams = {
        payment_intent: paymentIntentId,
        amount: amountInCents,
        reason,
        metadata: {
          ...metadata,
          created_by: 'firespoon_api',
          timestamp: new Date().toISOString()
        }
      };

      logger.info('Creating Stripe refund', {
        paymentIntentId,
        amount: amountInCents,
        reason
      });

      const refund = await this.stripe.refunds.create(refundParams);

      logger.info('Stripe refund created successfully', {
        refundId: refund.id,
        paymentIntentId,
        amount: amountInCents,
        status: refund.status
      });

      return refund;

    } catch (error) {
      logger.error('Failed to create Stripe refund', {
        paymentIntentId,
        amount,
        error: error.message,
        errorType: error.type,
        errorCode: error.code
      });

      // 处理常见的Stripe错误
      if (error.type === 'StripeCardError') {
        throw new Error(`Card error: ${error.message}`);
      } else if (error.type === 'StripeInvalidRequestError') {
        throw new Error(`Invalid request: ${error.message}`);
      } else if (error.type === 'StripeAPIError') {
        throw new Error(`Stripe API error: ${error.message}`);
      } else if (error.type === 'StripeConnectionError') {
        throw new Error(`Network error: ${error.message}`);
      } else if (error.type === 'StripeAuthenticationError') {
        throw new Error(`Authentication error: ${error.message}`);
      } else {
        throw new Error(`Refund failed: ${error.message}`);
      }
    }
  }

  /**
   * 获取退款状态
   * @param {String} refundId - Stripe退款ID
   * @returns {Object} 退款信息
   */
  async getRefundStatus(refundId) {
    try {
      if (!this.stripe) {
        await this.initializeStripe();
      }

      const refund = await this.stripe.refunds.retrieve(refundId);
      
      logger.debug('Retrieved Stripe refund status', {
        refundId,
        status: refund.status,
        amount: refund.amount
      });

      return refund;

    } catch (error) {
      logger.error('Failed to retrieve Stripe refund status', {
        refundId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 取消退款（仅在pending状态下可用）
   * @param {String} refundId - Stripe退款ID
   * @returns {Object} 取消结果
   */
  async cancelRefund(refundId) {
    try {
      if (!this.stripe) {
        await this.initializeStripe();
      }

      const refund = await this.stripe.refunds.cancel(refundId);
      
      logger.info('Stripe refund cancelled', {
        refundId,
        status: refund.status
      });

      return refund;

    } catch (error) {
      logger.error('Failed to cancel Stripe refund', {
        refundId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 列出支付意图的所有退款
   * @param {String} paymentIntentId - 支付意图ID
   * @param {Number} limit - 限制数量
   * @returns {Array} 退款列表
   */
  async listRefunds(paymentIntentId, limit = 10) {
    try {
      if (!this.stripe) {
        await this.initializeStripe();
      }

      const refunds = await this.stripe.refunds.list({
        payment_intent: paymentIntentId,
        limit
      });

      logger.debug('Retrieved Stripe refunds list', {
        paymentIntentId,
        count: refunds.data.length
      });

      return refunds.data;

    } catch (error) {
      logger.error('Failed to list Stripe refunds', {
        paymentIntentId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 验证退款金额是否有效
   * @param {String} paymentIntentId - 支付意图ID
   * @param {Number} refundAmount - 退款金额
   * @returns {Object} 验证结果
   */
  async validateRefundAmount(paymentIntentId, refundAmount) {
    try {
      if (!this.stripe) {
        await this.initializeStripe();
      }

      // 获取支付意图信息
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      // 获取已有退款
      const existingRefunds = await this.listRefunds(paymentIntentId);
      
      // 计算已退款总额
      const totalRefunded = existingRefunds.reduce((sum, refund) => {
        return sum + (refund.status === 'succeeded' ? refund.amount : 0);
      }, 0);

      // 计算可退款余额
      const availableAmount = paymentIntent.amount - totalRefunded;
      const requestedAmountInCents = Math.round(refundAmount * 100);

      const isValid = requestedAmountInCents <= availableAmount;

      return {
        isValid,
        availableAmount: availableAmount / 100,
        requestedAmount: refundAmount,
        totalRefunded: totalRefunded / 100,
        originalAmount: paymentIntent.amount / 100
      };

    } catch (error) {
      logger.error('Failed to validate refund amount', {
        paymentIntentId,
        refundAmount,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new StripeRefundService();
