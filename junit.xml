<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="50" failures="0" errors="0" time="26.383">
  <testsuite name="Order GraphQL Mutations" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:25" time="8.172" tests="4">
    <testcase classname="Order GraphQL Mutations should create a new order" name="Order GraphQL Mutations should create a new order" time="0.436">
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order with invalid restaurant ID" name="Order GraphQL Mutations should fail to create order with invalid restaurant ID" time="0.207">
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order without authentication" name="Order GraphQL Mutations should fail to create order without authentication" time="0.227">
    </testcase>
    <testcase classname="Order GraphQL Mutations should update order status" name="Order GraphQL Mutations should update order status" time="0.2">
    </testcase>
  </testsuite>
  <testsuite name="GraphQL Queries Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:33" time="2.569" tests="2">
    <testcase classname="GraphQL Queries Integration Tests GraphQL Schema Introspection should respond to GraphQL introspection query" name="GraphQL Queries Integration Tests GraphQL Schema Introspection should respond to GraphQL introspection query" time="0.04">
    </testcase>
    <testcase classname="GraphQL Queries Integration Tests GraphQL Schema Introspection should have Restaurant type in schema" name="GraphQL Queries Integration Tests GraphQL Schema Introspection should have Restaurant type in schema" time="0.027">
    </testcase>
  </testsuite>
  <testsuite name="WhatsApp Webhook Integration" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:36" time="2.215" tests="3">
    <testcase classname="WhatsApp Webhook Integration should process incoming message and create session" name="WhatsApp Webhook Integration should process incoming message and create session" time="0.85">
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should reject webhook with invalid signature" name="WhatsApp Webhook Integration should reject webhook with invalid signature" time="0.008">
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should handle malformed webhook payload" name="WhatsApp Webhook Integration should handle malformed webhook payload" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:38" time="1.897" tests="6">
    <testcase classname="Order GraphQL API Integration Tests Order Schema Types should have Order type in schema" name="Order GraphQL API Integration Tests Order Schema Types should have Order type in schema" time="0.02">
    </testcase>
    <testcase classname="Order GraphQL API Integration Tests Order Schema Types should have OrderStatus enum in schema" name="Order GraphQL API Integration Tests Order Schema Types should have OrderStatus enum in schema" time="0.009">
    </testcase>
    <testcase classname="Order GraphQL API Integration Tests Order Input Types should have OrderInput type in schema" name="Order GraphQL API Integration Tests Order Input Types should have OrderInput type in schema" time="0.011">
    </testcase>
    <testcase classname="Order GraphQL API Integration Tests Order Input Types should validate GraphQL order operations" name="Order GraphQL API Integration Tests Order Input Types should validate GraphQL order operations" time="0.017">
    </testcase>
    <testcase classname="Order GraphQL API Integration Tests GraphQL Error Handling should handle malformed order queries" name="Order GraphQL API Integration Tests GraphQL Error Handling should handle malformed order queries" time="0.025">
    </testcase>
    <testcase classname="Order GraphQL API Integration Tests GraphQL Error Handling should validate required arguments" name="Order GraphQL API Integration Tests GraphQL Error Handling should validate required arguments" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Restaurant GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:40" time="1.237" tests="9">
    <testcase classname="Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint" name="Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint" time="0.02">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries" name="Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries" time="0.021">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema" name="Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema" time="0.012">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries" time="0.009">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax" time="0.01">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries" time="0.005">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection" name="Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection" time="0.006">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should list available queries" name="Restaurant GraphQL API Integration Tests Schema Introspection should list available queries" time="0.005">
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations" name="Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations" time="0.007">
    </testcase>
  </testsuite>
  <testsuite name="Order Management Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:41" time="1.07" tests="2">
    <testcase classname="Order Management Integration Tests Order GraphQL Schema Tests should have order query in schema" name="Order Management Integration Tests Order GraphQL Schema Tests should have order query in schema" time="0.019">
    </testcase>
    <testcase classname="Order Management Integration Tests Order GraphQL Schema Tests should have orders query in schema" name="Order Management Integration Tests Order GraphQL Schema Tests should have orders query in schema" time="0.013">
    </testcase>
  </testsuite>
  <testsuite name="Payment System Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:42" time="1.327" tests="1">
    <testcase classname="Payment System Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" name="Payment System Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" time="0.022">
    </testcase>
  </testsuite>
  <testsuite name="Order State Machine Integration Tests (GraphQL)" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:44" time="1.212" tests="3">
    <testcase classname="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have updateOrderStatus mutation in schema" name="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have updateOrderStatus mutation in schema" time="0.023">
    </testcase>
    <testcase classname="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have Order type with orderStatus field in schema" name="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have Order type with orderStatus field in schema" time="0.015">
    </testcase>
    <testcase classname="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have order status enum values in schema" name="Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have order status enum values in schema" time="0.011">
    </testcase>
  </testsuite>
  <testsuite name="PayPal Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:45" time="1.296" tests="2">
    <testcase classname="PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" name="PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" time="0.032">
    </testcase>
    <testcase classname="PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have query types in schema" name="PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have query types in schema" time="0.036">
    </testcase>
  </testsuite>
  <testsuite name="Order Notifications Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:46" time="1.159" tests="2">
    <testcase classname="Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have order subscription in schema" name="Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have order subscription in schema" time="0.015">
    </testcase>
    <testcase classname="Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have mutation types in schema" name="Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have mutation types in schema" time="0.013">
    </testcase>
  </testsuite>
  <testsuite name="Customer GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:47" time="1.186" tests="5">
    <testcase classname="Customer GraphQL API Integration Tests Customer Schema Types should have Customer type in schema" name="Customer GraphQL API Integration Tests Customer Schema Types should have Customer type in schema" time="0.017">
    </testcase>
    <testcase classname="Customer GraphQL API Integration Tests Customer Schema Types should have Address type in schema" name="Customer GraphQL API Integration Tests Customer Schema Types should have Address type in schema" time="0.011">
    </testcase>
    <testcase classname="Customer GraphQL API Integration Tests Customer Input Types should have AddressInput type in schema" name="Customer GraphQL API Integration Tests Customer Input Types should have AddressInput type in schema" time="0.011">
    </testcase>
    <testcase classname="Customer GraphQL API Integration Tests Customer Operations should validate customer-related mutations exist" name="Customer GraphQL API Integration Tests Customer Operations should validate customer-related mutations exist" time="0.015">
    </testcase>
    <testcase classname="Customer GraphQL API Integration Tests Customer Operations should handle GraphQL validation errors" name="Customer GraphQL API Integration Tests Customer Operations should handle GraphQL validation errors" time="0.016">
    </testcase>
  </testsuite>
  <testsuite name="Stripe Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:49" time="1.357" tests="2">
    <testcase classname="Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" name="Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema" time="0.021">
    </testcase>
    <testcase classname="Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have mutation types in schema" name="Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have mutation types in schema" time="0.014">
    </testcase>
  </testsuite>
  <testsuite name="Real Stripe Payment Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-16T14:24:50" time="0.953" tests="9">
    <testcase classname="Real Stripe Payment Integration Tests Real Payment Intent Flow should create real payment intent with Stripe" name="Real Stripe Payment Integration Tests Real Payment Intent Flow should create real payment intent with Stripe" time="0.001">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Payment Intent Flow should retrieve real payment intent status" name="Real Stripe Payment Integration Tests Real Payment Intent Flow should retrieve real payment intent status" time="0">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Payment Intent Flow should handle payment with test card" name="Real Stripe Payment Integration Tests Real Payment Intent Flow should handle payment with test card" time="0.001">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Payment Intent Flow should handle declined card" name="Real Stripe Payment Integration Tests Real Payment Intent Flow should handle declined card" time="0">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Customer Management should create real Stripe customer" name="Real Stripe Payment Integration Tests Real Customer Management should create real Stripe customer" time="0.001">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Customer Management should retrieve real Stripe customer" name="Real Stripe Payment Integration Tests Real Customer Management should retrieve real Stripe customer" time="0">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Real Webhook Processing should process real webhook signature validation" name="Real Stripe Payment Integration Tests Real Webhook Processing should process real webhook signature validation" time="0">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid amount" name="Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid amount" time="0.001">
    </testcase>
    <testcase classname="Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid currency" name="Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid currency" time="0">
    </testcase>
  </testsuite>
</testsuites>