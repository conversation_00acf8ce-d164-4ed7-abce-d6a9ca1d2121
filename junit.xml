<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="5" failures="2" errors="0" time="2.644">
  <testsuite name="Order Model - refundStatus Simple Tests" errors="0" failures="2" skipped="0" timestamp="2025-06-18T19:50:07" time="2.035" tests="5">
    <testcase classname="Order Model - refundStatus Simple Tests should verify refundStatus field exists in schema" name="Order Model - refundStatus Simple Tests should verify refundStatus field exists in schema" time="0.007">
    </testcase>
    <testcase classname="Order Model - refundStatus Simple Tests should create order with minimal data and verify refundStatus default" name="Order Model - refundStatus Simple Tests should create order with minimal data and verify refundStatus default" time="0.015">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/subdocument.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/ArraySubdocument.js:8:21)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$__set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1666:26)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1495:10)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at _callee3$ (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js:89:21)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Order Model - refundStatus Simple Tests should verify refundStatus can be set to valid enum values" name="Order Model - refundStatus Simple Tests should verify refundStatus can be set to valid enum values" time="0.005">
    </testcase>
    <testcase classname="Order Model - refundStatus Simple Tests should verify Order model has all refund-related fields" name="Order Model - refundStatus Simple Tests should verify Order model has all refund-related fields" time="0.004">
    </testcase>
    <testcase classname="Order Model - refundStatus Simple Tests should demonstrate refundStatus functionality without complex ObjectIds" name="Order Model - refundStatus Simple Tests should demonstrate refundStatus functionality without complex ObjectIds" time="0.023">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/subdocument.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/ArraySubdocument.js:8:21)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$__set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1666:26)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1495:10)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js:184:21)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
</testsuites>