# Firespoon API Test Environment Configuration
# Copy this file to .env.test and fill in your values

# Node environment
NODE_ENV=test

# Database Configuration
# MongoDB 7.0 和 Redis 7.2.4 通过 testcontainers 自动管理
# 无需手动配置数据库连接字符串

# JWT Secret for authentication
JWT_SECRET=test-jwt-secret

# WhatsApp API Configuration
WS_API_URL=https://test-api.example.com
WS_AUTH_URL=https://test-auth.example.com
WS_BOUNDARY=test-boundary

# Payemoji credentials
PAYEMOJI_CLIENT_ID=test-client-id
PAYEMOJI_CLIENT_SECRET=test-client-secret
PAYEMOJI_WEBHOOK_SECRET=test-webhook-secret
PAYEMOJI_AGENT_ID=test-agent-id

# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret

# Logging
LOG_LEVEL=debug
