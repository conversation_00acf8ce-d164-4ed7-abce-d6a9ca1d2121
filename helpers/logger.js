const winston = require('winston');

// 定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// 获取控制台日志级别
const consoleLevel = () => {
  const level = process.env.CONSOLE_LOG_LEVEL || process.env.LOG_LEVEL || 'info';
  console.log('Console log level:', level);
  return level;
};

// 获取文件日志级别
const fileLevel = () => {
  const level = process.env.FILE_LOG_LEVEL || process.env.LOG_LEVEL || 'warn';
  console.log('File log level:', level);
  return level;
};

// 定义日志颜色
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// 添加颜色
winston.addColors(colors);

// 定义日志格式
const baseFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.printf(info => {
    let logMessage = `${info.timestamp} ${info.level}: ${info.message}`;

    const metadata = { ...info };
    delete metadata.timestamp;
    delete metadata.level;
    delete metadata.message;
    delete metadata.splat;
    // 限制大型对象的大小
    const safeStringify = obj => {
      const seen = new Set();
      return JSON.stringify(
        obj,
        (key, value) => {
          // 处理循环引用
          if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
              return '[Circular]';
            }
            seen.add(value);
          }

          // 对于字符串，如果太长就截断
          if (typeof value === 'string' && value.length > 1024) {
            return value.substring(0, 1024) + '... [truncated]';
          }

          // 对于数组，如果太长就截断
          if (Array.isArray(value) && value.length > 20) {
            return [...value.slice(0, 20), `... and ${value.length - 20} more items`];
          }

          return value;
        },
        2
      );
    };

    // 如果有剩余的元数据，则格式化并添加到日志中
    if (Object.keys(metadata).length > 0) {
      try {
        logMessage += `\n${safeStringify(metadata)}`;
      } catch (error) {
        logMessage += `\n[Error stringifying metadata: ${error.message}]`;
      }
    }

    return logMessage;
  })
);

// 定义输出目标
const transports = [
  // 控制台输出 - 使用彩色基本格式
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize({ all: true }),
      baseFormat // 使用相同的基本格式
    ),
    level: consoleLevel()
  }),
  // 错误日志文件
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: baseFormat // 使用相同的基本格式
  }),
  // 所有日志文件
  new winston.transports.File({
    filename: 'logs/all.log',
    level: fileLevel(),
    format: baseFormat // 使用相同的基本格式
  })
];

// 创建logger实例，不再需要全局format
const logger = winston.createLogger({
  levels,
  transports
});

module.exports = logger;
