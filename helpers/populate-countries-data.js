const fs = require('fs');
const path = require('path');
const Country = require('../models/country');

/**
 * 检查是否已插入国家数据
 * @returns {Promise<boolean>} 是否已插入数据
 */
const hasCountriesInserted = async () => {
  try {
    return Boolean(await Country.countDocuments());
  } catch (error) {
    console.error('Error checking if countries exist:', error);
    throw error;
  }
};

/**
 * 从JSON文件加载并插入国家数据到数据库
 * @returns {Promise<boolean>} 操作是否成功
 */
const populateCountries = async () => {
  try {
    // 检查是否已插入数据
    if (await hasCountriesInserted()) {
      console.log('Countries already inserted, skipping...');
      return true;
    }

    const directoryPath = path.join(__dirname, '../seed/countries+cities.json');
    
    // 检查文件是否存在
    if (!fs.existsSync(directoryPath)) {
      throw new Error(`Countries data file not found: ${directoryPath}`);
    }

    // 使用异步读取
    const rawData = await fs.promises.readFile(directoryPath, 'utf8');
    const countriesData = JSON.parse(rawData);
    
    // 验证数据格式
    if (!Array.isArray(countriesData)) {
      throw new Error('Countries data should be an array');
    }

    // 检查数据是否为空
    if (countriesData.length === 0) {
      console.warn('No countries data to insert');
      return false;
    }

    await Country.insertMany(countriesData);
    console.log(`Successfully inserted ${countriesData.length} countries`);
    return true;
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      console.error('Invalid JSON format in countries data file:', error.message);
    } else {
      console.error('Error populating countries data:', error.message);
    }
    throw error; // 重新抛出错误，让调用方处理
  }
};

module.exports = populateCountries;
