// TODO: make it either an object or named variables, i think object is better option
// e.g object: {PENDING:'PENDING',PAID:'PAID'}
// e.g variables: const PENDING='PENDING'
//               const PAID='PAID'
// all of them, except months
exports.payment_status = ['PENDING', 'PAID'];
exports.size = ['SMALL', 'MEDIUM', 'LARGE'];
exports.payment_method = ['COD', 'PAYPAL', 'STRIPE'];
exports.months = ['Jan', 'Feb', 'Mar', 'April', 'May', 'Jun', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

exports.days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
exports.WITHDRAW_REQUEST_STATUS = {
  REQUESTED: 'REQUESTED',
  TRANSFERRED: 'TRANSFERRED',
  CANCELLED: 'CANCELLED'
};

exports.SHOP_TYPE = {
  GROCERY: 'grocery',
  RESTAURANT: 'restaurant'
};
/* replace this by ORDER_STATUS, left it as reference for now
exports.order_status = [
  'PENDING', // 0
  'ACCEPTED', // 1
  'PICKED', // 2
  'DELIVERED', // 3
  'CANCELLED', // 4
  'COMPLETED', // 5,
  'ASSIGNED' // 6
] */
exports.ORDER_STATUS = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  PICKED: 'PICKED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  COMPLETED: 'COMPLETED',
  ASSIGNED: 'ASSIGNED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  REFUNDED: 'REFUNDED'
};

exports.BANNER_ACTIONS = {
  NAVIGATE: 'navigate',
  OPEN_MODAL: 'openModal'
};

// 退款相关枚举
exports.REFUND_REASON = {
  MERCHANT_OUT_OF_STOCK: 'MERCHANT_OUT_OF_STOCK',
  MERCHANT_CANNOT_DELIVER: 'MERCHANT_CANNOT_DELIVER',
  MERCHANT_OTHER: 'MERCHANT_OTHER',
  CUSTOMER_CANCELLED: 'CUSTOMER_CANCELLED'
};

exports.REFUND_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCEEDED: 'SUCCEEDED',
  FAILED: 'FAILED'
};

exports.REFUND_TYPE = {
  FULL: 'FULL',
  PARTIAL: 'PARTIAL'
};

exports.getThirtyDaysAgo = () => {
  return new Date(new Date() - 30 * 24 * 60 * 60 * 1000);
};
