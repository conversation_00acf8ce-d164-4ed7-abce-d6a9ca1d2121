const Refund = require('../../models/refund');
const Order = require('../../models/order');
const refundService = require('../../services/refundService');
const refundCalculationService = require('../../services/refundCalculationService');
const { REFUND_TYPE } = require('../../helpers/enum');
const logger = require('../../helpers/logger');

const refundResolvers = {
  Query: {
    /**
     * 查询单个退款记录
     */
    getRefund: async (_, args, { req }) => {
      try {
        if (!req.restaurantId) {
          throw new Error('Unauthenticated!');
        }

        const refund = await refundService.getRefund(args.refundId);
        if (!refund) {
          throw new Error('Refund not found');
        }

        // 验证权限：只能查询自己餐厅的退款记录
        const order = await Order.findById(refund.originalOrderId);
        if (!order || order.restaurantId !== req.restaurantId) {
          throw new Error('Access denied');
        }

        return refund;
      } catch (error) {
        logger.error('Error in getRefund', { 
          refundId: args.refundId, 
          error: error.message 
        });
        throw error;
      }
    },

    /**
     * 查询订单的所有退款记录
     */
    getOrderRefunds: async (_, args, { req }) => {
      try {
        if (!req.restaurantId) {
          throw new Error('Unauthenticated!');
        }

        // 验证订单权限
        const order = await Order.findById(args.orderId);
        if (!order || order.restaurantId !== req.restaurantId) {
          throw new Error('Order not found or access denied');
        }

        const refunds = await refundService.getOrderRefunds(order.orderId);
        return refunds;
      } catch (error) {
        logger.error('Error in getOrderRefunds', { 
          orderId: args.orderId, 
          error: error.message 
        });
        throw error;
      }
    }
  },

  Mutation: {
    /**
     * 部分退款
     */
    refundOrder: async (_, args, { req }) => {
      try {
        if (!req.restaurantId) {
          throw new Error('Unauthenticated!');
        }

        const { _id: orderId, amount, reason, reasonText } = args;

        // 验证订单权限
        const order = await Order.findById(orderId);
        if (!order || order.restaurantId !== req.restaurantId) {
          throw new Error('Order not found or access denied');
        }

        logger.info('Processing partial refund request', {
          orderId,
          amount,
          reason,
          reasonText,
          restaurantId: req.restaurantId
        });

        // 发起部分退款
        const result = await refundService.initiateRefund(
          orderId,
          amount,
          reason,
          reasonText,
          REFUND_TYPE.PARTIAL,
          req.restaurantId
        );

        return {
          success: result.success,
          refund: result.refund,
          order: result.order,
          message: 'Partial refund initiated successfully'
        };

      } catch (error) {
        logger.error('Error in refundOrder', {
          orderId: args._id,
          amount: args.amount,
          reason: args.reason,
          error: error.message
        });
        
        return {
          success: false,
          refund: null,
          order: null,
          message: error.message
        };
      }
    }
  },

  // 类型解析器
  Refund: {
    // 如果需要解析关联字段，可以在这里添加
    originalOrderId: async (parent) => {
      return await Order.findById(parent.originalOrderId);
    }
  }
};

module.exports = refundResolvers;
