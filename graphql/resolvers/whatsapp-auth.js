/**
 * WhatsApp认证高阶函数
 * 用于包装需要WhatsApp认证的resolver
 */
const { createWhatsAppError, ERROR_TYPES } = require('../../middleware/whatsapp-graphql-auth');
const logger = require('../../helpers/logger');

/**
 * 高阶函数，用于包装需要WhatsApp认证的resolver
 * @param {Function} resolver - 原始resolver函数
 * @returns {Function} 包装后的resolver函数
 */
const withWhatsAppAuth = resolver => {
  return async (parent, args, context, info) => {
    // 如果已经提供了customerId参数，则使用它（内部调用）
    if (args.customerId) {
      logger.debug(`Internal request with customerId: ${args.customerId} for ${info.fieldName}`);
      return resolver(parent, args, context, info);
    }

    // 否则，要求WhatsApp认证
    // 检查是否是WhatsApp请求
    if (!context.whatsAppAuth) {
      // 如果不是WhatsApp请求，抛出错误
      logger.debug(`Request to ${info.fieldName} without WhatsApp authentication`);
      throw createWhatsAppError(ERROR_TYPES.MISSING_TOKEN);
    }

    // 检查是否有WhatsApp错误
    if (context.whatsAppErrorType) {
      throw createWhatsAppError(context.whatsAppErrorType);
    } else if (context.whatsAppError) {
      // 向后兼容，处理旧格式的错误
      logger.warn('Deprecated: Using whatsAppError instead of whatsAppErrorType');
      throw createWhatsAppError(ERROR_TYPES.EXPIRED_SESSION);
    }

    // 如果没有customerId，抛出错误
    if (!context.whatsAppCustomerId) {
      throw createWhatsAppError(ERROR_TYPES.MISSING_TOKEN);
    }

    // 将customerId添加到args中
    args.customerId = context.whatsAppCustomerId;

    logger.debug(`WhatsApp request: Using customerId ${context.whatsAppCustomerId} for ${info.fieldName}`);

    // 调用原始resolver
    return resolver(parent, args, context, info);
  };
};

module.exports = {
  withWhatsAppAuth
};
