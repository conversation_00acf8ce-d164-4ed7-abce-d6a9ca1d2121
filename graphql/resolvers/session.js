const sessionService = require('../../whatsapp/services/sessionService');
const logger = require('../../helpers/logger');
const { createWhatsAppError, ERROR_TYPES } = require('../../middleware/whatsapp-graphql-auth');
const SessionIdGenerator = require('../../whatsapp/utils/sessionIdGenerator');

/**
 * 直接获取会话数据的解析器
 * 不使用withWhatsAppAuth，而是手动实现类似的逻辑
 * 这样可以使用token参数而不是从上下文中获取
 */
const getSessionByTokenResolver = async (_, { token }, context) => {
  try {
    // 验证token格式
    if (!SessionIdGenerator.validateToken(token)) {
      logger.debug(`Invalid token format: ${token}`);
      throw createWhatsAppError(ERROR_TYPES.INVALID_TOKEN);
    }

    // 获取会话数据
    const session = await sessionService.getSessionByToken(token);
    if (!session) {
      logger.debug(`No session found for token: ${token}`);
      throw createWhatsAppError(ERROR_TYPES.EXPIRED_SESSION);
    }

    // 检查currentOrder是否存在
    if (!session.context || !session.context.currentOrder.restaurantId || !session.context.currentOrder.customerId) {
      logger.debug(`No current order in session for token: ${token}`);
      // 直接返回null，表示没有订单
      return null;
    }

    // 返回currentOrder数据
    return session.context.currentOrder;
  } catch (error) {
    // 如果是已知的WhatsApp错误，直接抛出
    if (error.extensions && error.extensions.code && error.extensions.code.startsWith('WHATSAPP_')) {
      throw error;
    }

    // 其他错误记录并转换为GraphQL错误
    logger.error('Error in getSessionByToken resolver', {
      error: error.message,
      stack: error.stack,
      token
    });
    throw createWhatsAppError(ERROR_TYPES.INVALID_TOKEN);
  }
};

module.exports = {
  Query: {
    getSessionByToken: getSessionByTokenResolver
  }
};
