
"""
owner is the owner of restaurant
"""
type Owner {
  _id: String
  email: String
}
type Variation {
  _id: ID!
  title: String!
  price: Float!
  discounted: Float
  addons: [String!]
}

type CartVariation {
  _id: ID!
  title: String!
  price: Float!
  discounted: Float
  addons: [CartAddon!]
}


type Food {
  _id: ID!
  title: String!
  description: String!
  variations: [Variation!]!
  image: String
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
}

type CartFood {
  _id: ID!
  title: String!
  description: String!
  variation: CartVariation!
  image: String!
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
}

type Option {
  _id: String!
  title: String!
  description: String
  price: Float!
}

type Addon {
  _id: String!
  options: [String!]
  title: String!
  description: String
  quantityMinimum: Int!
  quantityMaximum: Int!
}

type CartAddon {
  _id: String!
  options: [Option!]
  title: String!
  description: String
  quantityMinimum: Int!
  quantityMaximum: Int!
}


# ============================================================================
# Core Restaurant Type
# ============================================================================

type Restaurant {
  _id: ID!
  orderId: Int!
  orderPrefix: String
  name: String!
  image: String
  logo: String
  address: String
  location: Point
  categories: [Category!]
  options: [Option!]
  addons: [Addon!]
  reviewData: ReviewData
  zone: Zone
  username: String
  password: String
  deliveryTime: Int
  minimumOrder: Int
  sections: [String!]
  rating: Float
  isActive: Boolean!
  isAvailable: Boolean!
  openingTimes: [OpeningTimes]
  slug: String
  stripeDetailsSubmitted: Boolean
  commissionRate: Float
  owner: Owner
  deliveryBounds: Polygon
  tax: Float
  notificationToken: String
  enableNotification: Boolean
  shopType: String
  cuisines: [String]
  keywords: [String]
  tags: [String]
  reviewCount: Int
  reviewAverage: Float
  restaurantUrl: String
  phone: String
  deliveryCostType: String
  deliveryCostRate: Float
  deliveryCostMin: Float
  restaurantBrandId: ID
  restaurantBrand: String
}

# ============================================================================
# Restaurant Operating Hours Types
# ============================================================================

type OpeningTimes {
  day: String!
  times: [Timings]
}

type Timings {
  startTime: [String]
  endTime: [String]
}


type Category {
  _id: ID!
  title: String!
  foods: [Food!]
  createdAt: String!
  updatedAt: String!
}

# ============================================================================
# Restaurant Authentication Types
# ============================================================================

type OwnerAuthData {
  userId: ID!
  token: String!
  tokenExpiration: Int!
  email: String!
  userType: String!
  restaurants: [Restaurant]!
  pushToken: String
}

type OwnerData {
  _id: ID!
  email: String!
  userType: String!
  restaurants: [Restaurant]!
  pushToken: String
}

input AddonsInput {
  _id: String
  options: [String!]
}
input OrderInput {
  food: String!
  quantity: Int!
  variation: String!
  addons: [AddonsInput!]
  specialInstructions: String
}

input VariationInput {
  _id: String
  title: String
  price: Float!
  discounted: Float
  addons: [String!]
}

input FoodInput {
  _id: String
  restaurant: String!
  category: String!
  title: String!
  description: String
  image: String
  variations: [VariationInput!]!
}

input CategoryInput {
  _id: String
  title: String!
  restaurant: String!
}

input RestaurantInput {
  name: String!
  username: String
  password: String
  image: String
  logo: String
  address: String
  categories: [CategoryInput!]
  reviews: [ReviewInput!]
  deliveryTime: Int
  minimumOrder: Int
  salesTax: Float
  shopType: String
  cuisines: [String]
  restaurantUrl: String
  phone: String
  deliveryCostType: String
  deliveryCostRate: Float
  deliveryCostMin: Float
  restaurantBrandId: ID
  restaurantBrand: String
}

input AddonInput {
  restaurant: String!
  addons: [createAddonInput!]!
}

input RestaurantProfileInput {
  _id: String
  name: String!
  image: String
  logo: String
  address: String
  orderPrefix: String
  username: String
  password: String
  deliveryTime: Int
  minimumOrder: Int
  salesTax: Float
  shopType: String
  cuisines: [String]
  restaurantUrl: String
  phone: String
  deliveryCostType: String
  deliveryCostRate: Float
  deliveryCostMin: Float
  restaurantBrandId: ID
  restaurantBrand: String
}

input OptionInput {
  _id: String
  title: String!
  description: String
  price: Float!
}

input OwnerInput {
  email: String
  password: String
}

input VendorInput {
  _id: String
  email: String
  password: String
}

input editOptionInput {
  restaurant: String!
  options: OptionInput
}

input CreateOptionInput {
  restaurant: String
  options: [OptionInput!]!
}

input editAddonInput {
  restaurant: String!
  addons: createAddonInput!
}
input createAddonInput {
  title: String!
  _id: String
  description: String
  options: [String]
  quantityMinimum: Int!
  quantityMaximum: Int!
}

input CouponInput {
  _id: String
  title: String!
  discount: Float!
  enabled: Boolean
}
input TippingInput {
  _id: String
  tipVariations: [Float]
  enabled: Boolean
}
input TaxationInput {
  _id: String
  taxationCharges: Float
  enabled: Boolean
}

input CartFoodInput {
  _id: String
  variation: CartVariationInput!
}
input CartVariationInput {
  _id: String
  addons: [CartAddonInput!]
}
input CartAddonInput {
  _id: String
  options: [String!]
}
input OfferInput {
  _id: String
  name: String!
  tag: String!
  restaurants: [String]
}
input SectionInput {
  _id: String
  name: String!
  enabled: Boolean!
  restaurants: [String]
}

input TimingsInput {
  day: String!
  times: [TimesInput]
}

input TimesInput {
  startTime: [String]
  endTime: [String]
}

input DeliveryCostConfigurationInput {
  deliveryRate: Float!
  costType: String
}

# ============================================================================
# Restaurant Operations
# ============================================================================

extend type Query {
  restaurant(id: String, slug: String): Restaurant!
  restaurants: [Restaurant!]
  restaurantOrders: [Order!]!

  #TBD
  restaurantPreview(id: String, slug: String): RestaurantPreview!
  restaurantsPreview: [RestaurantPreview!]
}

extend type Mutation {
  createRestaurant(restaurant: RestaurantInput!, owner: ID!): Restaurant!
  editRestaurant(restaurant: RestaurantProfileInput!): Restaurant!

  deleteRestaurant(id: String!): Restaurant!
  createOptions(optionInput: CreateOptionInput): Restaurant!
  editOption(optionInput: editOptionInput): Restaurant!
  deleteOption(id: String!, restaurant: String!): Restaurant!
  createAddons(addonInput: AddonInput): Restaurant!
  editAddon(addonInput: editAddonInput): Restaurant!
  deleteAddon(id: String!, restaurant: String!): Restaurant!
  createCategory(category: CategoryInput): Restaurant!
  editCategory(category: CategoryInput): Restaurant!
  createFood(foodInput: FoodInput): Restaurant!
  editFood(foodInput: FoodInput): Restaurant!
  deleteCategory(id: String!, restaurant: String!): Restaurant!
  deleteFood(id: String!, restaurant: String!, categoryId: String!): Restaurant!
  saveRestaurantToken(token: String, isEnabled: Boolean): Restaurant!
  updateTimings(id: String!, openingTimes: [TimingsInput]): Restaurant!
  toggleAvailability: Restaurant!
  updateCommission(id: String!, commissionRate: Float!): Restaurant!
  toggleMenuFood(id: ID!, restaurant: ID!, categoryId: ID!): Food!

  updateDeliveryBoundsAndLocation(
    id: ID!
    bounds: [[[Float!]]]
    location: CoordinatesInput!
  ): RestaurantResponse!
}
