
type Address {
  _id: ID!
  location: Point
  deliveryAddress: String!
  details: String
  label: String!
  selected: Boolean
}

type OrdersWithCashOnDeliveryInfo {
  orders: [Order!]!
  totalAmountCashOnDelivery: Float!
  countCashOnDeliveryOrders: Int!
}
type RestaurantPreview {
  _id: ID!
  orderId: Int!
  orderPrefix: String
  name: String!
  image: String
  address: String
  location: Point
  zone: Zone
  username: String
  password: String
  deliveryTime: Int
  minimumOrder: Int
  sections: [String!]
  rating: Float
  isActive: Boolean!
  isAvailable: Boolean!
  openingTimes: [OpeningTimes]
  slug: String
  stripeDetailsSubmitted: Boolean
  commissionRate: Float
  owner: Owner
  deliveryBounds: Polygon
  tax: Float
  notificationToken: String
  enableNotification: Boolean
  shopType: String
  cuisines: [String]
  keywords: [String]
  tags: [String]
  reviewCount: Int
  reviewAverage: Float
  restaurantUrl: String
  phone: String
}
"""
user is the previous design of customer, now replaced by customer
"""
type User {
  _id: ID
  name: String
  phone: String
  phoneIsVerified: Boolean
  email: String
  emailIsVerified: Boolean
  password: String
  isActive: Boolean
  isOrderNotification: Boolean
  isOfferNotification: Boolean
  createdAt: String
  updatedAt: String
  addresses: [Address!]
  notificationToken: String
  favourite: [String!]
  userType: String
}
type Configuration {
  _id: String!
  pushToken: String
  email: String
  emailName: String
  password: String
  enableEmail: Boolean
  clientId: String
  clientSecret: String
  sandbox: Boolean
  publishableKey: String
  secretKey: String
  currency: String
  currencySymbol: String
  deliveryRate: Float
  twilioAccountSid: String
  twilioAuthToken: String
  twilioPhoneNumber: String
  twilioEnabled: Boolean
  formEmail: String
  sendGridApiKey: String
  sendGridEnabled: Boolean
  sendGridEmail: String
  sendGridEmailName: String
  sendGridPassword: String
  dashboardSentryUrl: String
  webSentryUrl: String
  apiSentryUrl: String
  customerAppSentryUrl: String
  restaurantAppSentryUrl: String
  riderAppSentryUrl: String
  googleApiKey: String
  cloudinaryUploadUrl: String
  cloudinaryApiKey: String
  webAmplitudeApiKey: String
  appAmplitudeApiKey: String
  webClientID: String
  androidClientID: String
  iOSClientID: String
  expoClientID: String
  googleMapLibraries: String
  googleColor: String
  termsAndConditions: String
  privacyPolicy: String
  testOtp: String
  firebaseKey: String
  authDomain: String
  projectId: String
  storageBucket: String
  msgSenderId: String
  appId: String
  measurementId: String
  isPaidVersion: Boolean
  skipMobileVerification: Boolean
  skipEmailVerification: Boolean
  enableRiderDemo: Boolean
  enableRestaurantDemo: Boolean
  enableAdminDemo: Boolean
  costType: String
  vapidKey: String
}

type MyOrders {
  userId: String!
  orders: [Order!]
}

type ForgotPassword {
  result: Boolean!
}

type DashboardData {
  totalOrders: Int!
  totalSales: Float!
}

type DashboardSales {
  orders: [SalesValues!]
}
type DashboardOrders {
  orders: [OrdersValues!]
}

type SalesValues {
  day: String!
  amount: Float!
}
type OrdersValues {
  day: String!
  count: Int!
}
type Coupon {
  _id: String!
  title: String!
  discount: Float!
  enabled: Boolean!
}
type Taxation {
  _id: String!
  taxationCharges: Float
  enabled: Boolean!
}
type Tipping {
  _id: String!
  tipVariations: [Float]
  enabled: Boolean!
}

type OfferInfo {
  _id: String!
  name: String!
  tag: String!
  restaurants: [String]
}
type SectionInfo {
  _id: String!
  name: String!
  restaurants: [String]
}
type Offer {
  _id: String!
  name: String!
  tag: String!
  restaurants: [OfferRestaurant]
}

type OfferRestaurant {
  _id: String!
  name: String!
  image: String!
  address: String!
  location: Point
  categories: [Category]
}
type SectionRestaurant {
  _id: String!
  name: String!
}
type Section {
  _id: String!
  name: String!
  enabled: Boolean!
  restaurants: [SectionRestaurant]
}

type SubscriptionOrders {
  restaurantId: String
  customerId: String
  order: Order!
  origin: String!
}

type Subscription_Zone_Orders {
  zoneId: String
  order: Order!
  origin: String!
}

type NearByDataPreview {
  restaurants: [RestaurantPreview!]
  offers: [OfferInfo!]
  sections: [SectionInfo!]
}

input EmailConfigurationInput {
  email: String!
  password: String!
  emailName: String!
  enableEmail: Boolean!
}

input TwilioConfigurationInput {
  twilioAccountSid: String!
  twilioAuthToken: String!
  twilioPhoneNumber: String!
  twilioEnabled: Boolean!
}
input FormEmailConfigurationInput {
  formEmail: String!
}
input SendGridConfigurationInput {
  sendGridApiKey: String!
  sendGridEnabled: Boolean!
  sendGridEmail: String!
  sendGridEmailName: String!
  sendGridPassword: String!
}

input SentryConfigurationInput {
  dashboardSentryUrl: String!
  webSentryUrl: String!
  apiSentryUrl: String!
  customerAppSentryUrl: String!
  restaurantAppSentryUrl: String!
  riderAppSentryUrl: String!
}
input GoogleApiKeyConfigurationInput {
  googleApiKey: String!
}
input CloudinaryConfigurationInput {
  cloudinaryUploadUrl: String!
  cloudinaryApiKey: String!
}
input AmplitudeApiKeyConfigurationInput {
  webAmplitudeApiKey: String!
  appAmplitudeApiKey: String!
}
input GoogleClientIDConfigurationInput {
  webClientID: String!
  androidClientID: String!
  iOSClientID: String!
  expoClientID: String!
}
input WebConfigurationInput {
  googleMapLibraries: String!
  googleColor: String!
}
input AppConfigurationsInput {
  termsAndConditions: String!
  privacyPolicy: String!
  testOtp: String!
}

input FirebaseConfigurationInput {
  firebaseKey: String!
  authDomain: String!
  projectId: String!
  storageBucket: String!
  msgSenderId: String!
  appId: String!
  measurementId: String!
  vapidKey: String
}

input PaypalConfigurationInput {
  clientId: String!
  clientSecret: String!
  sandbox: Boolean!
}

input StripeConfigurationInput {
  publishableKey: String!
  secretKey: String!
}

input CurrencyConfigurationInput {
  currency: String!
  currencySymbol: String!
}

input VerificationConfigurationInput {
  skipEmailVerification: Boolean!
  skipMobileVerification: Boolean!
}

input DemoConfigurationInput {
  enableRiderDemo: Boolean!
  enableRestaurantDemo: Boolean!
  enableAdminDemo: Boolean!
}

input UpdateUser {
  name: String!
  phone: String
  phoneIsVerified: Boolean
  emailIsVerified: Boolean
}

input UserInput {
  phone: String
  email: String
  password: String
  name: String

  notificationToken: String
  appleId: String
}

input FormSubmissionInput {
  name: String!
  email: String!
  message: String!
}

input CuisineInput {
  _id: String
  name: String!
  description: String
  image: String
  shopType: String
}

type Cuisine {
  _id: String!
  name: String!
  description: String
  image: String
  shopType: String
}

type Banner {
  _id: String!
  title: String
  description: String
  file: String
  action: String
  screen: String
  parameters: String
}

input BannerInput {
  _id: String
  title: String
  description: String
  file: String
  action: String
  screen: String
  parameters: String
}

type FormSubmissionResponse {
  message: String!
  status: String!
}

type RestaurantResponse {
  success: Boolean!
  message: String
  data: Restaurant
}



type SaveNotificationTokenWebResponse {
  success: Boolean!
  message: String
}

type Otp {
  result: Boolean!
}


type Pagination {
  total: Int
}

type City {
  id: Int
  name: String
  latitude: String
  longitude: String
}

type Country {
  id: Int
  name: String
  latitude: String
  longitude: String
  cities: [City]
}

type PopularItemsResponse {
  id: String!
  count: Int!
}

type DemoCredentails {
  restaurantUsername: String
  restaurantPassword: String
  riderUsername: String
  riderPassword: String
}

extend type Query {
  categories: [Category!]!
  foods: [Food!]!

  getDashboardTotal(
    starting_date: String
    ending_date: String
    restaurant: String!
  ): DashboardData!
  likedFood: [Food!]!
  foodByCategory(
    category: String!
    onSale: Boolean
    inStock: Boolean
    min: Float
    max: Float
    search: String
  ): [Food!]!
  profile: User
  configuration: Configuration!
  users: [User!]
  userFavourite(latitude: Float, longitude: Float): [Restaurant]
  orderPaypal(id: String!): Order!
  orderStripe(id: String!): Order!
  pageCount(restaurant: String!): Int

  options: [Option!]
  addons: [Addon!]
  foodByIds(foodIds: [CartFoodInput!]!): [CartFood!]
  getDashboardOrders(
    starting_date: String
    ending_date: String
    restaurant: String!
  ): DashboardOrders!
  getDashboardSales(
    starting_date: String
    ending_date: String
    restaurant: String!
  ): DashboardSales!
  coupons: [Coupon!]!
  cuisines: [Cuisine!]!
  banners: [Banner!]!
  bannerActions: [String!]!
  taxes: Taxation!
  tips: Tipping!

  restaurantList: [Restaurant!]
  restaurantListPreview: [RestaurantPreview!]

  restaurantByOwner(id: String): OwnerData!
  offers: [Offer]
  sections: [Section]
  vendors: [OwnerData]
  getVendor(id: String!): OwnerData
  orderCount(restaurant: String!): Int
  zones: [Zone!]
  zone(id: String!): Zone!
  getActiveOrders(restaurantId: ID): [Order!]
  orderDetails(id: String!): Order!
  getCountries: [Country]
  getCountryByIso(iso: String!): Country
  recentOrderRestaurants(latitude: Float!, longitude: Float!): [Restaurant!]
  recentOrderRestaurantsPreview(
    latitude: Float!
    longitude: Float!
  ): [RestaurantPreview!]
  mostOrderedRestaurants(latitude: Float!, longitude: Float!): [Restaurant!]
  mostOrderedRestaurantsPreview(
    latitude: Float!
    longitude: Float!
  ): [RestaurantPreview!]
  relatedItems(itemId: String!, restaurantId: String!): [String!]!
  popularItems(restaurantId: String!): [PopularItemsResponse!]!

  lastOrderCreds: DemoCredentails
}

extend type Mutation {
  sendOtpToEmail(email: String!, otp: String!): Otp!
  sendOtpToPhoneNumber(phone: String!, otp: String!): Otp!
  emailExist(email: String!): User!
  phoneExist(phone: String!): User!
  Deactivate(isActive: Boolean!, email: String!): User!
  adminLogin(email: String!, password: String!): Admin!
  login(
    appleId: String
    email: String
    password: String
    type: String!
    name: String
    notificationToken: String
    isActive: Boolean
  ): AuthData!
  ownerLogin(email: String!, password: String!): OwnerAuthData!
  createUser(userInput: UserInput): AuthData!
  createVendor(vendorInput: VendorInput): OwnerData!
  editVendor(vendorInput: VendorInput): OwnerData!
  deleteVendor(id: String!): Boolean
  updateUser(updateUserInput: UpdateUser!): User!
  updateNotificationStatus(
    offerNotification: Boolean!
    orderNotification: Boolean!
  ): User!
  orderPickedUp(_id: String!): Order!
  likeFood(foodId: String!): Food!
  saveEmailConfiguration(
    configurationInput: EmailConfigurationInput!
  ): Configuration!
  saveFormEmailConfiguration(
    configurationInput: FormEmailConfigurationInput!
  ): Configuration!
  saveSendGridConfiguration(
    configurationInput: SendGridConfigurationInput!
  ): Configuration!

  saveFirebaseConfiguration(
    configurationInput: FirebaseConfigurationInput!
  ): Configuration!

  saveSentryConfiguration(
    configurationInput: SentryConfigurationInput!
  ): Configuration!
  saveGoogleApiKeyConfiguration(
    configurationInput: GoogleApiKeyConfigurationInput!
  ): Configuration!
  saveCloudinaryConfiguration(
    configurationInput: CloudinaryConfigurationInput!
  ): Configuration!
  saveAmplitudeApiKeyConfiguration(
    configurationInput: AmplitudeApiKeyConfigurationInput!
  ): Configuration!
  saveGoogleClientIDConfiguration(
    configurationInput: GoogleClientIDConfigurationInput!
  ): Configuration!
  saveWebConfiguration(
    configurationInput: WebConfigurationInput!
  ): Configuration!
  saveAppConfigurations(
    configurationInput: AppConfigurationsInput!
  ): Configuration!

  saveDeliveryRateConfiguration(
    configurationInput: DeliveryCostConfigurationInput
  ): Configuration

  savePaypalConfiguration(
    configurationInput: PaypalConfigurationInput!
  ): Configuration!
  saveStripeConfiguration(
    configurationInput: StripeConfigurationInput!
  ): Configuration!
  saveTwilioConfiguration(
    configurationInput: TwilioConfigurationInput!
  ): Configuration!

  saveCurrencyConfiguration(
    configurationInput: CurrencyConfigurationInput!
  ): Configuration!
  pushToken(token: String): User!
  updateOrderStatus(id: String!, status: String!, reason: String): Order!
  uploadToken(id: String!, pushToken: String!): OwnerData!
  forgotPassword(email: String!, otp: String!): ForgotPassword!
  resetPassword(password: String!, email: String!): ForgotPassword!
  vendorResetPassword(oldPassword: String!, newPassword: String!): Boolean!


  
  updatePaymentStatus(id: String, status: String): Order!

  createCoupon(couponInput: CouponInput!): Coupon!
  editCoupon(couponInput: CouponInput!): Coupon!
  deleteCoupon(id: String!): String!
  coupon(coupon: String!): Coupon!
  createCuisine(cuisineInput: CuisineInput!): Cuisine!
  editCuisine(cuisineInput: CuisineInput!): Cuisine!
  deleteCuisine(id: String!): String!
  cuisine(cuisine: String!): Cuisine!
  createBanner(bannerInput: BannerInput!): Banner!
  editBanner(bannerInput: BannerInput!): Banner!
  deleteBanner(id: String!): String!
  banner(banner: String!): Banner!
  createTipping(tippingInput: TippingInput!): Tipping!
  editTipping(tippingInput: TippingInput!): Tipping!
  createTaxation(taxationInput: TaxationInput!): Taxation!
  editTaxation(taxationInput: TaxationInput!): Taxation!
  createAddress(addressInput: AddressInput!): User!
  editAddress(addressInput: AddressInput!): User!
  deleteAddress(id: ID!): User!
  deleteBulkAddresses(ids: [ID!]!): User!
  changePassword(oldPassword: String!, newPassword: String!): Boolean!
  createOffer(offer: OfferInput!): Offer!
  editOffer(offer: OfferInput!): Offer!
  deleteOffer(id: String!): Boolean
  createSection(section: SectionInput!): Section!
  editSection(section: SectionInput!): Section!
  deleteSection(id: String!): Boolean
  addRestaurantToOffer(id: String!, restaurant: String!): Offer
  selectAddress(id: String!): User!
  muteRing(orderId: String): Boolean!
  restaurantLogin(username: String!, password: String!): RestaurantAuth!
  createZone(zone: ZoneInput!): Zone!
  editZone(zone: ZoneInput!): Zone!
  deleteZone(id: String!): Zone!

  addFavourite(id: String!): User!
  sendNotificationUser(
    notificationTitle: String
    notificationBody: String!
  ): String!

  saveNotificationTokenWeb(token: String!): SaveNotificationTokenWebResponse!
  sendFormSubmission(
    formSubmissionInput: FormSubmissionInput!
  ): FormSubmissionResponse!
  abortOrder(id: String!): Order!
  saveVerificationsToggle(
    configurationInput: VerificationConfigurationInput!
  ): Configuration!

  saveDemoConfiguration(
    configurationInput: DemoConfigurationInput!
  ): Configuration!
}