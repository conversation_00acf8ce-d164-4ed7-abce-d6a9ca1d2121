# ============================================================================
# Order Summary Types
# ============================================================================

type OrderBrief {
  orderId: ID!
  amount: Float!
  restaurantId: ID!
  restaurantName: String!
  restaurantBrandId: ID
  restaurantBrand: String
  orderStatus: OrderStatus!
  orderDate: DateTime!
}

# ============================================================================
# Order Status Enum
# ============================================================================

enum OrderStatus {
  PENDING
  ACCEPTED
  PICKED
  DELIVERED
  CANCELLED
  COMPLETED
  ASSIGNED
  PARTIALLY_REFUNDED
  REFUNDED
}

enum RefundReason {
  MERCHANT_OUT_OF_STOCK
  MERCHANT_CANNOT_DELIVER
  MERCHANT_OTHER
  CUSTOMER_CANCELLED
}

enum RefundType {
  FULL
  PARTIAL
}

enum RefundStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
}

enum OrderRefundStatus {
  NONE
  PARTIAL
  FULL
}

type Refund {
  _id: ID!
  refundId: String!
  orderId: String!
  refundType: RefundType!
  requestAmount: Float!
  finalRefundAmount: Float!
  reason: RefundReason!
  reasonText: String
  feeBearer: String!
  transactionFee: Float!
  status: RefundStatus!
  stripeRefundId: String
  createdAt: DateTime!
  processedAt: DateTime
  completedAt: DateTime
  errorMessage: String
}

type RefundResponse {
  success: Boolean!
  refund: Refund!
  order: Order!
  message: String
}



# ============================================================================
# Detailed Order Type
# ============================================================================

type ItemVariation {
  _id: ID!
  title: String!
  price: Float!
  discounted: Float!
}


type ItemOption {
  _id: String!
  title: String!
  description: String
  price: Float!
}

type ItemAddon {
  _id: String!
  options: [ItemOption!]
  title: String!
  description: String
  quantityMinimum: Int!
  quantityMaximum: Int!
}

type Item {
  _id: ID!
  title: String!
  food: String!
  description: String!
  image: String
  quantity: Int!
  variation: ItemVariation!
  addons: [ItemAddon!]
  specialInstructions: String
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
}

type Order {
  _id: ID!
  """
  same as in orderbrief
  """
  orderId: String!
  orderAmount: Float
  restaurantId: ID!
  restaurantName: String!
  restaurantBrandId: ID
  restaurantBrand: String
  orderStatus: OrderStatus!
  orderDate: DateTime!
  """
  detailed order info
  """
  customerId: String!
  customerPhone: String
  items: [Item!]!
  """
  # deliveryAddress is formattedAddress in CustomerAddress, for print and manual check. Also to ensure the data is saved even if customer delete their address info.
  # deliveryAddressId is addressId in CustomerAddress, together with customerId could identify the unique address
  """
  isPickedUp: Boolean!
  deliveryAddress: String!
  deliveryAddressId: ID!
  deliveryCoordinates: Point
  deliveryInstructions: String
  """
  status
  """
  status: Boolean
  completionTime: String
  expectedTime: String
  preparationTime: String
  acceptedAt: String
  pickedAt: String
  deliveredAt: String
  cancelledAt: String
  assignedAt: String

  """
  payment related
  """
  paymentMethod: String
  paidAmount: Float
  paymentStatus: String!
  reason: String
  isActive: Boolean!
  createdAt: String!
  updatedAt: String!
  deliveryCharges: Float
  tipping: Float!
  taxationAmount: Float!
  """
  delivery related
  """
  rider: Rider
  review: Review
  zone: Zone!
  isRinged: Boolean!
  isRiderRinged: Boolean!
  instructions: String
  """
  refund related
  """
  refunds: [Refund!]!
  totalRefunded: Float!
  refundStatus: OrderRefundStatus!
}

extend type Query {
  orders(
    restaurantId: ID
    restaurantBrandId: ID
    orderStatus: OrderStatus
    startTime: DateTime
    endTime: DateTime
    offset: Int
  ): [Order!]!
  order(id: String!): Order!
  ordersByRestId(
    restaurant: String!
    page: Int
    rows: Int
    search: String
  ): [Order!]
    getOrdersByDateRange(
    startingDate: String!
    endingDate: String!
    restaurant: String!
  ): OrdersWithCashOnDeliveryInfo!
    getOrderStatuses: [String!]
  getPaymentStatuses: [String!]
  undeliveredOrders(offset: Int): [Order!]!
  deliveredOrders(offset: Int): [Order!]!
  allOrders(page: Int): [Order!]!
  # 退款相关查询
  getRefund(refundId: String!): Refund
  getOrderRefunds(orderId: String!): [Refund!]!
}

extend type Mutation {
  placeOrder(
    restaurantId: ID!
    customerId: String!
    orderInput: [OrderInput!]!
    paymentMethod: String!
    couponCode: String
    deliveryAddressId: ID
    tipping: Float
    orderDate: String!
    isPickedUp: Boolean!
    taxationAmount: Float!
    deliveryCharges: Float!
    instructions: String
  ): Order!

  placeOrderWhatsApp(
    restaurantId: ID!
    customerId: String!
    orderInput: [OrderInput!]!
    paymentMethod: String!
    couponCode: String
    deliveryAddressId: ID
    tipping: Float
    orderDate: String!
    isPickedUp: Boolean!
    taxationAmount: Float!
    deliveryCharges: Float!
    instructions: String
    itemsSubtotal: Float!
    orderAmount: Float!
  ): Order!

  cancelOrder(_id: String!, reason: RefundReason!): Order!
  editOrder(_id: String!, orderInput: [OrderInput!]!): Order!
  acceptOrder(_id: String!, time: String): Order!
  # 退款相关操作
  refundOrder(
    _id: String!
    amount: Float!
    reason: RefundReason!
    reasonText: String
  ): RefundResponse!
}
extend type Subscription {
  subscribePlaceOrder(restaurant: String!): SubscriptionOrders!
  SubscribeOrderStatus(customerId: String!): SubscriptionOrders!
  subscriptionOrder(id: String!): Order!
  subscriptionAssignRider(riderId: String!): SubscriptionOrders!
}
