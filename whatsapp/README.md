# WhatsApp Integration Module

本模块实现了 Firespoon API 与 Payemoji WhatsApp 消息服务的集成，提供完整的 WhatsApp 对话管理、订单处理和支付流程。

## 模块结构

```
whatsapp/
├── cart.js                    # 购物车处理模块
├── controllers/               # 控制器层
│   └── webhookController.js   # Webhook事件处理
├── machines/                  # 对话状态机
│   ├── dialog.js              # 对话管理器
│   └── orderFsmActions.js     # 订单状态机动作函数
├── middleware/                # 中间件
│   ├── sessionValidator.js    # 会话验证中间件
│   └── whatsappAuth.js        # Webhook验证中间件
├── routes/                    # 路由定义
│   └── index.js               # 集中路由管理
├── services/                  # 服务层
│   ├── messageBuilders.js     # 消息构建器
│   ├── restaurantStore.js     # 餐厅数据存储
│   ├── sessionService.js      # 会话管理服务
│   └── whatsappService.js     # WhatsApp API封装
└── utils/                     # 工具函数
    ├── linkGenerator.js       # 链接生成器
    └── sessionIdGenerator.js  # 会话ID生成器
```

## 核心功能

### 1. Webhook 处理
- 接收和处理来自 Payemoji 的 Webhook 事件
- 支持消息接收、状态更新和对话状态变更事件
- 自动验证 webhook 签名和防重放攻击

### 2. 会话管理
- 基于 Redis 的会话存储，TTL 为 24 小时
- 支持会话创建、读取、更新和删除
- 会话事件队列处理，确保消息按顺序处理
- 支持通过 token 进行会话认证

### 3. 对话状态机
- 事件驱动的对话流程管理
- 支持餐厅选择、菜单浏览、地址管理、订单确认等完整流程
- 智能上下文管理，跟踪用户状态和偏好

### 4. 消息处理
- 多种消息类型支持：文本、快速回复、列表选择、模板消息
- 消息队列和重试机制
- 自动消息格式化和本地化

### 5. 订单管理
- 完整的订单生命周期管理
- 支持自取和配送订单
- 实时价格计算和订单验证
- 订单历史查询和重新下单

## API 端点

### Webhook 端点
- `POST /whatsapp/webhook` - 接收入站消息和事件

### 购物车管理
- `POST /whatsapp/submit-cart` - 提交购物车数据（需要 X-WhatsAppW-Token 认证）

### 地址管理
地址管理已迁移到 GraphQL API，使用 `X-WhatsAppW-Token` 请求头进行认证：
- 查询：`customerAddresses` - 获取客户地址列表
- 变更：`addCustomerAddress` - 添加新地址
- 变更：`updateCustomerAddress` - 更新地址
- 变更：`deleteCustomerAddress` - 删除地址

## 函数调用关系

### 主要服务类

#### WhatsAppService
- `sendDialogueText(dialogueId, text)` - 发送对话文本消息
- `sendQuickReply(dialogueId, options, type)` - 发送快速回复按钮
- `sendListPicker(dialogueId, options, type)` - 发送列表选择器
- `sendBasicText(phone, text)` - 发送基础文本消息
- `getAccessToken()` - 获取访问令牌

#### SessionService
- `createSession(dialogueId, customerPhone, recipientId, context, brandWhatsappId)` - 创建会话
- `getSession(dialogueId)` - 获取会话
- `getSessionByToken(token)` - 通过 token 获取会话
- `updateSession(dialogueId, data)` - 更新会话
- `queueSessionEvent(dialogueId, event)` - 队列会话事件

#### DialogManager
- `processSessionEvent(session, event)` - 处理会话事件
- `handleCartUpdate(session, eventData, updatedContext)` - 处理购物车更新
- `handleOrderPlaced(session, eventData, updatedContext)` - 处理订单下单
- `handlePaymentDone(session, eventData, updatedContext)` - 处理支付完成

### 订单状态机动作函数

#### 消息发送函数
- `sendWelcomeMessage(session, updatedContext)` - 发送欢迎消息
- `sendPaymentLink(context, paymentLink, orderId)` - 发送支付链接
- `askForAddress(context)` - 请求地址选择
- `sendAddressSelectionDialog(context)` - 发送地址选择对话框
- `showOrderHistory(context)` - 显示订单历史
- `sendManageAddress(context)` - 发送地址管理界面

#### 工具函数
- `orderFormat(currentOrder, currentOrderState, restaurant, isAddressSelected)` - 格式化订单信息
- `calculateTotal(items)` - 计算商品总价
- `calculateFinalTotal(order)` - 计算最终总价
- `formatItems(items, restaurant)` - 格式化商品列表
- `fetchCustomerInfo(whatsappNumber, brandId)` - 获取客户信息
- `fetchBrandInfo(brandWhatsappId)` - 获取品牌信息
- `getCurrencySymbol()` - 获取货币符号
- `initCurrency()` - 初始化货币配置

#### 格式化函数
- `formatOrderListItem(order, restaurantName, index, currencySymbol)` - 格式化订单列表项
- `formatDateWithShortMonth(date)` - 格式化日期（短月份）
- `formatDateYYMMDDHHMM(date)` - 格式化日期（年月日时分）
- `base64UrlEncode(str)` - Base64 URL 编码

## 消息流程

### 1. 用户首次接触流程
```
1. Payemoji Webhook → webhookController.handleIncoming()
2. webhookController.processIncomingMessage()
   ├── webhookController.extractWebhookData() - 提取消息数据
   ├── webhookController.getOrCreateSession() - 获取或创建会话
   │   ├── sessionService.getSession() - 尝试获取现有会话
   │   └── sessionService.createSession() - 创建新会话
   │       ├── fetchCustomerInfo() - 获取客户信息
   │       ├── fetchBrandInfo() - 获取品牌信息
   │       └── restaurantStore.getRestaurantsByBrand() - 获取餐厅列表
   └── sessionService.queueSessionEvent() - 队列消息事件
3. DialogManager.processSessionEvent()
   └── DialogManager.handleMessageReceived()
       └── orderFsmActions.sendWelcomeMessage() - 发送欢迎消息
           ├── linkGenerator.generateMenuLink() - 生成菜单链接
           └── whatsappService.sendQuickReply() - 发送快速回复按钮
```

### 2. 购物车提交和订单流程
```
1. Web端购物车提交 → POST /whatsapp/submit-cart
2. sessionValidator() - 验证会话token
3. cartHandler.validatePayload() - 验证购物车数据
4. cartHandler.handleSubmit() - 处理提交
   └── sessionService.queueSessionEvent(CART_SUBMITTED)
5. DialogManager.handleCartUpdate()
   ├── restaurantStore.getRestaurantRef() - 获取餐厅信息
   ├── 价格计算和验证
   │   ├── 商品价格验证
   │   ├── 配送费计算 (calculateDistance)
   │   └── 税费和小费处理
   ├── orderFsmActions.orderFormat() - 格式化订单信息
   └── 地址检查
       ├── 已选择地址 → whatsappService.sendQuickReply() (确认按钮)
       └── 未选择地址 → orderFsmActions.askForAddress()
6. 用户确认订单 → DialogManager.handleOrderPlaced()
   ├── resolvers.Mutation.placeOrderWhatsApp() - 调用GraphQL下单
   ├── orderFsmActions.orderFormat() - 生成订单摘要
   └── orderFsmActions.sendPaymentLink() - 发送支付链接
7. 支付完成 → DialogManager.handlePaymentDone()
   └── whatsappService.sendDialogueText() - 发送确认消息
```

### 3. 地址管理流程
```
1. 用户选择"管理地址" → DialogManager.handleMessageReceived()
2. orderFsmActions.sendManageAddress()
   ├── 检查现有地址
   │   ├── 无地址 → linkGenerator.generateAddressLink()
   │   └── 有地址 → whatsappService.sendListPicker()
   └── whatsappService.sendDialogueText() - 发送添加地址链接
3. 地址选择/添加 → GraphQL API (X-WhatsAppW-Token认证)
   ├── customerAddresses Query - 获取地址列表
   ├── addCustomerAddress Mutation - 添加地址
   └── updateCustomerAddress Mutation - 更新地址
4. 地址更新 → DialogManager.handleAddressUpdated()
   └── 更新会话上下文中的地址信息
```

### 4. 订单历史流程
```
1. 用户选择"订单历史" → DialogManager.handleOrderHistory()
2. orderFsmActions.showOrderHistory()
   ├── 检查客户订单历史
   ├── orderFsmActions.getCurrencySymbol() - 获取货币符号
   ├── orderFsmActions.formatOrderListItem() - 格式化订单项
   │   └── orderFsmActions.formatDateWithShortMonth() - 格式化日期
   └── whatsappService.sendListPicker() - 发送订单列表
3. 用户选择订单 → DialogManager.handleViewOrderDetails()
   ├── 获取订单详情
   ├── 格式化订单信息
   └── whatsappService.sendQuickReply() - 发送详情和操作按钮
4. 重新下单 → DialogManager.handleReorder()
   ├── 验证商品可用性
   ├── 重建订单数据
   ├── orderFsmActions.orderFormat() - 格式化订单
   └── 进入地址选择流程
```

### 5. 餐厅选择流程
```
1. 用户选择"选择餐厅" → DialogManager.handleRestaurantSelected()
2. restaurantStore.getRestaurantRef() - 获取餐厅详情
3. 更新会话上下文
   ├── selectedRestaurantRef
   ├── isRestaurantSelected = true
   └── 地址相关状态重置
4. orderFsmActions.sendWelcomeMessage() - 发送更新的欢迎消息
   └── linkGenerator.generateMenuLink() - 生成新的菜单链接
```

### 6. 错误处理流程
```
1. 网络错误 → whatsappService 重试机制
   ├── 指数退避策略
   ├── 最大重试次数限制
   └── 失败回调处理
2. 数据验证错误 → 详细日志记录
   ├── logger.error() - 记录错误详情
   ├── 用户友好错误消息
   └── 优雅降级处理
3. 业务逻辑错误 → 状态恢复
   ├── 会话状态验证
   ├── 订单一致性检查
   └── 支付状态同步
```

## 外部依赖配置

### 环境变量
```bash
# Payemoji API 配置
WS_API_URL=https://api.payemoji.com
WS_AUTH_URL=https://auth.payemoji.com
WS_CLIENT_ID=your_client_id
WS_CLIENT_SECRET=your_client_secret
WS_WEBHOOK_SECRET=your_webhook_secret
WS_AGENT_ID=your_agent_id

# Redis 配置
REDIS_URL=redis://localhost:6379

# 对话会话配置
DIALOG_SESSION_TOKEN_BYTES=32
DIALOG_SESSION_TTL_SECONDS=86400

# Stripe 支付配置
STRIPE_CHECKOUT_BASE_URL=https://your-domain.com/stripe/checkout

# 货币配置
CURRENCY_DEFAULT_CURRENCY=USD
CURRENCY_DEFAULT_CURRENCY_SYMBOL=$
```

### 数据库依赖
- **MongoDB**: 存储客户信息、订单数据、餐厅信息
- **Redis**: 会话存储和消息队列

### 外部服务
- **Payemoji API**: WhatsApp 消息发送和接收
- **Stripe**: 支付处理
- **GraphQL API**: 数据查询和变更

## 安全特性

### 1. Webhook 验证
- X-MMC-Signature 签名验证
- 时间戳验证防止重放攻击
- 请求体完整性检查

### 2. 会话安全
- 不透明 token 生成
- 会话 TTL 自动过期
- 客户身份验证

### 3. 数据验证
- 严格的输入验证
- 价格和数量验证
- 地址和餐厅信息验证

## 错误处理

### 1. 网络错误
- 自动重试机制
- 指数退避策略
- 失败回调处理

### 2. 数据错误
- 详细错误日志
- 用户友好错误消息
- 优雅降级处理

### 3. 业务逻辑错误
- 状态验证
- 订单一致性检查
- 支付状态同步

## 开发指南

### 添加新的消息处理逻辑
1. 在 `DialogManager` 中添加新的事件处理器
2. 在 `orderFsmActions.js` 中实现相应的动作函数
3. 更新消息映射和事件类型

### 扩展订单流程
1. 在 `handleCartUpdate` 中添加新的验证逻辑
2. 在 `orderFormat` 中更新显示格式
3. 测试完整的订单流程

### 集成新的支付方式
1. 在 `handleOrderPlaced` 中添加支付方式选择
2. 实现相应的支付链接生成
3. 处理支付完成回调
