/**
 * Dialog Manager for WhatsApp Conversations
 * This file handles the dialog flow based on session context and webhook data
 */
const restaurantStore = require('../services/restaurantStore');
const sessionService = require('../services/sessionService');
const logger = require('../../helpers/logger');
const whatsappService = require('../services/whatsappService');
const orderFsmActions = require('./orderFsmActions');
const { calculateDistance } = require('../../helpers/utilities');
const linkGenerator = require('../utils/linkGenerator');
const config = require('../../config');

// 导入resolver
const resolvers = require('../../graphql/resolvers/index');

class DialogManager {
  constructor() {
    // Sessions are managed by sessionService using Redis

    // 事件处理器映射
    this.eventHandlers = {
      // 系统事件
      CART_SUBMITTED: this.handleCartUpdate.bind(this),
      PAYMENT_COMPLETE: this.handlePaymentDone.bind(this),

      // 用户交互事件
      RESTAURANT_SELECTED: this.handleRestaurantSelected.bind(this),
      ADDRESS_UPDATED: this.handleAddressUpdated.bind(this),
      ORDER_PLACED: this.handleOrderPlaced.bind(this),
      PAYMENT_DONE: this.handlePaymentDone.bind(this),
      CHANGE_ADDRESS: this.handleChangeAddress.bind(this),
      CANCEL_ORDER: this.handleCancelOrder.bind(this),
      ORDER_HISTORY: this.handleOrderHistory.bind(this),
      OLDER_ORDERS: this.handleOlderOrders.bind(this),
      REORDER: this.handleReorder.bind(this),
      CONTACT_RESTAURANT: this.handleContactRestaurant.bind(this),
      VIEW_ORDER_DETAILS: this.handleViewOrderDetails.bind(this),
      HELP: this.handleHelp.bind(this)
    };

    // 消息内容到事件类型的映射
    this.messageToEventMap = {
      'Confirm Order': 'ORDER_PLACED',
      confirm_order: 'ORDER_PLACED',
      Restart: 'CANCEL_ORDER',
      cancel_order: 'CANCEL_ORDER',
      change_address: 'CHANGE_ADDRESS',
      pay_online: 'PAY_ONLINE',
      order_history: 'ORDER_HISTORY',
      older_orders: 'OLDER_ORDERS'
    };
  }
  /**
   * Handle cart update event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data containing cartData
   * @param {Object} updatedContext - Context object to update (passed by reference)
   */
  async handleCartUpdate(session, eventData, updatedContext) {
    const dialogueId = session.dialogueId;
    const cartData = eventData.cartData;
    if (!updatedContext) {
      throw new Error('updatedContext is required for handleCartUpdate');
    }
    try {
      // 在开头验证必要的字段
      if (!cartData.restaurantId) {
        throw new Error('Missing restaurantId in cart data');
      }
      if (cartData.isPickedUp === undefined) {
        throw new Error('Missing isPickedUp in cart data');
      }
      if (cartData.isPickedUp === false && !cartData.deliveryAddressId) {
        throw new Error('Missing deliveryAddressId for delivery order');
      }
      if (!cartData.orderInput || !Array.isArray(cartData.orderInput) || cartData.orderInput.length === 0) {
        throw new Error('Empty or invalid orderInput in cart data');
      }
      // Check if there's already an unpaid order
      if (updatedContext.orderPlaced && !updatedContext.paymentDone) {
        // Send message with current order info
        const { currentOrder, selectedRestaurantRef } = updatedContext;
        const orderSummary = orderFsmActions.orderFormat(
          currentOrder,
          updatedContext.currentOrderState || {},
          selectedRestaurantRef,
          updatedContext.isAddressSelected
        );
        const orderId = updatedContext.currentOrderState?.orderId;
        const stripeCheckoutUrl = `<${config.STRIPE.CHECKOUT_BASE_URL}?id=${orderId}&platform=ws>`;
        const processedOrderSummary = JSON.stringify(orderSummary).slice(1, -1);
        const combinedText =
          processedOrderSummary +
          '\\n\\n' +
          'You already have an unpaid order. Please complete payment for this order before placing a new one.' +
          '\\n' +
          stripeCheckoutUrl;
        await whatsappService.sendQuickReply(
          dialogueId,
          {
            text: combinedText,
            buttons: [{ text: 'Cancel Order', payload: 'cancel_order' }],
            header: `Unpaid Order ${orderId}`,
            footer: '    '
          },
          'dialog'
        );

        return;
      }

      // Get the latest context data
      const { customer } = updatedContext;

      // 1. 根据restaurantId查询餐厅信息
      const restaurant = restaurantStore.getRestaurantRef(cartData.restaurantId);
      if (!restaurant) {
        throw new Error(`Restaurant not found for ID: ${cartData.restaurantId}`);
      }

      // 更新selectedRestaurantRef和isRestaurantSelected
      updatedContext.selectedRestaurantRef = restaurant;
      updatedContext.isRestaurantSelected = true;
      logger.debug(`Updated restaurant: ${restaurant.id} - ${restaurant.name}`);

      // 2. 如果不是自取，更新地址信息
      if (!cartData.isPickedUp) {
        const addressId = cartData.deliveryAddressId; // 已验证存在
        const currentAddressIndex = updatedContext.selectedAddressIndex;
        const currentAddress =
          currentAddressIndex !== null && currentAddressIndex >= 0
            ? updatedContext.customer.addresses[currentAddressIndex]
            : null;

        if (currentAddress?.addressId !== addressId) {
          await this.updateDeliveryAddress(addressId, customer, updatedContext);
        } else {
          updatedContext.currentOrderState.deliveryAddress = currentAddress.formattedAddress;
        }
      }
      logger.debug(
        `Cart update: restaurant=${updatedContext.selectedRestaurantRef?.id}, address=${updatedContext.isAddressSelected}`
      );

      try {
        if (!updatedContext.selectedRestaurantRef) {
          throw new Error('No restaurant selected for cart update');
        }

        logger.debug('Using restaurant with complete info', {
          restId: updatedContext.selectedRestaurantRef.id,
          name: updatedContext.selectedRestaurantRef.name,
          hasCat: updatedContext.selectedRestaurantRef.categories?.length > 0,
          hasAddons: updatedContext.selectedRestaurantRef.addons?.length > 0
        });

        const restaurant = updatedContext.selectedRestaurantRef;

        // Flatten foods array from all categories
        const foods = restaurant.categories.map(c => c.foods).flat();
        const availableAddons = restaurant.addons || [];
        const availableOptions = restaurant.options || [];

        // Process items similar to placeOrder logic
        const processedItems = (cartData.orderInput || [])
          .map(item => {
            try {
              // Find the food item
              const food = foods.find(element => element._id.toString() === item.food);
              if (!food) {
                logger.warn(`Food not found: ${item.food}`, { dialogueId });
                return null;
              }

              // Find the variation
              const variation = food.variations.find(v => v._id.toString() === item.variation);
              if (!variation) {
                logger.warn(`Variation not found: ${item.variation} for food ${item.food}`, { dialogueId });
                return null;
              }

              // Process addons
              const addonList = [];
              if (item.addons && Array.isArray(item.addons)) {
                item.addons.forEach(data => {
                  if (!data) return;

                  // Process options
                  const selectedOptions = [];
                  if (data.options && Array.isArray(data.options)) {
                    data.options.forEach(option => {
                      const foundOption = availableOptions.find(op => op._id.toString() === option);
                      if (foundOption) {
                        selectedOptions.push(foundOption);
                      } else {
                        logger.warn(`Option not found: ${option}`, { dialogueId });
                      }
                    });
                  }

                  // Find the addon
                  const addon = availableAddons.find(a => a._id.toString() === data._id.toString());
                  if (addon) {
                    addonList.push({ ...(addon._doc || addon), options: selectedOptions });
                  } else {
                    logger.warn(`Addon not found: ${data._id}`, { dialogueId });
                  }
                });
              }

              // Create processed item (similar to Item model in placeOrder)
              // Ensure variation has required fields for orderVariationSchema
              const processedVariation = {
                _id: variation._id,
                title: variation.title,
                price: variation.price,
                discounted: variation.discounted || 0 // Ensure discounted field is always present
              };

              return {
                food: item.food,
                title: food.title,
                description: food.description,
                image: food.image,
                variation: processedVariation,
                addons: addonList,
                quantity: item.quantity,
                specialInstructions: item.specialInstructions
              };
            } catch (error) {
              logger.error('Error processing item', { err: error.message, item });
              return null;
            }
          })
          .filter(item => item !== null); // Remove any null items

        // 1. 计算商品总价（items subtotal）
        let itemsSubtotal = 0.0;
        processedItems.forEach(item => {
          if (!item.variation || typeof item.variation.price !== 'number' || isNaN(item.variation.price)) {
            throw new Error(`Invalid variation price for item: ${item.title}`);
          }

          let itemPrice = item.variation.price;
          const quantity = parseInt(item.quantity);

          if (isNaN(quantity) || quantity <= 0) {
            throw new Error(`Invalid quantity for item: ${item.title}`);
          }

          // Add addon prices with validation
          if (item.addons && item.addons.length > 0) {
            item.addons.forEach(addon => {
              if (addon.options && addon.options.length > 0) {
                addon.options.forEach(option => {
                  if (typeof option.price !== 'number' || isNaN(option.price)) {
                    throw new Error(`Invalid option price for item: ${item.title}`);
                  }
                  itemPrice = itemPrice + option.price;
                });
              }
            });
          }

          itemsSubtotal += itemPrice * quantity;
        });

        // Validate calculated items subtotal
        if (isNaN(itemsSubtotal) || itemsSubtotal < 0) {
          throw new Error(`Invalid calculated items subtotal: ${itemsSubtotal}`);
        }

        // 2. 获取配送方式和配送费 - 已在开头验证过isPickedUp字段
        let deliveryCharges = 0;
        if (cartData.isPickedUp) {
          // 自取订单不收取配送费
          deliveryCharges = 0;
        } else {
          // 有配送地址，根据deliveryCostType计算配送费
          const address = updatedContext.customer.addresses[updatedContext.selectedAddressIndex];
          const restaurantLat = restaurant.location?.coordinates?.[1];
          const restaurantLon = restaurant.location?.coordinates?.[0];
          const addressLat = address.coordinates?.coordinates?.[1] || address.coordinates?.latitude;
          const addressLon = address.coordinates?.coordinates?.[0] || address.coordinates?.longitude;
          // 计算距离（以公里为单位）
          const distance = calculateDistance(restaurantLat, restaurantLon, addressLat, addressLon);
          // 根据餐厅配置计算配送费
          if (restaurant.deliveryCostType === 'fixed') {
            // 固定费用模式
            deliveryCharges = restaurant.deliveryCostMin;
          } else {
            // 基于距离的费用模式
            const baseCost = restaurant.deliveryCostRate * distance;
            const minCost = restaurant.deliveryCostMin; // 默认最低配送费为3
            // 取基于距离的费用和最低费用中的较大值
            deliveryCharges = Math.max(baseCost, minCost);
          }
        }

        // 3. 获取税费
        let taxationAmount = 0;
        if (typeof cartData.taxationAmount === 'number' && !isNaN(cartData.taxationAmount)) {
          taxationAmount = cartData.taxationAmount;
        } else if (cartData.taxationAmount !== undefined) {
          throw new Error(`Invalid taxation amount: ${cartData.taxationAmount}`);
        }

        // 4. 获取小费 - 如果有值就赋值, 没有值就为0
        const numericTipping = Number(cartData.tipping);
        const tipping = numericTipping > 0 ? numericTipping : 0;
        // 无效的小费值不抛出错误，直接使用默认值0

        // 5. 计算总金额
        // 如果deliveryCharges为null（没有配送地址），则不包含在总金额中
        const rawOrderAmount = itemsSubtotal + (deliveryCharges || 0) + taxationAmount + tipping;

        // 验证最终的orderAmount
        if (isNaN(rawOrderAmount) || rawOrderAmount < 0) {
          throw new Error(`Invalid calculated order amount: ${rawOrderAmount}`);
        }

        // 格式化金额为两位小数，避免浮点数精度问题
        const orderAmount = parseFloat(rawOrderAmount.toFixed(2));

        // 6. 更新cartData，确保所有金额都格式化为两位小数
        cartData.processedItems = processedItems;
        cartData.itemsSubtotal = parseFloat(itemsSubtotal.toFixed(2));
        cartData.deliveryCharges = deliveryCharges !== null ? parseFloat(deliveryCharges.toFixed(2)) : null;
        cartData.taxationAmount = parseFloat(taxationAmount.toFixed(2));
        cartData.tipping = parseFloat(tipping.toFixed(2));
        cartData.orderAmount = orderAmount; // 已经格式化过了
        // isPickedUp已在开头验证过，保持原值

        logger.debug(`Processed cart data: items=${processedItems.length}, total=${orderAmount}`);
      } catch (processingError) {
        logger.error('Error processing cart data', {
          dialogueId,
          err: processingError.message,
          stack: processingError.stack
        });
        // Continue with original cart data if processing fails
        cartData.processedItems = [];
      }

      // 直接使用cartData更新currentOrder，完全按照orderInput结构
      updatedContext.currentOrder = {
        ...cartData, // 直接展开cartData的所有字段
        // 只覆盖需要的字段
        customerId: customer.customerId,
        paymentMethod: 'STRIPE',
        orderDate: new Date().toISOString(),
        isPickedUp: cartData.isPickedUp // 使用已验证的isPickedUp值
      };

      // Update currentOrderState with processed data and state information
      updatedContext.currentOrderState = {
        ...updatedContext.currentOrderState,
        // Store processed items for display
        processedItems: cartData.processedItems,
        // Set order status
        orderStatus: 'PENDING',
        paymentStatus: 'PENDING',
        lastUpdated: new Date().toISOString()
      };

      // orderFsmActions is already imported at the top of the file

      // Update tracking flags
      updatedContext.cartReceived = true;

      logger.debug(
        `Updated context: order=${!!updatedContext.currentOrder}, restaurant=${
          updatedContext.selectedRestaurantRef?.id
        }, address=${updatedContext.isAddressSelected}`
      );

      // Check if delivery address is already selected
      if (
        (updatedContext.isAddressSelected && updatedContext.currentOrderState.deliveryAddress) ||
        updatedContext.currentOrder.isPickedUp
      ) {
        // Address is already selected, send order summary with confirmation buttons
        const orderSummary = orderFsmActions.orderFormat(
          updatedContext.currentOrder,
          updatedContext.currentOrderState,
          updatedContext.selectedRestaurantRef,
          updatedContext.isAddressSelected
        );
        const processedOrderSummary = JSON.stringify(orderSummary).slice(1, -1);
        const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);
        await whatsappService.sendQuickReply(
          dialogueId,
          {
            text: processedOrderSummary + '\\n\\n' + 'Use the link below to modify your order:\\n<' + menuLink + '>',
            buttons: [
              { text: 'Confirm Order', payload: 'confirm_order' },
              { text: 'Restart', payload: 'Restart' }
            ],
            header: 'Order Confirmation',
            footer: '    '
          },
          'dialog'
        );

        // Order information is stored in session context, no need to update external order yet
      } else {
        // No address selected, first send order summary with quick reply options
        const orderSummary = orderFsmActions.orderFormat(
          updatedContext.currentOrder,
          updatedContext.currentOrderState,
          updatedContext.selectedRestaurantRef,
          updatedContext.isAddressSelected
        );

        // Send order summary as text
        await whatsappService.sendDialogueText(dialogueId, orderSummary);

        // Then ask for address
        await orderFsmActions.askForAddress({
          dialogueId,
          customer: updatedContext.customer
        });

        // Mark context as awaiting address
        updatedContext.awaitingAddress = true;
      }
    } catch (error) {
      logger.error(`Error in handleCartUpdate: ${error.message}`);
      // 发送错误消息给用户
      /* await whatsappService.sendDialogueText(
        dialogueId,
        `Sorry, there was an error processing your order: ${error.message}`
      );  */
      throw error;
    }
  }

  // handlePaymentComplete has been removed as its functionality is covered by handlePaymentDone

  /**
   * Update delivery address based on addressId
   * @param {string} addressId - The address ID to use
   * @param {Object} customer - Customer object with addresses
   * @param {Object} updatedContext - Context to update
   * @returns {Object|null} - The found address or null
   */
  /**
   * 更新送餐地址
   * @param {string} addressId - 地址ID
   * @param {Object} customer - 客户对象
   * @param {Object} updatedContext - 要更新的上下文
   * @returns {Object} - 找到的地址对象
   * @throws {Error} - 如果找不到地址则抛出错误
   */
  async updateDeliveryAddress(addressId, customer, updatedContext) {
    try {
      // 验证参数
      if (!addressId) {
        throw new Error('Address ID is required');
      }

      // 初始化变量
      let addressIndex = -1;
      let addressFound = false;

      // 步骤1: 在本地客户地址列表中查找
      if (customer?.addresses?.length > 0) {
        addressIndex = customer.addresses.findIndex(
          addr => addr.addressId && addr.addressId.toString() === addressId.toString()
        );

        if (addressIndex !== -1) {
          addressFound = true;
          logger.debug('Found address in local customer addresses', {
            addressId,
            addressIndex
          });
        }
      }

      // 步骤2: 如果本地没找到且有客户ID，尝试从数据库获取最新地址
      if (!addressFound && customer?.customerId) {
        try {
          const Customer = require('../../models/customer');
          const customerDoc = await Customer.findOne({ customerId: customer.customerId }).select({
            addresses: { $slice: [0, 20] }
          });

          // 如果数据库中有地址，更新本地地址列表
          if (customerDoc?.addresses?.length > 0) {
            logger.debug('Updating customer addresses from database', {
              customerId: customer.customerId,
              dbAddressCount: customerDoc.addresses.length,
              localAddressCount: customer.addresses?.length || 0
            });

            // 更新上下文中的地址列表
            if (!updatedContext.customer) updatedContext.customer = {};
            updatedContext.customer.addresses = customerDoc.addresses;

            // 同时更新传入的customer引用
            customer.addresses = updatedContext.customer.addresses;

            // 在更新后的地址列表中重新查找
            addressIndex = customer.addresses.findIndex(
              addr => addr.addressId && addr.addressId.toString() === addressId.toString()
            );

            if (addressIndex !== -1) {
              addressFound = true;
              logger.debug('Found address in updated customer addresses', {
                addressId,
                addressIndex,
                address: customer.addresses[addressIndex].formattedAddress
              });
            } else {
              logger.error(`Address not found in database: ${addressId}`);
            }
          }
        } catch (dbError) {
          // 记录数据库错误但继续执行，因为可能本地已有正确的地址
          logger.error('Error fetching address from database', {
            err: dbError.message,
            stack: dbError.stack,
            addressId,
            customerId: customer.customerId
          });
          // 不抛出异常，让流程继续
        }
      }

      // 步骤3: 如果经过上述步骤仍未找到地址，抛出错误
      if (!addressFound || addressIndex === -1) {
        const errorMsg = `Address not found for ID: ${addressId}`;
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // 步骤4: 找到地址，更新上下文
      const address = customer.addresses[addressIndex];

      // 更新上下文中的地址相关字段
      updatedContext.selectedAddressIndex = addressIndex;
      updatedContext.isAddressSelected = true;

      // 确保currentOrder已初始化并更新地址ID
      if (!updatedContext.currentOrder) updatedContext.currentOrder = {};
      updatedContext.currentOrder.deliveryAddressId = address.addressId;

      // 确保currentOrderState已初始化并更新地址信息
      if (!updatedContext.currentOrderState) updatedContext.currentOrderState = {};
      updatedContext.currentOrderState.deliveryAddress = address.formattedAddress;

      // 统一处理坐标 - 支持两种可能的格式
      const longitude = address.coordinates?.coordinates?.[0] || address.coordinates?.longitude || 0;
      const latitude = address.coordinates?.coordinates?.[1] || address.coordinates?.latitude || 0;

      // 设置deliveryCoordinates为标准格式
      updatedContext.currentOrderState.deliveryCoordinates = {
        type: 'Point',
        coordinates: [longitude, latitude]
      };

      logger.debug('Updated delivery address', {
        addressIndex,
        addressId: address.addressId,
        address: address.formattedAddress,
        coordinates: [longitude, latitude]
      });

      return address;
    } catch (error) {
      // 统一在函数末尾处理所有错误
      logger.error('Error in updateDeliveryAddress', {
        err: error.message,
        stack: error.stack,
        addressId
      });
      // 将所有错误都向上抛出，让调用方处理
      throw error;
    }
  }

  /**
   * Handle order placement
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleOrderPlaced(session, eventData, updatedContext) {
    const dialogueId = session.dialogueId;
    try {
      // 1. 准备调用 placeOrder resolver
      logger.debug('Preparing placeOrder resolver data', {
        dialogueId
      });

      // 验证updatedContext.currentOrder中的必要字段

      // 验证金额字段
      if (
        typeof updatedContext.currentOrder.orderAmount !== 'number' ||
        isNaN(updatedContext.currentOrder.orderAmount) ||
        updatedContext.currentOrder.orderAmount < 0
      ) {
        throw new Error(`Invalid orderAmount: ${updatedContext.currentOrder.orderAmount}`);
      }
      if (typeof updatedContext.currentOrder.tipping !== 'number' || isNaN(updatedContext.currentOrder.tipping)) {
        throw new Error(`Invalid tipping: ${updatedContext.currentOrder.tipping}`);
      }

      // 验证地址ID - 只有非自取订单才需要地址ID
      if (!updatedContext.currentOrder.isPickedUp && !updatedContext.currentOrder.deliveryAddressId) {
        throw new Error('Missing deliveryAddressId for delivery order');
      }

      // 验证其他必要字段
      if (!updatedContext.currentOrder.restaurantId) {
        throw new Error('Missing restaurantId in currentOrder');
      }

      if (!updatedContext.currentOrder.customerId) {
        throw new Error('Missing customerId in currentOrder');
      }

      if (!updatedContext.currentOrder.orderInput || !Array.isArray(updatedContext.currentOrder.orderInput)) {
        throw new Error('Invalid orderInput in currentOrder');
      }

      // 使用updatedContext.currentOrder作为args
      const args = updatedContext.currentOrder;

      logger.debug('Calling placeOrder resolver with args', {
        dialogueId,
        orderAmount: args.orderAmount,
        deliveryCharges: args.deliveryCharges,
        taxationAmount: args.taxationAmount,
        tipping: args.tipping,
        deliveryAddressId: args.deliveryAddressId
      });

      // 直接调用resolver
      const placeOrderResult = await resolvers.Mutation.placeOrderWhatsApp(null, args, { req: {}, res: {} });
      logger.debug('placeOrder resolver result', { placeOrderResult });

      const orderId = placeOrderResult.orderId;

      // 2. 生成订单摘要
      const orderSummary = orderFsmActions.orderFormat(
        updatedContext.currentOrder,
        updatedContext.currentOrderState,
        updatedContext.selectedRestaurantRef,
        updatedContext.isAddressSelected
      );

      // 3. 合并订单确认和支付链接为一条消息
      const stripeCheckoutUrl = `<${config.STRIPE.CHECKOUT_BASE_URL}?id=${orderId}&platform=ws>`;

      logger.debug('Generated Stripe checkout URL', {
        dialogueId,
        stripeCheckoutUrl,
        orderId, // Log the orderId explicitly
        orderIdType: typeof orderId // Check the type of orderId
      });

      // 发送合并后的消息
      const processedOrderSummary = JSON.stringify(orderSummary).slice(1, -1);
      await whatsappService.sendQuickReply(
        dialogueId,
        {
          text: `${processedOrderSummary}\\n\\nPlease complete your payment using the following link:\\n${stripeCheckoutUrl}`,
          buttons: [{ text: 'Manage', payload: 'HELP' }],
          header: `Order #${orderId} Created!`,
          footer: 'Your order will be processed after payment'
        },
        'dialog'
      );

      // 4. 直接更新传入的上下文
      updatedContext.currentOrderState = {
        ...updatedContext.currentOrderState,
        orderStatus: 'PENDING',
        paymentStatus: 'PENDING',
        orderId,
        paymentLink: stripeCheckoutUrl
      };
      updatedContext.orderPlaced = true;

      logger.debug('Context updated', { dialogueId, orderId, orderStatus: 'PENDING', paymentStatus: 'PENDING' });

      logger.info('Order placed successfully', { dialogueId, orderId });
    } catch (error) {
      logger.error('Error in handleOrderPlaced', { dialogueId, err: error.message, stack: error.stack });

      await whatsappService.sendDialogueText(
        dialogueId,
        'There was an issue processing your order. Please try again or contact customer support.'
      );
      throw error;
    }
  }
  /**
   * Handle payment done event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handlePaymentDone(session, eventData, updatedContext) {
    const dialogueId = session.dialogueId;
    try {
      // 获取支付数据
      const paymentData = eventData.data || eventData;
      const orderId = paymentData?.orderId;
      logger.info('Processing payment done event', {
        dialogueId,
        orderId,
        method: paymentData?.paymentMethod,
        status: paymentData?.paymentStatus
      });

      // Update context with payment information
      updatedContext.paymentDone = true;

      // Update payment status in currentOrderState
      updatedContext.currentOrderState.paymentStatus = paymentData?.status || 'completed';

      logger.debug('Order payment completed', { dialogueId, orderId });
      // Send confirmation message
      const orderSummary = orderFsmActions.orderFormat(
        updatedContext.currentOrder,
        updatedContext.currentOrderState || {},
        updatedContext.selectedRestaurantRef,
        updatedContext.isAddressSelected
      );

      const paymentMethod = paymentData?.method === 'COD' ? 'Cash on Delivery' : 'Online Payment';

      const confirmationMessage = `🎉 *${orderId} Payment Confirmed!*\n\n${orderSummary}\n\n💳 *Payment Method*: ${paymentMethod}\n\nThank you for your order! Your payment has been confirmed and your order is being processed.`;

      await whatsappService.sendDialogueText(dialogueId, confirmationMessage);

      // Store completed order in order history if needed
      if (!updatedContext.completedOrders) {
        updatedContext.completedOrders = [];
      }

      // Add current order to history before resetting
      updatedContext.completedOrders.push({
        orderId: updatedContext.currentOrderState.orderId,
        orderDate: updatedContext.currentOrder.orderDate,
        completedAt: new Date().toISOString(),
        paymentMethod: updatedContext.currentOrder.paymentMethod,
        orderAmount: updatedContext.currentOrder.orderAmount
      });

      // 从数据库中获取最新的customer信息并更新session.context.customer
      try {
        const Customer = require('../../models/customer');
        const customerId = updatedContext.customer?.customerId;

        if (customerId) {
          // 获取最新的customer信息
          const freshCustomer = await Customer.findOne({ customerId });

          if (freshCustomer) {
            // 只更新orderHistory相关字段，保留其他字段不变
            updatedContext.customer = {
              ...updatedContext.customer,
              orderHistory: freshCustomer.orderHistory || [],
              totalOrders: freshCustomer.totalOrders || 0,
              totalAmount: freshCustomer.totalAmount || 0
            };

            logger.debug('Updated session customer information', {
              dialogueId,
              customerId,
              orderHistoryLength: updatedContext.customer.orderHistory.length
            });
          }
        }
      } catch (error) {
        logger.error('Error updating session customer information', {
          dialogueId,
          error: error.message,
          stack: error.stack
        });
        // 不抛出错误，因为这不应该影响支付完成的主要流程
      }

      // Reset order state to allow new orders
      this._resetOrderState(updatedContext);

      logger.debug('Payment completed and order state reset', { dialogueId });
    } catch (error) {
      logger.error('Error in handlePaymentDone', { dialogueId, err: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * Process a session event
   * @param {Object} session - Session object
   * @param {Object} event - Event to process
   */
  async processSessionEvent(session, event) {
    try {
      // Update session with event data
      const updatedContext = { ...session.context };

      // Handle different event types
      switch (event.type) {
        case 'MESSAGE_RECEIVED':
          // 消息事件需要特殊处理，因为它需要解析消息内容并转换为标准事件
          await this.handleMessageReceived(session, event, updatedContext);
          break;
        case 'CART_SUBMITTED':
          // 通过统一的事件处理机制处理
          await this.processEvent(
            session,
            event.type,
            { cartData: event.data.cartData, dialogueId: session.dialogueId },
            updatedContext
          );
          break;
        case 'PAYMENT_COMPLETE':
          logger.info('Processing payment completion event', {
            dialogueId: session.dialogueId,
            orderId: event.data.orderId
          });
          await this.processEvent(
            session,
            event.type,
            { data: event.data, dialogueId: session.dialogueId },
            updatedContext
          );
          break;
        default:
          logger.warn(`Unknown event type: ${event.type}`);
      }

      // Update session with new context
      await sessionService.updateSession(session.dialogueId, {
        context: updatedContext,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      logger.error(`Error in processSessionEvent for ${session.dialogueId}`, {
        err: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * 统一的事件处理方法
   * @param {Object} session - Session object
   * @param {string} eventType - Event type        eventType = this.messageToEventMap[messageContent]
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async processEvent(session, eventType, eventData, updatedContext) {
    logger.debug(`Processing event: ${eventType}`, {
      dialogueId: session.dialogueId,
      eventType,
      eventDataKeys: Object.keys(eventData || {})
    });

    const handler = this.eventHandlers[eventType];
    if (handler) {
      // 直接传递 session 对象而不是 session.dialogueId
      await handler(session, eventData, updatedContext);
    } else {
      logger.warn(`No handler found for event type: ${eventType}`, { dialogueId: session.dialogueId });
    }
  }

  /**
   * Handle message received event
   * @param {Object} session - Session object
   * @param {Object} event - Event data
   * @param {Object} updatedContext - Updated context
   */
  async handleMessageReceived(session, event, updatedContext) {
    try {
      // Process the message content
      const { messageContent, messageType } = event.data;
      const dialogueId = session.dialogueId;
      logger.debug('Message received', { dialogueId, messageType, messageContent, sessionId: session.id });

      // 确定事件类型和数据
      let eventType = null;
      let eventData = { ...event.data, dialogueId };

      // 1. 首先检查是否有 externalID 需要特殊处理
      if (event.data.externalID) {
        const externalId = event.data.externalID;
        logger.debug('Processing message with externalId', { dialogueId, externalId });

        // 从 externalID 提取事件类型和数据
        if (externalId.startsWith('RS_')) {
          eventType = 'RESTAURANT_SELECTED';
          eventData.restaurantId = externalId.replace('RS_', '');
        } else if (externalId.startsWith('AU_')) {
          eventType = 'ADDRESS_UPDATED';
          const addressId = externalId.replace('AU_', '');
          eventData.address =
            updatedContext.customer?.addresses?.find(a => a.id === addressId)?.formattedAddress || messageContent;
        } else if (externalId === 'confirm_order') {
          eventType = 'ORDER_PLACED';
          logger.debug('Order placed', { dialogueId });
        } else if (externalId === 'Restart') {
          eventType = 'CANCEL_ORDER';
          logger.debug('Restart', { dialogueId });
        } else if (externalId === 'PAYMENT_DONE') {
          eventType = 'PAYMENT_DONE';
          eventData.data = messageContent;
        } else if (externalId.startsWith('RO_')) {
          eventType = 'REORDER';
          eventData.orderId = externalId.replace('RO_', '');
          logger.debug('Reorder request', { dialogueId, orderId: eventData.orderId });
        } else if (externalId.startsWith('CR_')) {
          eventType = 'CONTACT_RESTAURANT';
          eventData.restaurantId = externalId.replace('CR_', '');
          logger.debug('Contact restaurant request', { dialogueId, restaurantId: eventData.restaurantId });
        } else if (externalId === 'order_history') {
          eventType = 'ORDER_HISTORY';
          logger.debug('Order history request', { dialogueId });
        } else if (externalId === 'older_orders') {
          eventType = 'OLDER_ORDERS';
          logger.debug('Older orders request', { dialogueId });
        } else if (externalId.startsWith('OR_')) {
          eventType = 'VIEW_ORDER_DETAILS';
          eventData.orderId = externalId.replace('OR_', '');
          logger.debug('View order details request', { dialogueId, orderId: eventData.orderId });
        }
      }
      // 2. 然后检查消息内容
      else if (messageContent) {
        logger.debug('Processing interactive message with content', { dialogueId, messageContent });

        // 使用映射表将消息内容映射到事件类型
        eventType = this.messageToEventMap[messageContent];

        // 如果消息内容没有匹配到事件类型，将消息内容作为 externalId 处理
        if (!eventType) {
          logger.debug('Message content not mapped to event type, treating as externalId', {
            dialogueId,
            messageContent
          });

          // 将消息内容作为 externalId 处理
          if (messageContent.startsWith('RS_')) {
            eventType = 'RESTAURANT_SELECTED';
            eventData.restaurantId = messageContent.replace('RS_', '');
          } else if (messageContent.startsWith('AU_')) {
            eventType = 'ADDRESS_UPDATED';
            const addressId = messageContent.replace('AU_', '');
            eventData.address =
              updatedContext.customer?.addresses?.find(a => a.id === addressId)?.formattedAddress || messageContent;
          } else if (messageContent === 'confirm_order') {
            eventType = 'ORDER_PLACED';
          } else if (messageContent === 'PAYMENT_DONE') {
            eventType = 'PAYMENT_DONE';
          } else if (messageContent.startsWith('RO_')) {
            eventType = 'REORDER';
            eventData.orderId = messageContent.replace('RO_', '');
          } else if (messageContent.startsWith('CR_')) {
            eventType = 'CONTACT_RESTAURANT';
            eventData.restaurantId = messageContent.replace('CR_', '');
          } else if (messageContent.startsWith('OR_')) {
            eventType = 'VIEW_ORDER_DETAILS';
            eventData.orderId = messageContent.replace('OR_', '');
          }
        }
      }

      // 3. 如果确定了事件类型，则通过统一的事件处理机制处理
      if (eventType) {
        logger.debug(`Message converted to event: ${eventType}`, { dialogueId });
        await this.processEvent(session, eventType, eventData, updatedContext);
      } else {
        logger.debug('Message did not match any known event type, triggering HELP event', { dialogueId });
        // 对于未识别的消息，触发 HELP 事件
        await this.processEvent(session, 'HELP', eventData, updatedContext);
      }

      // 4. 更新上下文中的消息相关信息
      updatedContext.lastMessageReceived = new Date().toISOString();
      updatedContext.lastMessageContent = messageContent;
      updatedContext.lastMessageType = messageType;

      logger.debug('Context updated with message data', {
        dialogueId,
        lastMsgTime: updatedContext.lastMessageReceived,
        lastMsgType: updatedContext.lastMessageType
      });
    } catch (error) {
      logger.error(`Error handling message for ${session.dialogueId}:`, { err: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * Handle restaurant selected event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Updated context
   */
  async handleRestaurantSelected(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      const restaurantId = eventData.restaurantId;

      // Find restaurant in the store
      const restaurant = restaurantStore.getRestaurantRef(restaurantId);
      if (!restaurant) {
        logger.error(`Restaurant not found: ${restaurantId}`, { dialogueId });
        await whatsappService.sendDialogueText(
          dialogueId,
          'Sorry, we could not find the selected restaurant. Please try again.'
        );
        return;
      }

      // Update context with restaurant data
      updatedContext.selectedRestaurantRef = restaurant;
      updatedContext.isRestaurantSelected = true;

      // 如果有历史订单，从最新订单中获取地址作为默认地址
      if (updatedContext.customer?.orderHistory?.length > 0) {
        // 查找与所选餐厅相关的最新订单
        const latestOrder = updatedContext.customer.orderHistory.find(order => order.restaurantId === restaurant.id);

        // 如果找到订单且有地址信息，设置为默认地址
        if (latestOrder && latestOrder.deliveryAddressId) {
          // 查找对应的地址在数组中的索引
          const addressIndex = updatedContext.customer?.addresses?.findIndex(
            addr => addr.addressId === latestOrder.deliveryAddressId
          );

          // 如果找到有效的索引，设置为选中的地址
          if (addressIndex !== undefined && addressIndex >= 0) {
            updatedContext.selectedAddressIndex = addressIndex;
            updatedContext.isAddressSelected = true;

            // 更新订单状态中的地址信息
            const address = updatedContext.customer.addresses[addressIndex];
            if (!updatedContext.currentOrderState) updatedContext.currentOrderState = {};
            updatedContext.currentOrderState.deliveryAddress = address.formattedAddress;

            // 统一处理坐标
            const longitude = address.coordinates?.coordinates?.[0] || address.coordinates?.longitude || 0;
            const latitude = address.coordinates?.coordinates?.[1] || address.coordinates?.latitude || 0;

            updatedContext.currentOrderState.deliveryCoordinates = {
              type: 'Point',
              coordinates: [longitude, latitude]
            };

            logger.debug('Set default address from order history', {
              addressIndex,
              addressId: address.addressId,
              address: address.formattedAddress
            });
          }
        }
      }

      // Generate menu link - 使用 session.id 作为 token
      const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);

      // Send restaurant information with rich formatting
      const restaurantMessage = `🏪 *${restaurant.name}*\n\n📍 *Address*: ${restaurant.address}\n📞 *Phone*: ${
        restaurant.phone
      }\n🕒 *Hours*: ${restaurant.openingHours || 'Please call for hours'}\n\n🔗 *Order Now*: ${menuLink}`;

      await whatsappService.sendDialogueText(dialogueId, restaurantMessage);
    } catch (error) {
      const dialogueId = session.dialogueId;
      logger.error(`Error in handleRestaurantSelected for ${dialogueId}`, { err: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * Handle change address event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleChangeAddress(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      // Check if order is already placed
      if (updatedContext.orderPlaced) {
        await whatsappService.sendDialogueText(
          dialogueId,
          'Your order has already been placed. The delivery address cannot be changed at this time.'
        );
      } else {
        // Send address selection dialog
        await orderFsmActions.askForAddress({
          dialogueId,
          customer: updatedContext.customer
        });
      }
    } catch (error) {
      const dialogueId = session.dialogueId;
      logger.error('Error in handleChangeAddress', { dialogueId, err: error.message, stack: error.stack });
      throw error;
    }
  }
  /**
   * Handle cancel order event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleCancelOrder(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      // Reset order state using the common method
      this._resetOrderState(updatedContext);
      const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);

      await whatsappService.sendDialogueText(
        dialogueId,
        'Your order has been cancelled. You can place a new order using the link below.' + '\n\n<' + menuLink + '>'
      );
    } catch (error) {
      const dialogueId = session.dialogueId;
      logger.error('Error in handleCancelOrder', { dialogueId, err: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * Reset order state to allow new orders
   * @param {Object} context - Context to reset
   * @private
   */
  _resetOrderState(context) {
    // Create a reset currentOrder that maintains structure but clears data
    context.currentOrder = {
      ...context.currentOrder,
      // Clear order data
      orderInput: [],
      orderAmount: 0,
      orderId: null,
      paymentStatus: null,
      paidAmount: 0,
      orderStatus: null,

      // Preserve restaurant and customer info for convenience
      restaurantId: context.currentOrder.restaurantId,
      restaurantName: context.currentOrder.restaurantName,
      customerId: context.currentOrder.customerId,

      // Reset delivery info
      deliveryAddressId: null,
      deliveryCharges: 0,

      // Reset additional charges
      tipping: 0,
      taxationAmount: 0,

      // Clear other fields
      couponCode: null,
      instructions: null
    };

    // Reset currentOrderState
    context.currentOrderState = {
      orderId: null,
      orderStatus: null,
      paymentStatus: null,
      processedItems: [],
      paidAmount: 0,
      paymentLink: null,
      deliveryAddress: null,
      deliveryCoordinates: {
        type: 'Point',
        coordinates: [null, null]
      },
      isActive: true,
      lastUpdated: null
    };

    // Reset order flags
    context.orderPlaced = false;
    context.cartReceived = false;
    context.paymentDone = false;

    logger.debug('Order state reset', { customerId: context.currentOrder.customerId });
  }

  /**
   * Handle address updated event
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Updated context
   */
  async handleAddressUpdated(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      const address = eventData.address;

      // 查找地址在数组中的索引
      const addressIndex = updatedContext.customer?.addresses?.findIndex(addr => addr.addressId === address.addressId);

      // 如果找到有效的索引，更新为选中的地址
      if (addressIndex !== undefined && addressIndex >= 0) {
        // Update context with address data
        updatedContext.selectedAddressIndex = addressIndex;
        updatedContext.awaitingAddress = false;
        updatedContext.isAddressSelected = true;

        // Update currentOrder and currentOrderState with address data
        updatedContext.currentOrder.deliveryAddressId = address.addressId;
        updatedContext.currentOrderState.deliveryAddress = address.formattedAddress;
        updatedContext.currentOrderState.deliveryCoordinates = {
          type: 'Point',
          coordinates: [address.coordinates?.longitude, address.coordinates?.latitude]
        };
      } else {
        // 如果找不到地址索引，记录错误
        logger.warn('Address not found in customer addresses', {
          dialogueId,
          addressId: address.addressId,
          customerAddressCount: updatedContext.customer?.addresses?.length || 0
        });
        // 尝试添加地址到customer.addresses
        if (!updatedContext.customer.addresses) updatedContext.customer.addresses = [];
        updatedContext.customer.addresses.push(address);
        // 更新索引为新添加的地址
        updatedContext.selectedAddressIndex = updatedContext.customer.addresses.length - 1;
        updatedContext.isAddressSelected = true;

        // 更新订单信息
        updatedContext.currentOrder.deliveryAddressId = address.addressId;
        updatedContext.currentOrderState.deliveryAddress = address.formattedAddress;
        updatedContext.currentOrderState.deliveryCoordinates = {
          type: 'Point',
          coordinates: [address.coordinates?.longitude, address.coordinates?.latitude]
        };
      }

      // orderFsmActions is already imported at the top of the file

      // Check if order is already placed
      if (updatedContext.orderPlaced) {
        // Order already confirmed, can't change address
        const orderSummary = orderFsmActions.orderFormat(
          updatedContext.currentOrder,
          updatedContext.currentOrderState,
          updatedContext.selectedRestaurantRef,
          updatedContext.isAddressSelected
        );

        const orderMessage = `${orderSummary}\n\nYour order has already been placed. The delivery address cannot be changed at this time.`;

        await whatsappService.sendDialogueText(dialogueId, orderMessage);
      } else if (updatedContext.cartReceived) {
        // Cart received but order not confirmed yet, update address and send confirmation
        const orderSummary = orderFsmActions.orderFormat(
          updatedContext.currentOrder,
          updatedContext.currentOrderState,
          updatedContext.selectedRestaurantRef,
          updatedContext.isAddressSelected
        );

        await whatsappService.sendDialogueText(dialogueId, orderSummary);

        // Send quick reply options for order confirmation
        await whatsappService.sendQuickReply(
          dialogueId,
          {
            text: 'Would you like to confirm this order or make changes?',
            buttons: [
              { text: 'Confirm Order ✅', payload: 'confirm_order' },
              { text: 'Change Address 📍', payload: 'change_address' }
            ],
            header: 'Order Confirmation',
            footer: ''
          },
          'dialog'
        );

        // Order information is stored in session context, no need to update external order yet
      } else {
        // No cart yet, just update address and send confirmation
        await whatsappService.sendDialogueText(
          dialogueId,
          `Thank you! Your delivery address has been set to: ${address}`
        );

        // 不再重新获取session，直接使用传入的session对象

        // If restaurant is selected, send restaurant info and ordering link
        if (updatedContext.isRestaurantSelected && updatedContext.selectedRestaurantRef) {
          const restaurant = updatedContext.selectedRestaurantRef;
          const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);

          const restaurantMessage = `🏪 *${restaurant.name}*\n\n📍 *Address*: ${restaurant.address}\n📞 *Phone*: ${restaurant.phone}\n\n🔗 *Order Now*: ${menuLink}`;

          await whatsappService.sendDialogueText(dialogueId, restaurantMessage);
        }
      }
    } catch (error) {
      logger.error(`Error in handleAddressUpdated for ${session.dialogueId}`, { err: error.message, stack: error.stack });
      throw error;
    }
  }

  /**
   * Handle order history display
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   * @param {boolean} isInitial - Whether this is the initial display (true) or pagination (false)
   */
  async handleOrderHistoryDisplay(session, eventData, updatedContext, isInitial = true) {
    const dialogueId = session.dialogueId;
    try {
      const action = isInitial ? 'initial' : 'pagination';
      logger.debug(`Processing order history request (${action})`, { dialogueId });

      // 设置偏移量 - 只有初始显示时才更新上下文
      if (isInitial) {
        // 初始显示时重置偏移量
        updatedContext.orderHistoryOffset = 0;

        // 初始显示，使用上下文中的客户数据
        await orderFsmActions.showOrderHistory({
          dialogueId: dialogueId,
          customer: updatedContext.customer
        });
      } else {
        // 分页时增加偏移量，但不存入上下文
        const offset = (updatedContext.orderHistoryOffset || 0) + 10;

        // 分页请求时，直接从数据库获取最新数据，但不更新上下文
        try {
          const Customer = require('../../models/customer');
          const customerId = updatedContext.customer?.customerId;

          if (!customerId) {
            throw new Error('Customer ID not found in context');
          }

          // 获取最新的客户信息
          const freshCustomer = await Customer.findOne({ customerId });

          if (!freshCustomer) {
            throw new Error(`Customer not found with ID: ${customerId}`);
          }

          // 使用新获取的数据，但不更新上下文
          const orderHistory = freshCustomer.orderHistory || [];
          const nextOrders = orderHistory.slice(offset, offset + 10);

          if (nextOrders.length === 0) {
            await whatsappService.sendDialogueText(dialogueId, 'No more orders to display.');
            return;
          }

          // 格式化订单数据
          const orderItems = nextOrders.map(order => {
            const restaurantName = order.restaurantName || 'Unknown Restaurant';
            return orderFsmActions.formatOrderListItem(order, restaurantName);
          });

          // 发送订单列表
          await whatsappService.sendListPicker(
            dialogueId,
            {
              header: 'Order History (Continued)',
              text: 'Here are more of your previous orders:',
              footer: 'Select an order to view details',
              listName: 'Orders',
              listItems: orderItems
            },
            'dialog'
          );

          // 如果还有更多订单，添加"查看更多"按钮
          if (offset + 10 < orderHistory.length) {
            await whatsappService.sendQuickReply(
              dialogueId,
              {
                text: 'Would you like to see older orders?',
                buttons: [{ text: 'Older Orders', payload: 'older_orders' }],
                header: 'Order History',
                footer: ''
              },
              'dialog'
            );
          }

          // 只记录偏移量，不更新客户数据
          updatedContext.orderHistoryOffset = offset;
        } catch (dbError) {
          logger.error('Error fetching customer data for pagination', {
            dialogueId,
            err: dbError.message,
            stack: dbError.stack
          });

          // 数据库查询失败时，提示用户稍后再试
          await whatsappService.sendDialogueText(dialogueId, '系统暂时无法获取您的订单历史，请稍后再试。');
          return;
        }
      }

      logger.debug(`Order history displayed (${action})`, { dialogueId, offset: updatedContext.orderHistoryOffset });
    } catch (error) {
      logger.error(`Error in handleOrderHistoryDisplay (${isInitial ? 'initial' : 'pagination'})`, {
        dialogueId,
        err: error.message,
        stack: error.stack
      });

      // 发送错误消息给用户
      await whatsappService.sendDialogueText(
        dialogueId,
        'Sorry, we encountered an error while retrieving your order history. Please try again later.'
      );

      throw error;
    }
  }

  /**
   * Handle order history request (initial display)
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleOrderHistory(session, eventData, updatedContext) {
    return this.handleOrderHistoryDisplay(session, eventData, updatedContext, true);
  }

  /**
   * Handle older orders request (pagination)
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleOlderOrders(session, eventData, updatedContext) {
    return this.handleOrderHistoryDisplay(session, eventData, updatedContext, false);
  }

  /**
   * Handle reorder request
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data containing orderId
   * @param {Object} updatedContext - Context to update
   */
  async handleReorder(session, eventData, updatedContext) {
    const dialogueId = session.dialogueId;

    try {
      logger.debug('Processing reorder request', { dialogueId, orderId: eventData.orderId });

      // 1. 检查是否有未付款订单
      if (updatedContext.orderPlaced && !updatedContext.paymentDone) {
        // 有未付款订单，发送提醒消息
        const { currentOrder, selectedRestaurantRef } = updatedContext;
        const orderSummary = orderFsmActions.orderFormat(
          currentOrder,
          updatedContext.currentOrderState || {},
          selectedRestaurantRef,
          updatedContext.isAddressSelected
        );

        const orderId = updatedContext.currentOrderState?.orderId;
        const stripeCheckoutUrl = `<https://whatsapp.imyth.org/stripe/cs?id=${orderId}&platform=ws>`;
        const processedOrderSummary = JSON.stringify(orderSummary).slice(1, -1);
        const combinedText =
          processedOrderSummary +
          '\\n\\n' +
          'You already have an unpaid order. Please complete payment for this order before placing a new one.' +
          '\\n' +
          stripeCheckoutUrl;

        await whatsappService.sendQuickReply(
          dialogueId,
          {
            text: combinedText,
            buttons: [{ text: 'Cancel Order', payload: 'cancel_order' }],
            header: `Unpaid Order ${orderId}`,
            footer: '    '
          },
          'dialog'
        );

        return;
      }

      // 2. 从数据库获取完整订单信息
      const orderModel = require('../../models/order');
      const order = await orderModel.findById(eventData.orderId);

      if (!order) {
        await whatsappService.sendDialogueText(
          dialogueId,
          'Sorry, we could not find the order you want to reorder. Please try again.'
        );
        return;
      }

      logger.debug('Found order in database', {
        dialogueId,
        orderId: order._id,
        orderStatus: order.orderStatus,
        restaurantId: order.restaurantId,
        itemCount: order.items?.length || 0
      });

      // 3. 获取餐厅信息
      const restaurant = restaurantStore.getRestaurantRef(order.restaurantId);
      if (!restaurant) {
        await whatsappService.sendDialogueText(
          dialogueId,
          'Sorry, we could not find the restaurant for this order. Please try again.'
        );
        return;
      }

      // 4. 重置当前订单状态
      this._resetOrderState(updatedContext);

      // 5. 更新餐厅信息
      updatedContext.selectedRestaurantRef = restaurant;
      updatedContext.isRestaurantSelected = true;

      // 6. 处理订单商品
      const unavailableItems = [];
      const availableItems = [];

      // 获取餐厅所有商品的扁平列表
      const allFoods = restaurant.categories
        ? restaurant.categories.reduce((acc, category) => {
            return acc.concat(category.foods || []);
          }, [])
        : [];

      // 处理每个订单项
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          // 查找商品是否仍然存在
          const food = allFoods.find(f => f._id.toString() === item.food);
          if (!food) {
            unavailableItems.push(item.title || 'Unknown item');
            continue;
          }

          // 查找变体是否仍然存在
          const variation = food.variations.find(v => v._id.toString() === (item.variation?._id?.toString() || ''));
          if (!variation) {
            unavailableItems.push(`${item.title} (${item.variation?.title || 'selected variation'})`);
            continue;
          }

          // 创建orderInput格式的商品项
          const orderInputItem = {
            food: item.food,
            variation: variation._id.toString(),
            quantity: item.quantity,
            specialInstructions: item.specialInstructions || ''
          };

          // 处理加料选项
          if (item.addons && Array.isArray(item.addons) && item.addons.length > 0) {
            orderInputItem.addons = item.addons
              .map(addon => {
                if (!addon) return null;

                // 创建加料选项
                return {
                  _id: addon._id.toString(),
                  options: addon.options ? addon.options.map(opt => opt._id.toString()) : []
                };
              })
              .filter(addon => addon !== null);
          }

          availableItems.push(orderInputItem);
        }
      }

      // 7. 处理配送地址
      let deliveryAddressId = null;
      if (!order.isPickedUp && order.deliveryAddressId) {
        // 尝试在用户地址列表中找到匹配的地址
        const addressIndex = updatedContext.customer?.addresses?.findIndex(
          addr => addr.addressId === order.deliveryAddressId
        );

        if (addressIndex !== -1) {
          // 找到匹配的地址，更新上下文
          updatedContext.selectedAddressIndex = addressIndex;
          updatedContext.isAddressSelected = true;
          deliveryAddressId = order.deliveryAddressId;

          // 更新订单状态中的地址信息
          const address = updatedContext.customer.addresses[addressIndex];
          if (!updatedContext.currentOrderState) updatedContext.currentOrderState = {};
          updatedContext.currentOrderState.deliveryAddress = address.formattedAddress;

          // 统一处理坐标
          const longitude = address.coordinates?.coordinates?.[0] || address.coordinates?.longitude || 0;
          const latitude = address.coordinates?.coordinates?.[1] || address.coordinates?.latitude || 0;

          updatedContext.currentOrderState.deliveryCoordinates = {
            type: 'Point',
            coordinates: [longitude, latitude]
          };
        }
      }

      // 8. 创建新的currentOrder对象
      if (availableItems.length > 0) {
        // 创建符合placeOrder结构的currentOrder对象
        updatedContext.currentOrder = {
          restaurantId: restaurant.id,
          customerId: updatedContext.customer?.customerId,
          orderInput: availableItems,
          paymentMethod: 'STRIPE',
          deliveryAddressId: deliveryAddressId,
          tipping: order.tipping || 0,
          orderDate: new Date().toISOString(),
          isPickedUp: order.isPickedUp || false,
          taxationAmount: order.taxationAmount || 0,
          deliveryCharges: order.deliveryCharges || 0,
          instructions: order.instructions || ''
        };

        // 9. 处理订单金额
        // 计算商品小计
        let itemsSubtotal = 0;
        availableItems.forEach(item => {
          const food = allFoods.find(f => f._id.toString() === item.food);
          if (food) {
            const variation = food.variations.find(v => v._id.toString() === item.variation);
            if (variation) {
              itemsSubtotal += variation.price * item.quantity;
            }
          }
        });

        // 计算订单总金额
        const orderAmount =
          itemsSubtotal +
          (order.isPickedUp ? 0 : order.deliveryCharges || 0) +
          (order.taxationAmount || 0) +
          (order.tipping || 0);

        // 更新金额字段
        updatedContext.currentOrder.itemsSubtotal = parseFloat(itemsSubtotal.toFixed(2));
        updatedContext.currentOrder.orderAmount = parseFloat(orderAmount.toFixed(2));

        // 10. 更新currentOrderState
        updatedContext.currentOrderState = {
          ...updatedContext.currentOrderState,
          orderStatus: 'PENDING',
          paymentStatus: 'PENDING',
          lastUpdated: new Date().toISOString()
        };

        // 11. 发送订单确认
        const orderSummary = orderFsmActions.orderFormat(
          updatedContext.currentOrder,
          updatedContext.currentOrderState,
          updatedContext.selectedRestaurantRef,
          updatedContext.isAddressSelected
        );

        // 添加不可用商品的提示
        let messageText = orderSummary;
        if (unavailableItems.length > 0) {
          messageText +=
            '\n\n⚠️ *Some items from your previous order are no longer available:*\n' +
            unavailableItems.map(item => `• ${item}`).join('\n');
        }
        const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);

        // 发送订单确认消息
        if (updatedContext.isAddressSelected) {
          // 地址已选择，发送确认按钮
          const processedOrderSummary = JSON.stringify(messageText).slice(1, -1);
          await whatsappService.sendQuickReply(
            dialogueId,
            {
              text: processedOrderSummary + '\\n\\n' + 'Use the link below to modify your order:\\n<' + menuLink + '>',
              buttons: [
                { text: 'Confirm Order', payload: 'confirm_order' },
                { text: 'Restart', payload: 'Restart' }
              ],
              header: 'Reorder Confirmation',
              footer: '    '
            },
            'dialog'
          );
        } else {
          // 地址未选择，先发送订单摘要
          await whatsappService.sendDialogueText(dialogueId, messageText);

          // 然后请求地址
          await orderFsmActions.askForAddress({
            dialogueId,
            customer: updatedContext.customer
          });

          // 标记为等待地址
          updatedContext.awaitingAddress = true;
        }

        // 更新跟踪标志
        updatedContext.cartReceived = true;

        logger.debug('Reorder processed successfully', {
          dialogueId,
          availableItems: availableItems.length,
          unavailableItems: unavailableItems.length,
          orderAmount: updatedContext.currentOrder.orderAmount
        });
      } else {
        // 没有可用商品，无法重新下单
        await whatsappService.sendDialogueText(
          dialogueId,
          'Sorry, none of the items from your previous order are currently available. Please place a new order.'
        );

        // 生成菜单链接
        const menuLink = linkGenerator.generateMenuLink(session.id, session.brandRef?.orderURL);

        // 发送餐厅信息
        const restaurantMessage = `🏪 *${restaurant.name}*\n\nPlease use the link below to view the menu and place a new order:\n\n🔗 *Order Now*: ${menuLink}`;
        await whatsappService.sendDialogueText(dialogueId, restaurantMessage);
      }
    } catch (error) {
      logger.error('Error in handleReorder', { dialogueId, err: error.message, stack: error.stack });

      // 发送错误消息给用户
      await whatsappService.sendDialogueText(
        dialogueId,
        'Sorry, we encountered an error while processing your reorder request. Please try again later.'
      );

      throw error;
    }
  }

  /**
   * Handle contact restaurant request
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data containing restaurantId
   * @param {Object} updatedContext - Context to update
   */
  async handleContactRestaurant(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      logger.debug('Processing contact restaurant request', { dialogueId, restaurantId: eventData.restaurantId });

      // Find the restaurant
      const restaurant = restaurantStore.getRestaurantRef(eventData.restaurantId);

      if (!restaurant) {
        await whatsappService.sendDialogueText(
          dialogueId,
          'Sorry, we could not find the restaurant you want to contact. Please try again.'
        );
        return;
      }

      // Send restaurant contact information
      const contactMessage = `🏪 *${restaurant.name} Contact Information*\n\n📍 *Address*: ${
        restaurant.address
      }\n📞 *Phone*: ${restaurant.phone}\n⏰ *Hours*: ${
        restaurant.openingHours || 'Please call for hours'
      }\n\nYou can contact the restaurant directly using the phone number above.`;

      await whatsappService.sendDialogueText(dialogueId, contactMessage);

      logger.debug('Restaurant contact information sent', { dialogueId, restaurantId: restaurant.id });
    } catch (error) {
      const dialogueId = session.dialogueId;
      logger.error('Error in handleContactRestaurant', { dialogueId, err: error.message, stack: error.stack });

      // Send error message to user
      await whatsappService.sendDialogueText(
        dialogueId,
        'Sorry, we encountered an error while retrieving the restaurant contact information. Please try again later.'
      );

      throw error;
    }
  }

  /**
   * Handle view order details request
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data containing orderId
   * @param {Object} updatedContext - Context to update
   */
  async handleViewOrderDetails(session, eventData, updatedContext) {
    const dialogueId = session.dialogueId;
    try {
      if (!eventData.orderId) {
        await whatsappService.sendDialogueText(dialogueId, 'Please select an order to view its details.');
        return;
      }

      // Query the database for the order details
      const orderModel = require('../../models/order');
      const order = await orderModel.findById(eventData.orderId);

      // If order not found, show error message
      if (!order) {
        await whatsappService.sendDialogueText(
          dialogueId,
          `We couldn't find an order with ID ${eventData.orderId}. Please check the order ID and try again.`
        );
        return;
      }
      const formattedDate = orderFsmActions.formatDateWithShortMonth(order.orderDate);

      // Get currency symbol
      const currencySymbol = orderFsmActions.getCurrencySymbol();

      // Format items using only order data
      const formattedItems = order.items
        .map(item => {
          const quantity = item.quantity;
          const title = item.title;
          const variationTitle = item.variation?.title;
          const variationText = variationTitle ? ` (${variationTitle})` : '';
          const itemPrice = item.variation?.price || 0;

          // Format addons if present
          let addonText = '';
          if (item.addons && item.addons.length > 0) {
            addonText = item.addons
              .map(addon => {
                const optionText = addon.options
                  .map(opt => `${opt.title}    ${opt.price ? `  +${currencySymbol}${opt.price}` : ''}`)
                  .join(', ');
                return `\n- ${optionText}`;
              })
              .join('');
          }

          return ` - *${quantity}x ${title}${variationText}     ${currencySymbol}${itemPrice}*${addonText}`;
        })
        .join('\n');

      // Build the complete message
      const messageText =
        `📅 *Date*: ${formattedDate}\n` +
        `🏪 *Restaurant*: ${order.restaurantName}\n` +
        `📍 *Delivery*: ${order.isPickedUp ? 'Pickup Order' : order.deliveryAddress}\n\n` +
        `${formattedItems}\n\n` +
        `💰 *Subtotal*: ${currencySymbol}${order.orderAmount.toFixed(2)}\n` +
        `${!order.isPickedUp ? `💰 *Delivery*: ${currencySymbol}${order.deliveryCharges.toFixed(2)}\n` : ''}` +
        `${order.tipping > 0 ? `💰 *Tip*: ${currencySymbol}${order.tipping.toFixed(2)}\n` : ''}` +
        `${order.taxationAmount > 0 ? `💰 *Tax*: ${currencySymbol}${order.taxationAmount.toFixed(2)}\n` : ''}` +
        `💰 *Total*: ${currencySymbol}${(
          order.orderAmount +
          (order.deliveryCharges || 0) +
          (order.tipping || 0) +
          (order.taxationAmount || 0)
        ).toFixed(2)}\n` +
        `📊 *Status*: ${order.orderStatus}\n` +
        `💳 *Payment*: ${order.paymentMethod} (${order.paymentStatus})`;
      const formattedMessage = JSON.stringify(messageText).slice(1, -1);

      // Send order details with quick reply buttons
      await whatsappService.sendQuickReply(
        dialogueId,
        {
          text: formattedMessage,
          buttons: [
            { text: 'Order Again', payload: `RO_${eventData.orderId}` },
            { text: 'Contact Restaurant', payload: `CR_${order.restaurantId}` }
          ],
          header: `Order #${order.orderId}`,
          footer: '   '
        },
        'dialog'
      );
    } catch (error) {
      logger.error('Error in handleViewOrderDetails', { dialogueId, err: error.message, stack: error.stack });
      await whatsappService.sendDialogueText(
        dialogueId,
        'Sorry, we encountered an error while retrieving the order details. Please try again later.'
      );
      throw error;
    }
  }

  /**
   * Handle help request
   * @param {Object} session - Session object
   * @param {Object} eventData - Event data
   * @param {Object} updatedContext - Context to update
   */
  async handleHelp(session, eventData, updatedContext) {
    try {
      const dialogueId = session.dialogueId;
      logger.debug('Processing help request', { dialogueId });

      // 发送欢迎消息
      // 直接使用传入的 session 对象
      await orderFsmActions.sendWelcomeMessage(session, updatedContext);

      logger.debug('Welcome message sent for help request', { dialogueId });
    } catch (error) {
      const dialogueId = session.dialogueId;
      logger.error('Error in handleHelp', { dialogueId, err: error.message, stack: error.stack });

      // 发送错误消息给用户
      await whatsappService.sendDialogueText(
        dialogueId,
        'Sorry, we encountered an error while processing your request. Please try again later.'
      );

      throw error;
    }
  }
}

module.exports = new DialogManager();
