/**
 * Order FSM Actions and Helper Functions
 * This file contains only the functions that are actually used in the codebase
 */

const whatsappService = require('../services/whatsappService');
const logger = require('../../helpers/logger');
const { generateMenuLink, generateAddressLink } = require('../utils/linkGenerator');
const Configuration = require('../../models/configuration');
const config = require('../../config');

/**
 * Send welcome message to the customer
 * @param {Object} session - Session object
 * @param {Object} updatedContext - Context object to update (passed by reference)
 */
const sendWelcomeMessage = async (session, updatedContext) => {
  try {
    if (!updatedContext) {
      throw new Error('updatedContext is required for sendWelcomeMessage');
    }
    // Handle nested context structure
    const { brandRef, dialogueId } = session;

    const customer = updatedContext.customer;

    // Validate required context properties
    if (!customer || !brandRef) {
      logger.error('[Welcome Message Error] Invalid context structure', {
        hasCustomer: !!customer,
        hasBrandRef: !!brandRef,
        customerKeys: customer ? Object.keys(customer) : [],
        brandRefKeys: brandRef ? Object.keys(brandRef) : []
      });
      throw new Error('Invalid context for welcome message: missing customer or brand data');
    }
    const previousOrders = customer.orderHistory || [];
    let msgBody = `${brandRef.welcomeMessage || 'How can we help you today?'}`;

    // 不再在这里选择餐厅，因为已经在 session 初始化时完成了
    // 直接使用 updatedContext 中的餐厅信息

    // 如果已选择餐厅，添加餐厅信息和菜单链接
    if (updatedContext.isRestaurantSelected && updatedContext.selectedRestaurantRef) {
      const menuLink = generateMenuLink(session.id, brandRef.orderURL);
      msgBody += `\n\nCurrent restaurant: ${updatedContext.selectedRestaurantRef.name}`;
      msgBody += `\n📍 *Address*: ${updatedContext.selectedRestaurantRef.address}`;
      msgBody += `\n📞 *Phone*: ${updatedContext.selectedRestaurantRef.phone}`;
      msgBody += `\n⏰ *Hours*: ${updatedContext.selectedRestaurantRef.openingHours || 'Please call for hours'}`;
      msgBody += `\nFollow the link below to view menu and order:`;
      msgBody += `\n<${menuLink}>`;
    } else {
      msgBody += '\nPlease select a restaurant';
    }

    // 准备按钮
    const buttons = [];

    // 如果品牌有多个餐厅，添加"Change Restaurant"或"Select Restaurant"按钮
    if (brandRef.restaurants?.length > 1) {
      if (updatedContext.isRestaurantSelected) {
        buttons.push({ text: 'Change Restaurant', payload: 'REST_LIST' });
      } else {
        buttons.push({ text: 'Select Restaurant', payload: 'REST_LIST' });
      }
    }

    // 如果有历史订单，添加"Order History"按钮
    if (previousOrders.length > 0) {
      buttons.push({ text: 'Order History', payload: 'order_history' });
    }

    // 发送消息
    if (buttons.length === 0) {
      // 如果没有按钮，直接发送文本消息
      msgBody = `Welcome to ${brandRef.name}!\n\n${msgBody}`;
      await whatsappService.sendDialogueText(dialogueId, msgBody);
    } else {
      // 如果有按钮，发送快速回复
      msgBody = JSON.stringify(msgBody).slice(1, -1);
      await whatsappService.sendQuickReply(
        dialogueId,
        {
          text: msgBody,
          buttons: buttons,
          header: `Welcome to ${brandRef.name}`,
          footer: '     '
        },
        'dialog'
      );
    }
  } catch (error) {
    logger.error('[Welcome Message Error]', error);
    try {
      await whatsappService.sendDialogueText(
        session.dialogueId,
        `We're experiencing some technical difficulties. Please try again later.`
      );
    } catch (innerError) {
      logger.error('[Fallback Message Error]', innerError);
    }
    throw error;
  }
};
/**
 * Process order input to create displayable items
 * @param {Array} orderInput - Raw order input array
 * @param {Object} restaurant - Restaurant information with foods, addons, etc.
 * @returns {Array} - Processed items with detailed information
 */
const processOrderInput = (orderInput, restaurant) => {
  if (!orderInput || !Array.isArray(orderInput) || orderInput.length === 0) {
    return [];
  }

  if (!restaurant || !restaurant.categories) {
    logger.warn('Restaurant information not available for processing order input');
    return [];
  }

  // Flatten foods array from all categories
  const foods = restaurant.categories.map(c => c.foods).flat();
  const availableAddons = restaurant.addons || [];
  const availableOptions = restaurant.options || [];

  // Process each item in orderInput
  return orderInput
    .map(item => {
      try {
        // Find the food item
        const food = foods.find(element => element._id.toString() === item.food);
        if (!food) return null;

        // Find the variation
        const variation = food.variations.find(v => v._id.toString() === item.variation);
        if (!variation) return null;

        // Process addons
        const addonList = [];
        if (item.addons && Array.isArray(item.addons)) {
          item.addons.forEach(data => {
            if (!data) return;

            // Process options
            const selectedOptions = [];
            if (data.options && Array.isArray(data.options)) {
              data.options.forEach(option => {
                const foundOption = availableOptions.find(op => op._id.toString() === option);
                if (foundOption) {
                  selectedOptions.push(foundOption);
                }
              });
            }

            // Find the addon
            const addon = availableAddons.find(a => a._id.toString() === data._id.toString());
            if (addon) {
              addonList.push({
                ...(addon._doc || addon),
                options: selectedOptions
              });
            }
          });
        }

        // Create processed item with properly formatted variation
        // Ensure variation has required fields for orderVariationSchema
        const processedVariation = {
          _id: variation._id,
          title: variation.title,
          price: variation.price,
          discounted: variation.discounted || 0 // Ensure discounted field is always present
        };

        return {
          food: item.food,
          title: food.title,
          description: food.description,
          image: food.image,
          variation: processedVariation,
          addons: addonList,
          quantity: item.quantity,
          specialInstructions: item.specialInstructions
        };
      } catch (error) {
        logger.error('Error processing order input item', { error: error.message, item });
        return null;
      }
    })
    .filter(item => item !== null); // Remove any null items
};

// 货币配置信息
let currencySymbol = config.CURRENCY.DEFAULT_CURRENCY_SYMBOL;
let currency = config.CURRENCY.DEFAULT_CURRENCY;

/**
 * 初始化货币配置
 * 在应用启动时调用一次，从数据库加载配置
 */
const initCurrency = async () => {
  try {
    const configuration = await Configuration.findOne();

    if (configuration) {
      // 设置货币配置
      currency = configuration.currency || config.CURRENCY.DEFAULT_CURRENCY;
      currencySymbol = configuration.currencySymbol || config.CURRENCY.DEFAULT_CURRENCY_SYMBOL;

      logger.debug('Currency config loaded: currency=' + currency + ', currencySymbol=' + currencySymbol);
    } else {
      // 如果没有找到配置，使用默认值
      currency = config.CURRENCY.DEFAULT_CURRENCY;
      currencySymbol = config.CURRENCY.DEFAULT_CURRENCY_SYMBOL;

      logger.debug('No configuration found, using default currency', {
        currency,
        currencySymbol
      });
    }
  } catch (error) {
    logger.error('Error loading currency configuration', { error: error.message });
    // 出错时使用默认值
    currency = config.CURRENCY.DEFAULT_CURRENCY;
    currencySymbol = config.CURRENCY.DEFAULT_CURRENCY_SYMBOL;
  }
};

/**
 * Get currency symbol
 * @returns {string} - Currency symbol
 */
const getCurrencySymbol = () => {
  return currencySymbol;
};

/**
 * Format order information for display
 * @param {Object} currentOrder - Order object with placeOrder-compatible structure
 * @param {Object} currentOrderState - Order state object with processed items and status
 * @param {Object} restaurant - Restaurant information
 * @param {boolean} isAddressSelected - Whether the address is selected
 * @returns {string} - Formatted order information string
 */
const orderFormat = (currentOrder, currentOrderState, restaurant, isAddressSelected) => {
  // Check if we have a valid order
  if (!currentOrder) {
    return 'No order information available';
  }

  // Check if this is a database order object that needs transformation
  let formattedOrder = currentOrder;
  if (currentOrder._doc) {
    try {
      // If it's a database object, try to use transformOrder
      const { transformOrder } = require('../../graphql/resolvers/merge');
      formattedOrder = transformOrder(currentOrder);
    } catch (error) {
      logger.error('Error transforming order', { error: error.message });
      // Continue with original order if transformation fails
      formattedOrder = currentOrder;
    }
  }

  // Determine which items array to use
  // First try processedItems from currentOrderState, then process orderInput if needed
  let itemsArray = [];
  if (
    currentOrderState &&
    Array.isArray(currentOrderState.processedItems) &&
    currentOrderState.processedItems.length > 0
  ) {
    itemsArray = currentOrderState.processedItems;
  } else if (Array.isArray(formattedOrder.orderInput) && formattedOrder.orderInput.length > 0) {
    // Process orderInput to get displayable items
    itemsArray = processOrderInput(formattedOrder.orderInput, restaurant);
  }
  if (!Array.isArray(itemsArray) || itemsArray.length === 0) {
    return 'No items in order';
  }
  logger.debug('Order items', { itemsArray });

  // Get currency symbol
  const currencySymbol = getCurrencySymbol();

  // 使用传入的数据中的商品小计，确保格式化为两位小数
  const price = parseFloat(currentOrder.itemsSubtotal).toFixed(2);

  // Format items for display - 只负责格式化显示，不计算价格
  const formattedItems = itemsArray
    .map(item => {
      try {
        // Get basic item info
        const quantity = item.quantity;
        const title = item.title;
        const variationTitle = item.variation?.title || '';
        const variationText = variationTitle ? ` (${variationTitle})` : '';

        // 使用传入的数据中的价格
        let itemPrice = item.variation?.price;

        // Process addons for display only
        const addonDetails = [];
        if (item.addons && item.addons.length > 0) {
          item.addons.forEach(addon => {
            if (!addon) return;

            // Process options for display only
            const optionDetails = [];
            if (addon.options && addon.options.length > 0) {
              addon.options.forEach(option => {
                if (!option) return;
                optionDetails.push(option.title + '    ' + (option.price ? `  +${currencySymbol}${option.price}` : ''));
              });
            }

            // Add addon to details
            if (optionDetails.length > 0) {
              addonDetails.push(`\n- ${optionDetails.join(', ')}`);
            }
          });
        }

        // Format item string - 使用原始数据中的价格
        return ` - *${quantity}x ${title}${variationText}${
          itemPrice ? `     ${currencySymbol}${itemPrice}*` : ''
        }${addonDetails.join('')}`;
      } catch (error) {
        logger.error('Error processing item', { error: error.message, item });
        return `- ${item.quantity}x Error processing item`;
      }
    })
    .join('\n');

  // 直接使用传入的数据，但确保金额格式化为两位小数
  const isPickedUp = currentOrder.isPickedUp;
  const deliveryCharges =
    currentOrder.deliveryCharges !== null ? parseFloat(currentOrder.deliveryCharges).toFixed(2) : null;
  const taxationAmount = currentOrder.taxationAmount ? parseFloat(currentOrder.taxationAmount).toFixed(2) : null;
  const tipping = currentOrder.tipping ? parseFloat(currentOrder.tipping).toFixed(2) : null;
  const orderAmount = parseFloat(currentOrder.orderAmount).toFixed(2);

  // Build restaurant info
  let restaurantInfo = `\n\n🏪 *Restaurant*: ${restaurant.name}`;

  // Build delivery info
  let deliveryInfo = '';
  if (isPickedUp) {
    deliveryInfo = `\n📍 *Pickup from*: ${restaurant ? restaurant.address : 'Restaurant address'}`;
  } else if (isAddressSelected && currentOrderState.deliveryAddress) {
    deliveryInfo = `\n📍 *Delivery to*: ${currentOrderState.deliveryAddress}`;
  } else if (deliveryCharges === null) {
    deliveryInfo = `\n📍 *Delivery Address*: TBD (Please select an address)`;
  }

  // Build cost breakdown
  // 构建成本明细
  const costBreakdown = `\n\n💰 *Subtotal*: ${currencySymbol} ${price}`;
  const tipInfo = tipping ? `\n💰 *Tip*: ${currencySymbol} ${tipping}` : '';
  const taxInfo = taxationAmount ? `\n💰 *Tax*: ${currencySymbol} ${taxationAmount}` : '';

  // 处理配送费显示
  let deliveryFeeInfo = '';
  if (isPickedUp) {
    // 自取订单不显示配送费
    deliveryFeeInfo = '';
  } else if (deliveryCharges === null) {
    // 配送地址待定，显示TBD
    deliveryFeeInfo = `\n💰 *Delivery Fee*: TBD (Select address to calculate)`;
  } else {
    // 正常显示配送费
    deliveryFeeInfo = `\n💰 *Delivery Fee*: ${currencySymbol} ${deliveryCharges}`;
  }

  // Build order summary
  return `🛒 *Order Summary*\n\n${formattedItems}${costBreakdown}${tipInfo}${taxInfo}${deliveryFeeInfo}\n💰 *Total*: ${currencySymbol} ${orderAmount}${restaurantInfo}${deliveryInfo}`;
};

/**
 * Fetch customer information
 */
const fetchCustomerInfo = async (whatsappNumber, brandId) => {
  try {
    // Placeholder for actual customer info fetching logic
    // In a real implementation, this would query a database or API
    logger.debug('Fetching customer info:', { whatsappNumber, brandId });

    const { Query } = require('../../graphql/resolvers/customer');
    const customer = await Query.customerbyPhone(null, {
      phone: whatsappNumber,
      restaurantBrandId: brandId,
      orderoffset: 0,
      orderlimit: 10
    });
    logger.debug('get customer info', { whatsappNumber, brandId, customer });
    if (!customer) {
      logger.error(`No customer found with WhatsApp ID: ${whatsappNumber}`);
      return null;
    }
    return customer;
  } catch (error) {
    logger.error('Error fetching customer info:', error);
    throw error;
  }
};

/**
 * Fetch brand information
 */
const fetchBrandInfo = async brandWhatsappId => {
  try {
    const { Query } = require('../../graphql/resolvers/brand');
    const brand = await Query.brand(null, { brandWhatsappId });

    if (!brand) {
      logger.error(`No brand found with WhatsApp ID: ${brandWhatsappId}`);
      return null;
    }
    return brand;
  } catch (error) {
    logger.error('Error fetching brand info:', error);
    throw error;
  }
};

/**
 * 格式化日期为 YY-MM-DD HH:MM
 * @param {Date|string} date - 要格式化的日期
 * @returns {string} 格式化后的日期字符串
 */
const formatDateYYMMDDHHMM = date => {
  const orderDate = new Date(date);
  const year = orderDate.getFullYear().toString().slice(-2);
  const month = (orderDate.getMonth() + 1).toString().padStart(2, '0');
  const day = orderDate.getDate().toString().padStart(2, '0');
  const hours = orderDate.getHours().toString().padStart(2, '0');
  const minutes = orderDate.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

/**
 * 格式化日期为 YY-MMM-DD HH:MM（月份使用英文缩写）
 * @param {Date|string} date - 要格式化的日期
 * @returns {string} 格式化后的日期字符串
 */
const formatDateWithShortMonth = date => {
  const d = new Date(date);

  // 使用toLocaleString一次性获取所有部分
  const options = {
    year: '2-digit',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  };
  // 英文环境的日期格式通常是 "MMM DD, YY, HH:MM"
  return d.toLocaleString('en', options);
};


/**
 * 格式化订单列表项
 * @param {Object} order - 订单对象
 * @param {string} restaurantName - 餐厅名称
 * @param {number} index - 订单索引
 * @param {string} currencySymbol - 货币符号
 * @returns {Object} 格式化后的列表项
 */
const formatOrderListItem = (order, restaurantName, index, currencySymbol = '$') => {
  const formattedDate = formatDateWithShortMonth(order.orderDate);
  const { amount } = order;

  return {
    listItem: `Order_${index + 1}`,
    subListItem: `${restaurantName} ${currencySymbol}${amount.toFixed(2)} (${formattedDate})`,
    payload: `OR_${order.orderId}`
  };
};

/**
 * Show order history
 */
const showOrderHistory = async context => {
  try {
    const { dialogueId, customer } = context;

    // 使用传入的客户订单历史，已在 dialog.js 中从数据库获取最新数据
    // Check if customer has any orders
    if (!customer.orderHistory || customer.orderHistory.length === 0) {
      await whatsappService.sendDialogueText(
        dialogueId,
        "You don't have any previous orders with us. Place your first order to get started!"
      );
      return;
    }

    // Get the latest 10 orders from the customer's order history
    const orders = customer.orderHistory.slice(0, 10);

    // Get currency symbol
    const currencySymbol = getCurrencySymbol();

    // Format order history for display
    const orderItems = orders.map((order, index) => {
      return formatOrderListItem(order, order.restaurantName, index, currencySymbol);
    });

    // Send order history list
    await whatsappService.sendListPicker(
      dialogueId,
      {
        header: 'Order History',
        text: 'Last 10 orders, please select to check detail or take actions.',
        footer: 'Select an order to view details',
        listName: 'Orders',
        listItems: orderItems
      },
      'dialog'
    );
  } catch (error) {
    logger.error('[Order History Error]', error);
    await whatsappService.sendDialogueText(
      context.dialogueId,
      'Sorry, we encountered an error while retrieving your order history. Please try again later.'
    );
  }
};

module.exports = {
  // Only exporting functions that are actually used

  // Action functions
  //sendPaymentLink,
  sendWelcomeMessage,
  showOrderHistory,
  //sendManageAddress,

  // Utility functions
  orderFormat,
  fetchCustomerInfo,
  fetchBrandInfo,
  //sendAddressSelectionDialog,
  //askForAddress,
  getCurrencySymbol,
  initCurrency,

  // 格式化辅助函数
  formatDateYYMMDDHHMM,
  formatDateWithShortMonth,
  formatOrderListItem,
};
