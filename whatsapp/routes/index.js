/**
 * WhatsApp Integration Routes
 * Manages all WhatsApp related API endpoints
 *
 * @module whatsapp/routes/index
 */
const express = require('express');
const router = express.Router();
const webhookController = require('../controllers/webhookController');
const cartHandler = require('../cart');
const whatsappAuth = require('../middleware/whatsappAuth');
const sessionValidator = require('../middleware/sessionValidator');

// WhatsApp webhook route - handles all types of webhook events
router.post('/webhook', whatsappAuth, webhookController.handleIncoming);

// Cart submission route - handles web cart submission using session token
router.post('/submit-cart', sessionValidator, cartHandler.validatePayload, cartHandler.handleSubmit);

// Note: Address management has been moved to GraphQL API with X-WhatsAppW-Token authentication

module.exports = router;
