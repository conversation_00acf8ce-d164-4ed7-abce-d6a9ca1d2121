const { Client } = require('@googlemaps/google-maps-services-js');

// 创建一个单例 Google Maps 客户端
const client = new Client({});

// 配置选项
const config = {
  timeout: 1000, // 请求超时时间（毫秒）
  retries: 3, // 重试次数
  language: 'en', // 返回结果的语言
  region: 'IE' // 区域偏好（爱尔兰）
};

/**
 * 创建带有默认配置的 geocode 请求参数
 * @param {Object} params - 请求参数
 * @returns {Object} - 完整的请求参数
 */
const createGeocodeParams = params => ({
  ...params,
  key: process.env.GOOGLE_MAPS_API_KEY,
  language: config.language,
  region: config.region
});

module.exports = {
  client,
  config,
  createGeocodeParams
};
