#!/usr/bin/env node

/**
 * Performance Test Runner
 * 性能测试运行脚本
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('../test/performance/config');

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  type: 'all',        // all, load, stress, concurrency, scenarios
  level: 'medium',    // light, medium, heavy
  verbose: false,
  report: true,
  cleanup: true
};

// 解析命令行参数
args.forEach(arg => {
  if (arg.startsWith('--type=')) {
    options.type = arg.split('=')[1];
  } else if (arg.startsWith('--level=')) {
    options.level = arg.split('=')[1];
  } else if (arg === '--verbose') {
    options.verbose = true;
  } else if (arg === '--no-report') {
    options.report = false;
  } else if (arg === '--no-cleanup') {
    options.cleanup = false;
  } else if (arg === '--help') {
    showHelp();
    process.exit(0);
  }
});

function showHelp() {
  console.log(`
Performance Test Runner

Usage: node scripts/run-performance-tests.js [options]

Options:
  --type=<type>     Test type: all, load, stress, concurrency, scenarios (default: all)
  --level=<level>   Test level: light, medium, heavy (default: medium)
  --verbose         Enable verbose output
  --no-report       Disable report generation
  --no-cleanup      Disable cleanup after tests
  --help            Show this help message

Examples:
  node scripts/run-performance-tests.js --type=load --level=heavy
  node scripts/run-performance-tests.js --type=stress --verbose
  node scripts/run-performance-tests.js --type=scenarios --no-cleanup

Environment Variables:
  PERFORMANCE_TEST_LEVEL=quick    Run quick tests (reduced load)
  NODE_ENV=ci                     CI environment optimizations
  VERBOSE_TESTS=true              Enable verbose test output
  KEEP_TEST_DATA=true             Keep test data on failure
  STRIPE_TEST_SECRET_KEY          Enable real Stripe API tests
  PAYPAL_CLIENT_ID                Enable real PayPal API tests
  MONGO_TEST_URL                  Test database URL
  `);
}

async function main() {
  console.log('🚀 Starting Performance Tests...\n');
  
  // 验证配置
  const configValidation = config.validateConfig();
  if (!configValidation.valid) {
    console.error('❌ Configuration validation failed:');
    configValidation.errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  // 创建报告目录
  if (options.report) {
    const reportDir = config.reporting.file.outputDir;
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
  }

  // 设置环境变量
  const env = {
    ...process.env,
    NODE_ENV: 'test',
    PERFORMANCE_TEST_LEVEL: options.level,
    VERBOSE_TESTS: options.verbose.toString(),
    KEEP_TEST_DATA: (!options.cleanup).toString()
  };

  const testResults = {
    startTime: new Date(),
    tests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    }
  };

  try {
    // 运行指定类型的测试
    if (options.type === 'all') {
      await runTestSuite('load', env, testResults);
      await runTestSuite('concurrency', env, testResults);
      await runTestSuite('stress', env, testResults);
      await runTestSuite('scenarios', env, testResults);
    } else {
      await runTestSuite(options.type, env, testResults);
    }

    testResults.endTime = new Date();
    testResults.duration = testResults.endTime - testResults.startTime;

    // 生成报告
    if (options.report) {
      await generateReport(testResults);
    }

    // 显示总结
    showSummary(testResults);

    // 退出码
    process.exit(testResults.summary.failed > 0 ? 1 : 0);

  } catch (error) {
    console.error('❌ Performance tests failed:', error.message);
    process.exit(1);
  }
}

async function runTestSuite(testType, env, testResults) {
  console.log(`\n📊 Running ${testType} tests...`);
  
  const testFile = getTestFile(testType);
  if (!testFile) {
    console.log(`⚠️  Unknown test type: ${testType}`);
    return;
  }

  const startTime = Date.now();
  
  try {
    const result = await runJestTest(testFile, env);
    const endTime = Date.now();
    
    const testResult = {
      type: testType,
      file: testFile,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      duration: endTime - startTime,
      success: result.success,
      output: result.output,
      stats: parseTestOutput(result.output)
    };

    testResults.tests.push(testResult);
    testResults.summary.total++;
    
    if (result.success) {
      testResults.summary.passed++;
      console.log(`✅ ${testType} tests passed (${testResult.duration}ms)`);
    } else {
      testResults.summary.failed++;
      console.log(`❌ ${testType} tests failed (${testResult.duration}ms)`);
      if (options.verbose) {
        console.log(result.output);
      }
    }
  } catch (error) {
    testResults.summary.failed++;
    console.log(`❌ ${testType} tests error: ${error.message}`);
  }
}

function getTestFile(testType) {
  const testFiles = {
    load: 'test/performance/loadTest.js',
    stress: 'test/performance/stressTest.js',
    concurrency: 'test/performance/concurrencyTest.js',
    scenarios: 'test/performance/realWorldScenarios.js'
  };
  
  return testFiles[testType];
}

function runJestTest(testFile, env) {
  return new Promise((resolve) => {
    const jestArgs = [
      testFile,
      '--verbose',
      '--detectOpenHandles',
      '--forceExit'
    ];

    if (options.verbose) {
      jestArgs.push('--verbose');
    }

    const jest = spawn('npx', ['jest', ...jestArgs], {
      env,
      stdio: 'pipe'
    });

    let output = '';
    
    jest.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      if (options.verbose) {
        process.stdout.write(text);
      }
    });

    jest.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      if (options.verbose) {
        process.stderr.write(text);
      }
    });

    jest.on('close', (code) => {
      resolve({
        success: code === 0,
        output
      });
    });
  });
}

function parseTestOutput(output) {
  const stats = {
    tests: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    duration: 0
  };

  // 解析Jest输出
  const testMatch = output.match(/Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/);
  if (testMatch) {
    stats.failed = parseInt(testMatch[1]);
    stats.passed = parseInt(testMatch[2]);
    stats.tests = parseInt(testMatch[3]);
  }

  const timeMatch = output.match(/Time:\s+([\d.]+)\s*s/);
  if (timeMatch) {
    stats.duration = parseFloat(timeMatch[1]) * 1000;
  }

  return stats;
}

async function generateReport(testResults) {
  console.log('\n📋 Generating performance report...');
  
  const reportDir = config.reporting.file.outputDir;
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  // JSON报告
  const jsonReport = {
    ...testResults,
    config: {
      type: options.type,
      level: options.level,
      environment: process.env.NODE_ENV
    }
  };
  
  const jsonPath = path.join(reportDir, `performance-report-${timestamp}.json`);
  fs.writeFileSync(jsonPath, JSON.stringify(jsonReport, null, 2));
  
  // HTML报告
  const htmlReport = generateHtmlReport(jsonReport);
  const htmlPath = path.join(reportDir, `performance-report-${timestamp}.html`);
  fs.writeFileSync(htmlPath, htmlReport);
  
  console.log(`📄 Reports generated:`);
  console.log(`  JSON: ${jsonPath}`);
  console.log(`  HTML: ${htmlPath}`);
}

function generateHtmlReport(data) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .test-result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { border-left: 5px solid #4CAF50; }
        .failure { border-left: 5px solid #f44336; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { background: #f9f9f9; padding: 10px; border-radius: 3px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Performance Test Report</h1>
        <p><strong>Generated:</strong> ${data.startTime}</p>
        <p><strong>Duration:</strong> ${data.duration}ms</p>
        <p><strong>Test Type:</strong> ${data.config.type}</p>
        <p><strong>Test Level:</strong> ${data.config.level}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <strong>Total Tests:</strong> ${data.summary.total}
        </div>
        <div class="stat">
            <strong>Passed:</strong> ${data.summary.passed}
        </div>
        <div class="stat">
            <strong>Failed:</strong> ${data.summary.failed}
        </div>
        <div class="stat">
            <strong>Success Rate:</strong> ${((data.summary.passed / data.summary.total) * 100).toFixed(2)}%
        </div>
    </div>
    
    ${data.tests.map(test => `
        <div class="test-result ${test.success ? 'success' : 'failure'}">
            <h3>${test.type} Tests</h3>
            <p><strong>File:</strong> ${test.file}</p>
            <p><strong>Duration:</strong> ${test.duration}ms</p>
            <p><strong>Status:</strong> ${test.success ? '✅ Passed' : '❌ Failed'}</p>
            ${test.stats ? `
                <div class="stats">
                    <div class="stat">Tests: ${test.stats.tests}</div>
                    <div class="stat">Passed: ${test.stats.passed}</div>
                    <div class="stat">Failed: ${test.stats.failed}</div>
                </div>
            ` : ''}
        </div>
    `).join('')}
</body>
</html>
  `;
}

function showSummary(testResults) {
  console.log('\n📊 Performance Test Summary');
  console.log('================================');
  console.log(`Total Duration: ${testResults.duration}ms`);
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`Passed: ${testResults.summary.passed}`);
  console.log(`Failed: ${testResults.summary.failed}`);
  console.log(`Success Rate: ${((testResults.summary.passed / testResults.summary.total) * 100).toFixed(2)}%`);
  
  if (testResults.summary.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests.filter(t => !t.success).forEach(test => {
      console.log(`  - ${test.type}: ${test.file}`);
    });
  }
  
  console.log('\n✨ Performance testing completed!');
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { main, runTestSuite, generateReport };
