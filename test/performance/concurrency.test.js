/**
 * Concurrency Testing Suite
 * 测试系统在并发场景下的数据一致性和竞态条件
 */

const { createTestApp, cleanupTestApp } = require('../helpers/testApp');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { createOrder } = require('../factories/orderFactory');
const Order = require('../../models/order');
const Customer = require('../../models/customer');

describe('Concurrency Testing', () => {
  let testCustomer;
  let testRestaurant;

  beforeAll(async () => {
    await connectTestDB({ useRealDatabase: true });
    // 创建测试应用但不保存引用，因为我们主要测试数据库并发
    await createTestApp();
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
    
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    authToken = generateAuthToken(testCustomer._id);
  });

  describe('Order Creation Concurrency', () => {
    test('should handle concurrent order creation without data corruption', async () => {
      const concurrentOrders = 50;
      const orderPromises = [];

      // 创建50个并发订单 - 直接使用数据库操作测试并发性
      for (let i = 0; i < concurrentOrders; i++) {
        orderPromises.push(
          createOrder({
            user: testCustomer._id,
            restaurant: testRestaurant._id,
            orderId: `CONCURRENT_${i}_${Date.now()}`,
            orderAmount: 10.00 + i,
            orderStatus: 'PENDING',
            paymentStatus: 'PENDING',
            orderDate: new Date(),
            items: [{
              _id: `item_${i}`,
              title: `Test Item ${i}`,
              quantity: 1,
              price: 10.00 + i
            }]
          })
        );
      }

      const results = await Promise.all(orderPromises);

      // 验证所有订单都成功创建
      expect(results.length).toBe(concurrentOrders);
      results.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result.orderId).toBeDefined();
        expect(result.orderAmount).toBe(10.00 + index);
      });

      // 验证数据库中的订单数量
      const dbOrders = await Order.find({ user: testCustomer._id });
      expect(dbOrders.length).toBe(concurrentOrders);

      // 验证订单ID的唯一性
      const orderIds = dbOrders.map(order => order.orderId);
      const uniqueOrderIds = [...new Set(orderIds)];
      expect(uniqueOrderIds.length).toBe(concurrentOrders);
    });

    test('should handle concurrent order status updates correctly', async () => {
      // 创建一个订单
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderStatus: 'PENDING'
      });

      // 并发尝试更新订单状态 - 直接使用数据库操作
      const updatePromises = Array(3).fill().map((_, index) =>
        Order.findByIdAndUpdate(
          order._id,
          {
            orderStatus: 'ACCEPTED',
            acceptedAt: new Date(),
            estimatedTime: 30 + index * 5
          },
          { new: true }
        )
      );

      const results = await Promise.allSettled(updatePromises);

      // 所有更新都应该成功（MongoDB的原子操作）
      const successfulUpdates = results.filter(result =>
        result.status === 'fulfilled' && result.value
      );

      expect(successfulUpdates.length).toBe(3);

      // 验证最终状态的一致性
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.orderStatus).toBe('ACCEPTED');
      expect(finalOrder.acceptedAt).toBeDefined();
    });
  });

  describe('Payment Processing Concurrency', () => {
    test('should prevent duplicate payment processing', async () => {
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderAmount: 25.99,
        paymentStatus: 'PENDING'
      });

      // 并发尝试更新支付状态 - 模拟支付处理的并发控制
      const paymentPromises = Array(5).fill().map(() =>
        Order.findOneAndUpdate(
          {
            _id: order._id,
            paymentStatus: 'PENDING' // 只有状态为PENDING的订单才能被更新
          },
          {
            paymentStatus: 'PAID',
            paidAt: new Date()
          },
          { new: true }
        )
      );

      const results = await Promise.allSettled(paymentPromises);

      // 应该只有一个支付成功（第一个找到PENDING状态的）
      const successfulPayments = results.filter(result =>
        result.status === 'fulfilled' && result.value !== null
      );

      expect(successfulPayments.length).toBe(1);

      // 其他请求应该返回null（因为条件不匹配）
      const failedPayments = results.filter(result =>
        result.status === 'fulfilled' && result.value === null
      );

      expect(failedPayments.length).toBe(4);

      // 验证最终状态
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.paymentStatus).toBe('PAID');
      expect(finalOrder.paidAt).toBeDefined();
    });
  });

  describe('Database Transaction Concurrency', () => {
    test('should handle concurrent customer updates without race conditions', async () => {
      const customer = await createCustomer({
        name: 'Original Name',
        email: '<EMAIL>'
      });

      // 并发更新客户信息
      const updatePromises = Array(10).fill().map((_, index) =>
        Customer.findByIdAndUpdate(
          customer._id,
          { 
            name: `Updated Name ${index}`,
            updatedAt: new Date()
          },
          { new: true }
        )
      );

      const results = await Promise.all(updatePromises);
      
      // 所有更新都应该成功
      expect(results.length).toBe(10);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.name).toMatch(/Updated Name \d/);
      });

      // 验证最终状态
      const finalCustomer = await Customer.findById(customer._id);
      expect(finalCustomer.name).toMatch(/Updated Name \d/);
    });

    test('should handle concurrent order item updates correctly', async () => {
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        items: [
          { _id: 'item_1', title: 'Test Item', quantity: 1, price: 10.00 }
        ]
      });

      // 并发更新订单项目 - 直接使用数据库操作
      const updatePromises = Array(5).fill().map((_, index) =>
        Order.findByIdAndUpdate(
          order._id,
          {
            $set: {
              'items.0.quantity': index + 1,
              updatedAt: new Date()
            }
          },
          { new: true }
        )
      );

      const results = await Promise.allSettled(updatePromises);

      // 所有更新都应该成功
      const successfulUpdates = results.filter(result =>
        result.status === 'fulfilled' && result.value
      );

      expect(successfulUpdates.length).toBe(5);

      // 验证最终状态的一致性
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.items[0].quantity).toBeGreaterThan(0);
      expect(finalOrder.items[0].quantity).toBeLessThanOrEqual(5);
    });
  });

  describe('Session and Cache Concurrency', () => {
    test('should handle concurrent session operations', async () => {
      const sessionService = require('../../whatsapp/services/sessionService');
      
      const sessionPromises = Array(20).fill().map((_, index) => {
        const sessionId = `test-session-${index}`;
        return sessionService.createSession(sessionId, {
          customerId: testCustomer._id.toString(),
          restaurantId: testRestaurant._id.toString(),
          step: 'MENU_SELECTION'
        });
      });

      const results = await Promise.all(sessionPromises);
      
      // 所有会话都应该成功创建
      expect(results.length).toBe(20);
      results.forEach((result) => {
        expect(result).toBeDefined();
        expect(result.customerId).toBe(testCustomer._id.toString());
      });

      // 验证会话的唯一性
      const sessionIds = results.map(result => result.sessionId);
      const uniqueSessionIds = [...new Set(sessionIds)];
      expect(uniqueSessionIds.length).toBe(20);
    });
  });

  describe('Resource Locking Tests', () => {
    test('should handle inventory updates with proper locking', async () => {
      // 模拟库存更新的并发操作
      const initialInventory = 100;
      let currentInventory = initialInventory;
      const orderQuantities = Array(50).fill(2); // 50个订单，每个订单2个商品

      const inventoryPromises = orderQuantities.map(async (quantity) => {
        // 模拟检查库存和减少库存的原子操作
        return new Promise((resolve) => {
          setTimeout(() => {
            if (currentInventory >= quantity) {
              currentInventory -= quantity;
              resolve({ success: true, remaining: currentInventory });
            } else {
              resolve({ success: false, error: 'Insufficient inventory' });
            }
          }, Math.random() * 10); // 随机延迟模拟真实场景
        });
      });

      const results = await Promise.all(inventoryPromises);
      
      const successfulOrders = results.filter(result => result.success);
      const failedOrders = results.filter(result => !result.success);

      // 验证库存计算的正确性
      const totalConsumed = successfulOrders.length * 2;
      expect(currentInventory).toBe(initialInventory - totalConsumed);
      expect(currentInventory).toBeGreaterThanOrEqual(0);

      console.log('Inventory Test Results:', {
        initialInventory,
        finalInventory: currentInventory,
        successfulOrders: successfulOrders.length,
        failedOrders: failedOrders.length,
        totalConsumed
      });
    });
  });

  describe('Error Handling Under Concurrency', () => {
    test('should gracefully handle errors in concurrent operations', async () => {
      // 创建一些会导致错误的并发数据库操作
      const invalidOperations = Array(10).fill().map(() =>
        Order.create({
          // 缺少必需字段，应该失败
          restaurant: testRestaurant._id
          // 缺少 user, orderAmount 等必需字段
        }).catch(error => ({ error: error.message }))
      );

      const validOperations = Array(10).fill().map((_, index) =>
        createOrder({
          user: testCustomer._id,
          restaurant: testRestaurant._id,
          orderId: `VALID_${index}_${Date.now()}`,
          orderAmount: 10.00 + index,
          orderStatus: 'PENDING',
          paymentStatus: 'PENDING',
          orderDate: new Date(),
          items: [{
            _id: `item_${index}`,
            title: `Valid Item ${index}`,
            quantity: 1,
            price: 10.00 + index
          }]
        }).catch(error => ({ error: error.message }))
      );

      const allOperations = [...invalidOperations, ...validOperations];
      const results = await Promise.allSettled(allOperations);

      // 验证错误操作被正确处理
      const errors = results.filter(result =>
        result.status === 'fulfilled' && result.value && result.value.error
      );
      expect(errors.length).toBe(10);

      // 验证有效操作成功处理
      const successes = results.filter(result =>
        result.status === 'fulfilled' && result.value && !result.value.error
      );
      expect(successes.length).toBe(10);

      // 验证数据库仍然稳定 - 检查有效订单是否正确保存
      const savedOrders = await Order.find({ user: testCustomer._id });
      expect(savedOrders.length).toBeGreaterThanOrEqual(10);
    });
  });
});
