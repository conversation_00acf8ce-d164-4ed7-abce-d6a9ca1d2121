/**
 * Real World Scenarios Testing
 * 模拟真实世界的用户行为和业务场景
 */

const request = require('supertest');
const app = require('../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { generateAuthToken } = require('../helpers/authHelper');

describe('Real World Scenarios Testing', () => {
  let testCustomers = [];
  let testRestaurants = [];
  let authTokens = [];

  beforeAll(async () => {
    await connectTestDB({ useRealDatabase: true });
    
    // 创建多个测试用户和餐厅
    for (let i = 0; i < 20; i++) {
      const customer = await createCustomer({
        name: `Customer ${i}`,
        email: `customer${i}@example.com`
      });
      testCustomers.push(customer);
      authTokens.push(generateAuthToken(customer._id));
    }

    for (let i = 0; i < 5; i++) {
      const restaurant = await createRestaurant({
        name: `Restaurant ${i}`,
        isActive: true
      });
      testRestaurants.push(restaurant);
    }
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('Peak Hour Simulation', () => {
    test('should handle lunch rush hour scenario', async () => {
      const lunchRushStart = Date.now();
      const orderPromises = [];
      
      // 模拟午餐高峰期：30分钟内100个订单
      for (let i = 0; i < 100; i++) {
        const customerIndex = i % testCustomers.length;
        const restaurantIndex = i % testRestaurants.length;
        
        const orderData = {
          restaurantId: testRestaurants[restaurantIndex]._id.toString(),
          customerId: testCustomers[customerIndex]._id.toString(),
          items: [
            {
              foodId: `lunch_item_${i % 10}`,
              quantity: Math.floor(Math.random() * 3) + 1,
              price: 8.99 + (Math.random() * 10)
            }
          ],
          deliveryAddress: `Office Building ${Math.floor(i / 10) + 1}`,
          paymentMethod: Math.random() > 0.5 ? 'CARD' : 'CASH'
        };

        // 随机延迟模拟真实的订单时间分布
        const delay = Math.random() * 1800000; // 0-30分钟随机延迟
        
        orderPromises.push(
          new Promise(resolve => {
            setTimeout(async () => {
              try {
                const response = await request(app)
                  .post('/api/orders')
                  .set('Authorization', `Bearer ${authTokens[customerIndex]}`)
                  .send(orderData);
                resolve({ success: true, response, orderIndex: i });
              } catch (error) {
                resolve({ success: false, error, orderIndex: i });
              }
            }, delay);
          })
        );
      }

      const results = await Promise.all(orderPromises);
      const lunchRushEnd = Date.now();
      const totalDuration = lunchRushEnd - lunchRushStart;

      const successfulOrders = results.filter(r => r.success);
      const failedOrders = results.filter(r => !r.success);

      expect(successfulOrders.length).toBeGreaterThan(90); // 90%成功率
      expect(totalDuration).toBeLessThan(1900000); // 31.5分钟内完成
      
      console.log('Lunch Rush Simulation:', {
        totalOrders: 100,
        successful: successfulOrders.length,
        failed: failedOrders.length,
        duration: `${(totalDuration / 1000 / 60).toFixed(2)} minutes`,
        successRate: `${(successfulOrders.length / 100 * 100).toFixed(2)}%`
      });
    });

    test('should handle dinner rush with complex orders', async () => {
      const dinnerOrders = [];
      
      // 模拟晚餐高峰期：更复杂的订单，多个商品
      for (let i = 0; i < 50; i++) {
        const customerIndex = i % testCustomers.length;
        const restaurantIndex = i % testRestaurants.length;
        
        const itemCount = Math.floor(Math.random() * 5) + 2; // 2-6个商品
        const items = [];
        
        for (let j = 0; j < itemCount; j++) {
          items.push({
            foodId: `dinner_item_${j}`,
            quantity: Math.floor(Math.random() * 2) + 1,
            price: 12.99 + (Math.random() * 15),
            specialInstructions: Math.random() > 0.7 ? `Special request ${j}` : undefined
          });
        }

        dinnerOrders.push({
          customerIndex,
          restaurantIndex,
          orderData: {
            restaurantId: testRestaurants[restaurantIndex]._id.toString(),
            customerId: testCustomers[customerIndex]._id.toString(),
            items,
            deliveryAddress: `Home Address ${customerIndex}`,
            paymentMethod: 'CARD',
            deliveryCharges: 3.99,
            taxAmount: items.reduce((sum, item) => sum + item.price * item.quantity, 0) * 0.08
          }
        });
      }

      const startTime = Date.now();
      const orderPromises = dinnerOrders.map(async (order, index) => {
        try {
          const response = await request(app)
            .post('/api/orders')
            .set('Authorization', `Bearer ${authTokens[order.customerIndex]}`)
            .send(order.orderData);
          
          return { success: true, response, orderIndex: index };
        } catch (error) {
          return { success: false, error, orderIndex: index };
        }
      });

      const results = await Promise.all(orderPromises);
      const endTime = Date.now();

      const successfulOrders = results.filter(r => r.success);
      const averageOrderValue = successfulOrders.reduce((sum, order) => {
        return sum + order.response.body.order.orderAmount;
      }, 0) / successfulOrders.length;

      expect(successfulOrders.length).toBeGreaterThan(45); // 90%成功率
      expect(averageOrderValue).toBeGreaterThan(30); // 平均订单价值 > $30
      expect(endTime - startTime).toBeLessThan(60000); // 1分钟内完成

      console.log('Dinner Rush Simulation:', {
        totalOrders: 50,
        successful: successfulOrders.length,
        averageOrderValue: `$${averageOrderValue.toFixed(2)}`,
        duration: `${(endTime - startTime) / 1000}s`
      });
    });
  });

  describe('Customer Journey Simulation', () => {
    test('should handle complete customer journey under load', async () => {
      const journeyPromises = testCustomers.slice(0, 10).map(async (customer, index) => {
        const authToken = authTokens[index];
        const restaurant = testRestaurants[index % testRestaurants.length];
        
        try {
          // 1. 浏览餐厅菜单
          const menuResponse = await request(app)
            .get(`/api/restaurants/${restaurant._id}/menu`)
            .set('Authorization', `Bearer ${authToken}`);

          // 2. 创建订单
          const orderResponse = await request(app)
            .post('/api/orders')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              restaurantId: restaurant._id.toString(),
              customerId: customer._id.toString(),
              items: [
                {
                  foodId: `journey_item_${index}`,
                  quantity: 2,
                  price: 15.99
                }
              ],
              deliveryAddress: `Customer ${index} Address`,
              paymentMethod: 'CARD'
            });

          const orderId = orderResponse.body.order._id;

          // 3. 处理支付
          const paymentResponse = await request(app)
            .post('/api/payments/process')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              orderId,
              method: 'STRIPE',
              amount: orderResponse.body.order.orderAmount,
              currency: 'USD',
              paymentData: { paymentMethodId: 'pm_test_123' }
            });

          // 4. 更新订单状态（模拟餐厅操作）
          const statusUpdates = ['ACCEPTED', 'PREPARING', 'READY', 'OUT_FOR_DELIVERY', 'DELIVERED'];
          
          for (const status of statusUpdates) {
            await request(app)
              .put(`/api/orders/${orderId}/status`)
              .set('Authorization', `Bearer ${authToken}`)
              .send({ 
                status,
                estimatedTime: status === 'ACCEPTED' ? 30 : undefined,
                riderId: status === 'OUT_FOR_DELIVERY' ? `rider_${index}` : undefined
              });
            
            // 模拟状态更新之间的时间间隔
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // 5. 检查最终订单状态
          const finalOrderResponse = await request(app)
            .get(`/api/orders/${orderId}`)
            .set('Authorization', `Bearer ${authToken}`);

          return {
            success: true,
            customerId: customer._id,
            orderId,
            finalStatus: finalOrderResponse.body.order.orderStatus,
            totalAmount: orderResponse.body.order.orderAmount
          };
        } catch (error) {
          return {
            success: false,
            customerId: customer._id,
            error: error.message
          };
        }
      });

      const journeyResults = await Promise.all(journeyPromises);
      const successfulJourneys = journeyResults.filter(r => r.success);
      const completedOrders = successfulJourneys.filter(r => r.finalStatus === 'DELIVERED');

      expect(successfulJourneys.length).toBeGreaterThan(8); // 80%成功率
      expect(completedOrders.length).toBeGreaterThan(8); // 80%完成率

      console.log('Customer Journey Simulation:', {
        totalJourneys: 10,
        successful: successfulJourneys.length,
        completed: completedOrders.length,
        averageOrderValue: `$${(successfulJourneys.reduce((sum, j) => sum + j.totalAmount, 0) / successfulJourneys.length).toFixed(2)}`
      });
    });
  });

  describe('Multi-Restaurant Scenario', () => {
    test('should handle orders across multiple restaurants simultaneously', async () => {
      const multiRestaurantPromises = [];
      
      // 每个餐厅同时处理多个订单
      testRestaurants.forEach((restaurant, restaurantIndex) => {
        for (let orderIndex = 0; orderIndex < 10; orderIndex++) {
          const customerIndex = (restaurantIndex * 10 + orderIndex) % testCustomers.length;
          
          multiRestaurantPromises.push(
            request(app)
              .post('/api/orders')
              .set('Authorization', `Bearer ${authTokens[customerIndex]}`)
              .send({
                restaurantId: restaurant._id.toString(),
                customerId: testCustomers[customerIndex]._id.toString(),
                items: [
                  {
                    foodId: `multi_item_${restaurantIndex}_${orderIndex}`,
                    quantity: 1,
                    price: 10.99 + restaurantIndex
                  }
                ],
                deliveryAddress: `Multi Address ${orderIndex}`,
                paymentMethod: 'CARD'
              })
              .then(response => ({
                success: true,
                restaurantIndex,
                orderIndex,
                orderId: response.body.order._id
              }))
              .catch(error => ({
                success: false,
                restaurantIndex,
                orderIndex,
                error: error.message
              }))
          );
        }
      });

      const results = await Promise.all(multiRestaurantPromises);
      const successfulOrders = results.filter(r => r.success);
      
      // 按餐厅分组统计
      const ordersByRestaurant = {};
      successfulOrders.forEach(order => {
        if (!ordersByRestaurant[order.restaurantIndex]) {
          ordersByRestaurant[order.restaurantIndex] = 0;
        }
        ordersByRestaurant[order.restaurantIndex]++;
      });

      expect(successfulOrders.length).toBeGreaterThan(40); // 80%成功率
      
      // 每个餐厅都应该有订单
      Object.keys(ordersByRestaurant).forEach(restaurantIndex => {
        expect(ordersByRestaurant[restaurantIndex]).toBeGreaterThan(7); // 每个餐厅至少70%成功
      });

      console.log('Multi-Restaurant Simulation:', {
        totalOrders: testRestaurants.length * 10,
        successful: successfulOrders.length,
        ordersByRestaurant
      });
    });
  });

  describe('Payment Processing Scenarios', () => {
    test('should handle mixed payment methods under load', async () => {
      const paymentMethods = ['STRIPE', 'PAYPAL', 'CASH'];
      const paymentPromises = [];

      for (let i = 0; i < 30; i++) {
        const method = paymentMethods[i % paymentMethods.length];
        const customerIndex = i % testCustomers.length;
        
        paymentPromises.push(
          request(app)
            .post('/api/payments/process')
            .set('Authorization', `Bearer ${authTokens[customerIndex]}`)
            .send({
              orderId: `payment-test-${i}`,
              method,
              amount: 25.99 + (i * 0.5),
              currency: 'USD',
              paymentData: method === 'STRIPE' ? { paymentMethodId: 'pm_test_123' } :
                          method === 'PAYPAL' ? { orderID: `paypal_${i}` } : {}
            })
            .then(response => ({
              success: true,
              method,
              transactionId: response.body.transactionId
            }))
            .catch(error => ({
              success: false,
              method,
              error: error.message
            }))
        );
      }

      const results = await Promise.all(paymentPromises);
      const successfulPayments = results.filter(r => r.success);
      
      // 按支付方式分组统计
      const paymentsByMethod = {};
      successfulPayments.forEach(payment => {
        if (!paymentsByMethod[payment.method]) {
          paymentsByMethod[payment.method] = 0;
        }
        paymentsByMethod[payment.method]++;
      });

      expect(successfulPayments.length).toBeGreaterThan(25); // 83%成功率
      
      // 每种支付方式都应该有成功的交易
      paymentMethods.forEach(method => {
        expect(paymentsByMethod[method]).toBeGreaterThan(7); // 每种方式至少70%成功
      });

      console.log('Mixed Payment Methods Simulation:', {
        totalPayments: 30,
        successful: successfulPayments.length,
        paymentsByMethod
      });
    });
  });
});
