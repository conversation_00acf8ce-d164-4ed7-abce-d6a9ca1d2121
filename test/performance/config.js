/**
 * Performance Testing Configuration
 * 性能测试的配置文件
 */

module.exports = {
  // 负载测试配置
  loadTest: {
    // 轻负载测试
    light: {
      connections: 10,
      duration: 10,
      maxLatency: 200,
      minThroughput: 50
    },
    // 中等负载测试
    medium: {
      connections: 50,
      duration: 30,
      maxLatency: 500,
      minThroughput: 100
    },
    // 重负载测试
    heavy: {
      connections: 100,
      duration: 60,
      maxLatency: 1000,
      minThroughput: 150
    }
  },

  // 压力测试配置
  stressTest: {
    // 极限连接数
    extremeConnections: 500,
    // 突发流量测试
    burstTraffic: {
      connections: 200,
      duration: 10,
      bursts: 3,
      intervalBetweenBursts: 5000
    },
    // 内存压力测试
    memoryPressure: {
      connections: 50,
      duration: 20,
      maxMemoryIncreaseMB: 500
    }
  },

  // 并发测试配置
  concurrencyTest: {
    // 并发订单创建
    orderCreation: {
      concurrentOrders: 50,
      maxErrorRate: 0.05 // 5%
    },
    // 并发状态更新
    statusUpdate: {
      concurrentUpdates: 10,
      allowedSuccessfulUpdates: 1
    },
    // 并发支付处理
    paymentProcessing: {
      concurrentPayments: 5,
      allowedSuccessfulPayments: 1
    }
  },

  // 真实场景测试配置
  realWorldScenarios: {
    // 午餐高峰期
    lunchRush: {
      totalOrders: 100,
      duration: 1800000, // 30分钟
      minSuccessRate: 0.9 // 90%
    },
    // 晚餐高峰期
    dinnerRush: {
      totalOrders: 50,
      maxDuration: 60000, // 1分钟
      minSuccessRate: 0.9,
      minAverageOrderValue: 30
    },
    // 客户完整流程
    customerJourney: {
      totalJourneys: 10,
      minSuccessRate: 0.8,
      minCompletionRate: 0.8
    }
  },

  // 性能阈值
  performanceThresholds: {
    // API响应时间阈值（毫秒）
    apiLatency: {
      simple: 200,      // 简单查询
      complex: 800,     // 复杂查询
      creation: 1000,   // 创建操作
      payment: 2000     // 支付处理
    },
    
    // 吞吐量阈值（请求/秒）
    throughput: {
      read: 200,        // 读操作
      write: 50,        // 写操作
      payment: 20       // 支付操作
    },
    
    // 错误率阈值
    errorRates: {
      normal: 0.01,     // 正常负载下 1%
      stress: 0.1,      // 压力测试下 10%
      extreme: 0.2      // 极限测试下 20%
    },
    
    // 资源使用阈值
    resources: {
      maxMemoryIncreaseMB: 500,
      maxCpuUsagePercent: 80,
      maxDatabaseConnections: 100
    }
  },

  // 测试环境配置
  environment: {
    // 数据库配置
    database: {
      useRealDB: true,
      connectionPoolSize: 50,
      maxConnectionAttempts: 3
    },
    
    // 服务器配置
    server: {
      port: process.env.TEST_PORT || 0, // 0 = 随机端口
      timeout: 30000 // 30秒超时
    },
    
    // 外部服务配置
    externalServices: {
      stripe: {
        enabled: !!process.env.STRIPE_TEST_SECRET_KEY,
        timeout: 10000
      },
      paypal: {
        enabled: !!(process.env.PAYPAL_CLIENT_ID && process.env.PAYPAL_CLIENT_SECRET),
        timeout: 15000
      }
    }
  },

  // 报告配置
  reporting: {
    // 控制台输出
    console: {
      enabled: true,
      verbose: process.env.VERBOSE_TESTS === 'true'
    },
    
    // 文件输出
    file: {
      enabled: true,
      outputDir: './test/performance/reports',
      formats: ['json', 'html']
    },
    
    // 实时监控
    monitoring: {
      enabled: process.env.ENABLE_MONITORING === 'true',
      interval: 1000 // 1秒间隔
    }
  },

  // 测试数据配置
  testData: {
    // 用户数据
    users: {
      count: 20,
      namePrefix: 'PerfTest User',
      emailDomain: 'perftest.example.com'
    },
    
    // 餐厅数据
    restaurants: {
      count: 5,
      namePrefix: 'PerfTest Restaurant',
      itemsPerRestaurant: 20
    },
    
    // 订单数据
    orders: {
      minItems: 1,
      maxItems: 5,
      minPrice: 5.99,
      maxPrice: 29.99
    }
  },

  // 清理配置
  cleanup: {
    // 测试后清理
    afterEach: true,
    afterAll: true,
    
    // 保留测试数据用于调试
    keepDataOnFailure: process.env.KEEP_TEST_DATA === 'true'
  }
};

// 根据环境变量调整配置
if (process.env.NODE_ENV === 'ci') {
  // CI环境下的配置调整
  module.exports.loadTest.heavy.connections = 50; // 减少连接数
  module.exports.stressTest.extremeConnections = 200; // 减少极限连接数
  module.exports.performanceThresholds.apiLatency.simple = 500; // 放宽延迟要求
}

if (process.env.PERFORMANCE_TEST_LEVEL === 'quick') {
  // 快速测试模式
  module.exports.loadTest.medium.duration = 10;
  module.exports.loadTest.heavy.duration = 20;
  module.exports.realWorldScenarios.lunchRush.totalOrders = 20;
  module.exports.realWorldScenarios.dinnerRush.totalOrders = 10;
}

// 导出辅助函数
module.exports.getConfig = function(testType, level = 'medium') {
  const config = module.exports[testType];
  if (config && config[level]) {
    return config[level];
  }
  return config;
};

module.exports.isExternalServiceEnabled = function(service) {
  return module.exports.environment.externalServices[service]?.enabled || false;
};

module.exports.getPerformanceThreshold = function(operation, metric) {
  const thresholds = module.exports.performanceThresholds;
  if (thresholds[metric] && thresholds[metric][operation]) {
    return thresholds[metric][operation];
  }
  return null;
};

// 验证配置的有效性
module.exports.validateConfig = function() {
  const errors = [];
  
  // 检查必需的环境变量
  if (module.exports.environment.database.useRealDB && !process.env.MONGO_TEST_URL) {
    errors.push('MONGO_TEST_URL is required when useRealDB is true');
  }
  
  // 检查性能阈值的合理性
  const latencyThresholds = module.exports.performanceThresholds.apiLatency;
  if (latencyThresholds.simple > latencyThresholds.complex) {
    errors.push('Simple API latency threshold should be less than complex API latency threshold');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};
