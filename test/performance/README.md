# 性能测试文档 (Performance Testing Documentation)

## 概述

性能测试框架用于评估 Firespoon API 在各种负载条件下的性能表现。测试包括负载测试、压力测试和并发测试，使用 Autocannon 进行 HTTP 负载测试。

## 快速开始

### 运行性能测试

```bash
# 运行所有性能测试
npm run test:performance

# 运行特定类型的性能测试
npm run test:load      # 负载测试
npm run test:stress    # 压力测试
npm run test:concurrency # 并发测试
```

### 查看测试报告

测试完成后，报告会保存在以下位置：
- `test/performance/reports/performance-test-results.xml` - JUnit 格式的测试结果
- `test/performance/reports/performance-summary.txt` - 性能测试摘要
- `test/performance/reports/test-metadata.json` - 测试元数据

## 测试类型

### 1. 负载测试 (Load Testing)

**目的：** 测试系统在正常负载下的性能表现

**测试内容：**
- GraphQL 查询性能
- 响应时间测试
- 吞吐量测试
- 内存泄漏检测
- 错误率分析

**性能指标：**
- 平均响应时间 < 1000ms
- 吞吐量 > 50 req/s
- 错误率 < 1%
- 内存增长 < 50%

**示例测试：**
```javascript
test('should handle restaurant queries under load', async () => {
  const query = `
    query {
      restaurants {
        _id
        name
        isActive
        address
      }
    }
  `;

  const result = await autocannon({
    url: `${baseURL}/graphql`,
    method: 'POST',
    connections: 50,
    duration: 10,
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query })
  });

  expect(result.errors).toBe(0);
  expect(result.latency.mean).toBeLessThan(1000);
});
```

### 2. 压力测试 (Stress Testing)

**目的：** 测试系统在极限负载下的表现和恢复能力

**测试内容：**
- 极高并发连接测试
- 系统恢复能力测试
- 资源耗尽测试
- 限流机制测试

**性能指标：**
- 500+ 并发连接处理
- 系统恢复时间 < 10秒
- 无内存泄漏
- 无数据损坏

**示例测试：**
```javascript
test('should survive 500 concurrent connections', async () => {
  const query = `
    query {
      configuration {
        _id
        currency
        deliveryRate
      }
    }
  `;

  const result = await autocannon({
    url: `${baseURL}/graphql`,
    method: 'POST',
    connections: 500,
    duration: 30,
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query })
  });

  expect(result.errors).toBeLessThan(result.requests.total * 0.05); // < 5% 错误率
});
```

### 3. 并发测试 (Concurrency Testing)

**目的：** 测试并发操作的数据一致性和事务完整性

**测试内容：**
- 并发订单创建
- 并发状态更新
- 并发支付处理
- 数据竞态条件检测

**性能指标：**
- 数据一致性 100%
- 无重复处理
- 无死锁
- 事务原子性保证

**示例测试：**
```javascript
test('should handle concurrent order creation without data corruption', async () => {
  const concurrentOrders = 50;
  const orderPromises = [];

  for (let i = 0; i < concurrentOrders; i++) {
    orderPromises.push(
      createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderId: `CONCURRENT_${i}_${Date.now()}`,
        orderAmount: 10.00 + i,
        orderStatus: 'PENDING'
      })
    );
  }

  const results = await Promise.all(orderPromises);
  
  expect(results.length).toBe(concurrentOrders);
  
  // 验证订单ID的唯一性
  const orderIds = results.map(order => order.orderId);
  const uniqueOrderIds = [...new Set(orderIds)];
  expect(uniqueOrderIds.length).toBe(concurrentOrders);
});
```

## 配置说明

### Jest 配置

性能测试使用专门的 Jest 配置文件：`test/config/jest.performance.config.js`

**主要配置：**
- `testTimeout: 120000` - 2分钟超时
- `maxWorkers: 1` - 串行执行
- `forceExit: true` - 强制退出
- `detectLeaks: false` - 禁用内存泄漏检测

### 环境配置

性能测试会自动设置以下环境变量：
- `NODE_ENV=test`
- `PERFORMANCE_TEST=true`
- `VERBOSE_TESTS=true`
- `TEST_TIMEOUT=120000`

### 测试级别

根据环境自动调整测试级别：
- **CI 环境：** `quick` - 快速测试模式
- **本地环境：** `normal` - 正常测试模式
- **手动设置：** `PERFORMANCE_TEST_LEVEL=intensive` - 密集测试模式

## 性能阈值

### 默认阈值配置

```javascript
defaultThresholds: {
  maxLatency: 1000,      // 最大延迟 1秒
  minThroughput: 50,     // 最小吞吐量 50 req/s
  maxErrorRate: 0.05,    // 最大错误率 5%
  maxMemoryIncrease: 500 // 最大内存增长 500MB
}
```

### 测试级别配置

```javascript
testLevels: {
  quick: {
    duration: 10,
    connections: 10
  },
  normal: {
    duration: 30,
    connections: 50
  },
  intensive: {
    duration: 60,
    connections: 100
  }
}
```

## 故障排除

### 常见问题

1. **测试超时**
   - 检查数据库连接
   - 增加超时时间
   - 减少并发连接数

2. **内存不足**
   - 启用垃圾回收：`node --expose-gc`
   - 增加内存限制：`--max-old-space-size=4096`

3. **GraphQL 查询错误**
   - 验证查询语法
   - 检查字段名称
   - 确认认证令牌

### 调试技巧

```bash
# 运行单个性能测试文件
npx jest test/performance/load.test.js --config test/config/jest.performance.config.js

# 启用详细输出
VERBOSE_TESTS=true npm run test:load

# 启用垃圾回收
node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest test/performance/load.test.js
```

## 最佳实践

1. **测试设计**
   - 使用真实的数据库和 Redis
   - 模拟真实的用户行为
   - 测试关键业务流程

2. **性能监控**
   - 监控内存使用情况
   - 记录响应时间分布
   - 分析错误模式

3. **结果分析**
   - 关注 P95 和 P99 延迟
   - 监控错误率趋势
   - 检查内存泄漏

4. **持续改进**
   - 定期运行性能测试
   - 建立性能基准
   - 优化瓶颈组件

## 报告解读

### 性能指标说明

- **Latency (延迟)**
  - `mean`: 平均延迟
  - `p50`: 50% 请求的延迟
  - `p95`: 95% 请求的延迟
  - `p99`: 99% 请求的延迟

- **Throughput (吞吐量)**
  - `mean`: 平均每秒请求数
  - `total`: 总请求数

- **Errors (错误)**
  - `total`: 总错误数
  - `timeouts`: 超时数
  - `non2xx`: 非 2xx 响应数

### 性能评估标准

- **优秀：** 延迟 < 500ms，吞吐量 > 100 req/s，错误率 < 0.1%
- **良好：** 延迟 < 1000ms，吞吐量 > 50 req/s，错误率 < 1%
- **可接受：** 延迟 < 2000ms，吞吐量 > 25 req/s，错误率 < 5%
- **需要优化：** 超出可接受范围
