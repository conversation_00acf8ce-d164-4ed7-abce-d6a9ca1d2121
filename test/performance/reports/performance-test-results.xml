<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Performance Tests" tests="7" failures="0" errors="0" time="69.155">
  <testsuite name="Basic Performance Testing" errors="0" failures="0" skipped="0" timestamp="2025-06-09T12:00:46" time="69.083" tests="7">
    <testcase classname="Basic Performance Testing Health Check Tests should handle health check under light load" name="Basic Performance Testing Health Check Tests should handle health check under light load" time="5.319">
    </testcase>
    <testcase classname="Basic Performance Testing Health Check Tests should handle health check under moderate load" name="Basic Performance Testing Health Check Tests should handle health check under moderate load" time="10.544">
    </testcase>
    <testcase classname="Basic Performance Testing GraphQL Endpoint Tests should handle simple GraphQL introspection query" name="Basic Performance Testing GraphQL Endpoint Tests should handle simple GraphQL introspection query" time="5.206">
    </testcase>
    <testcase classname="Basic Performance Testing GraphQL Endpoint Tests should handle configuration query without authentication" name="Basic Performance Testing GraphQL Endpoint Tests should handle configuration query without authentication" time="11.418">
    </testcase>
    <testcase classname="Basic Performance Testing Database Connection Tests should maintain stable database connection under load" name="Basic Performance Testing Database Connection Tests should maintain stable database connection under load" time="2.065">
    </testcase>
    <testcase classname="Basic Performance Testing Memory Usage Tests should not have significant memory leaks" name="Basic Performance Testing Memory Usage Tests should not have significant memory leaks" time="20.906">
    </testcase>
    <testcase classname="Basic Performance Testing Error Handling Tests should handle invalid endpoints gracefully" name="Basic Performance Testing Error Handling Tests should handle invalid endpoints gracefully" time="5.499">
    </testcase>
  </testsuite>
</testsuites>