/**
 * 基础性能测试
 * 专注于测试基本的API端点和数据库连接
 */

const autocannon = require('autocannon');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');

describe('Basic Performance Testing', () => {
  let testApp;
  let baseURL;

  beforeAll(async () => {
    console.log('🚀 Starting Basic Performance Tests...');
    
    // 首先连接测试数据库
    await connectTestDB({ useRealDatabase: true });
    console.log('✅ Database connected');
    
    // 等待数据库连接稳定
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 启动测试服务器
    testApp = await createTestApp();
    const port = testApp.port;
    baseURL = `http://localhost:${port}`;
    console.log(`✅ Test server started on port ${port}`);
    
    // 等待服务器启动完成
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up Basic Performance Tests...');
    
    // 清理测试应用
    if (testApp) {
      await cleanupTestApp();
    }
    
    // 等待清理完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 断开数据库连接
    await disconnectTestDB();
    console.log('✅ Cleanup completed');
  });

  beforeEach(async () => {
    // 检查数据库连接状态
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState !== 1) {
      console.log('Database connection lost, reconnecting...');
      await connectTestDB({ useRealDatabase: true });
    }
  });

  describe('Health Check Tests', () => {
    test('should handle health check under light load', async () => {
      const result = await autocannon({
        url: `${baseURL}/health`,
        method: 'GET',
        connections: 10,
        duration: 5,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBe(0);
      expect(result.latency.mean).toBeLessThan(100); // 健康检查应该很快
      expect(result.requests.mean).toBeGreaterThan(50); // 每秒至少50个请求

      console.log('Health Check Performance:', {
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        timeouts: result.timeouts
      });
    });

    test('should handle health check under moderate load', async () => {
      const result = await autocannon({
        url: `${baseURL}/health`,
        method: 'GET',
        connections: 50,
        duration: 10,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBe(0);
      expect(result.latency.mean).toBeLessThan(200); // 中等负载下仍然快速
      expect(result.requests.mean).toBeGreaterThan(100); // 每秒至少100个请求

      console.log('Moderate Load Health Check Performance:', {
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        timeouts: result.timeouts,
        p95: `${(result.latency.p95 || 0).toFixed(2)}ms`,
        p99: `${(result.latency.p99 || 0).toFixed(2)}ms`
      });
    });
  });

  describe('GraphQL Endpoint Tests', () => {
    test('should handle simple GraphQL introspection query', async () => {
      const query = `
        query {
          __schema {
            types {
              name
            }
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 10,
        duration: 5,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(1000); // GraphQL内省查询

      console.log('GraphQL Introspection Performance:', {
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        non2xx: result.non2xx,
        timeouts: result.timeouts
      });
    });

    test('should handle configuration query without authentication', async () => {
      const query = `
        query {
          configuration {
            _id
            currency
            currencySymbol
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        connections: 20,
        duration: 10,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(2000); // 配置查询

      console.log('Configuration Query Performance:', {
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        non2xx: result.non2xx,
        timeouts: result.timeouts,
        status: result.non2xx === 0 ? 'SUCCESS' : 'PARTIAL'
      });
    });
  });

  describe('Database Connection Tests', () => {
    test('should maintain stable database connection under load', async () => {
      const mongoose = require('mongoose');

      // 确保数据库连接
      if (mongoose.connection.readyState !== 1) {
        await connectTestDB({ useRealDatabase: true });
        // 等待连接稳定
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 记录初始连接状态
      const initialState = mongoose.connection.readyState;

      // 如果连接状态不是1，跳过这个测试
      if (initialState !== 1) {
        console.log(`Skipping database connection test - connection state: ${initialState}`);
        return;
      }

      // 运行一些负载测试
      const result = await autocannon({
        url: `${baseURL}/health`,
        method: 'GET',
        connections: 100,
        duration: 15,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // 检查连接状态是否保持稳定
      const finalState = mongoose.connection.readyState;
      expect(finalState).toBe(1); // 应该仍然是连接状态

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);

      console.log('Database Connection Stability Test:', {
        initialState,
        finalState,
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        timeouts: result.timeouts,
        connectionStable: initialState === finalState
      });
    });
  });

  describe('Memory Usage Tests', () => {
    test('should not have significant memory leaks', async () => {
      const initialMemory = process.memoryUsage();

      // 运行一些负载测试
      const result = await autocannon({
        url: `${baseURL}/health`,
        method: 'GET',
        connections: 50,
        duration: 20,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

      expect(result.errors).toBe(0);
      expect(memoryIncreasePercent).toBeLessThan(100); // 内存增长不超过100%

      console.log('Memory Usage Analysis:', {
        initial: `${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        final: `${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        increase: `${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`,
        increasePercent: `${memoryIncreasePercent.toFixed(2)}%`,
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`
      });
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle invalid endpoints gracefully', async () => {
      const result = await autocannon({
        url: `${baseURL}/invalid-endpoint`,
        method: 'GET',
        connections: 20,
        duration: 5,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBeGreaterThan(0); // 应该返回404等错误状态码
      expect(result.latency.mean).toBeLessThan(500); // 错误响应应该很快

      console.log('Error Handling Performance:', {
        latency: `${result.latency.mean.toFixed(2)}ms`,
        throughput: `${result.requests.mean.toFixed(2)} req/s`,
        errors: result.errors,
        non2xx: result.non2xx,
        timeouts: result.timeouts
      });
    });
  });
});
