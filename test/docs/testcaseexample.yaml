TestFile: aaa.js
---
# 第一个测试组文档
TestFunction: sss
Cases: # <-- 将测试用例列表放在一个明确的键下
  - CaseID: "U-SSS-001"
    Module: "auth"
    Description: "验证有效的 token 和 session"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "smoke"
      - "positive"
    Precondition:
      - "设置有效的 token: 'valid-token'"
      - "Mock SessionIdGenerator.validateToken 返回 true"
      - "Mock sessionService.getSessionByToken 返回 { dialogueId: 'dialogue-123' }"
    Steps:
      - "发送请求"
      - "验证 token"
      - "验证 session"
    ExpectedResult:
      - "req.session 被设置为 validSession"
      - "next() 被调用"
  - CaseID: "U-SSS-002"
    Module: "auth"
    Description: "缺少 Token"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "smoke"
      - "positive"
    Precondition:
      - "不设置请求头中的 token"
    ExpectedResult:
      - "返回 401 状态码"
      - "返回错误信息：'Missing or invalid X-WhatsAppW-Token header'"
      - "next() 不被调用"

---
TestFunction: ttt
Cases: # <-- 另一个测试用例列表
  - CaseID: "U-TTT-003"
    Module: "user"
    Description: "创建新用户"
    Importance: "Medium"
    Status: "Planned"
    Precondition: []
    Steps:
      - "发送创建用户请求"
    ExpectedResult:
      - "用户创建成功，返回 201"
  - CaseID: "U-TTT-C004"
    Module: "user"
    Description: "获取用户信息"
    Importance: "Low"
    Status: "InProgress"
    Precondition:
      - "用户已存在"
    Steps:
      - "发送获取用户信息请求"
    ExpectedResult:
      - "返回用户信息"