TestFile: whatsapp/services/whatsappService.js
---
TestFunction: sendMessage
Cases:
  - CaseID: "U-SVC-WS-101"
    Module: "whatsappService"
    Description: "Should send a message successfully with a valid payload and token"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "api-integration"
      - "messaging"
    Precondition:
      - "A valid `recipientId` and `messagePayload` are provided."
      - "Environment variables for WhatsApp API version, phone number ID, and access token are set."
      - "Mock the HTTP client (e.g., axios) to return a successful response (e.g., status 200 and a message ID)."
    Steps:
      - "1. Call `whatsappService.sendMessage(recipientId, messagePayload)`."
    ExpectedResult:
      - "The HTTP client is called with the correct method (POST), URL, headers, and data."
      - "The Authorization header is correctly set to 'Bearer <accessToken>'."
      - "The request data includes the correct messaging_product, recipient_type, to, and the provided messagePayload."
      - "The function returns the data from the successful API response."
  - CaseID: "U-SVC-WS-102"
    Module: "whatsappService"
    Description: "Should handle API errors gracefully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "api-integration"
    Precondition:
      - "Mock the HTTP client to return an error response (e.g., status 400 or 500) with an error payload from the API."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `whatsappService.sendMessage(recipientId, messagePayload)`."
    ExpectedResult:
      - "The error is caught."
      - "`logger.error` is called with detailed information from the API error response."
      - "The function throws an error or returns null/undefined."
  - CaseID: "U-SVC-WS-103"
    Module: "whatsappService"
    Description: "Should handle network or other request errors"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock the HTTP client to throw a network error (e.g., ECONNRESET)."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `whatsappService.sendMessage(recipientId, messagePayload)`."
    ExpectedResult:
      - "The error is caught."
      - "`logger.error` is called with the network error details."
      - "The function throws or returns null."
---
TestFunction: markMessageRead
Cases:
  - CaseID: "U-SVC-WS-201"
    Module: "whatsappService"
    Description: "Should successfully mark a message as read"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "api-integration"
      - "messaging"
    Precondition:
      - "A valid `messageId` is provided."
      - "Mock the HTTP client to return a successful response (e.g., { success: true })."
    Steps:
      - "1. Call `whatsappService.markMessageRead(messageId)`."
    ExpectedResult:
      - "The HTTP client is called with a POST request to the correct endpoint."
      - "The request payload is ` { messaging_product: 'whatsapp', status: 'read', message_id: messageId } `."
      - "The function returns the success status from the API response."
  - CaseID: "U-SVC-WS-202"
    Module: "whatsappService"
    Description: "Should handle errors when marking a message as read"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock the HTTP client to return an error response."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `whatsappService.markMessageRead(messageId)`."
    ExpectedResult:
      - "The error is caught and logged."
      - "The function returns an object indicating failure, or throws an error."
