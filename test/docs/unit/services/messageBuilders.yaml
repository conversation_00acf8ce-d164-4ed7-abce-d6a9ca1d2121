TestFile: whatsapp/services/messageBuilders.js
---
TestFunction: buildMenuMessage
Cases:
  - CaseID: "U-SVC-MB-101"
    Module: "messageBuilders"
    Description: "Should build a valid menu message with sections and rows"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "menu"
    Precondition:
      - "A `menuData` object is provided with categories and items."
      - "Each category has a title and items."
      - "Each item has an id, title, and description."
    Steps:
      - "1. Call `buildMenuMessage(menuData)`."
    ExpectedResult:
      - "Returns a valid WhatsApp list message object."
      - "The object has `type: 'interactive'` and `interactive.type: 'list'`."
      - "The number of sections in the message matches the number of categories in `menuData`."
      - "The rows within each section match the items in the corresponding category."
      - "Each row has a unique `id` (e.g., 'ADD_ITEM_item-id')."
  - CaseID: "U-SVC-MB-102"
    Module: "messageBuilders"
    Description: "Should handle empty menu data gracefully"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "edge-case"
    Precondition:
      - "`menuData` is null, undefined, or an empty array."
    Steps:
      - "1. Call `buildMenuMessage(menuData)`."
    ExpectedResult:
      - "Returns a valid message object indicating the menu is unavailable or empty."
      - "Alternatively, it could throw a specific error to be handled by the caller."
---
TestFunction: buildCartMessage
Cases:
  - CaseID: "U-SVC-MB-201"
    Module: "messageBuilders"
    Description: "Should build a valid cart message for a non-empty cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "cart"
    Precondition:
      - "A `cart` object is provided with items, totalQty, and totalPrice."
      - "The cart contains at least one item."
    Steps:
      - "1. Call `buildCartMessage(cart)`."
    ExpectedResult:
      - "Returns a valid WhatsApp interactive message object (e.g., with buttons for checkout, clear cart, back to menu)."
      - "The message body text correctly lists the items, quantities, and prices."
      - "The total price is correctly formatted in the message."
  - CaseID: "U-SVC-MB-202"
    Module: "messageBuilders"
    Description: "This function should not be called for an empty cart. See buildEmptyCartMessage."
    Importance: "N/A"
    Status: "Planned"
    Tags:
      - "boundary"
    Precondition:
      - "N/A"
    Steps:
      - "N/A"
    ExpectedResult:
      - "The calling action (e.g., viewCart) is responsible for checking if the cart is empty and calling buildEmptyCartMessage instead."
---
TestFunction: buildItemAddedMessage
Cases:
  - CaseID: "U-SVC-MB-301"
    Module: "messageBuilders"
    Description: "Should build a confirmation message after adding an item"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "confirmation"
    Precondition:
      - "An `orderItem` object is provided with details of the item just added."
    Steps:
      - "1. Call `buildItemAddedMessage(orderItem)`."
    ExpectedResult:
      - "Returns a valid WhatsApp text or interactive message object."
      - "The message text confirms that the item was added."
      - "It may include buttons to 'View Cart' or 'Continue Shopping'."
---
TestFunction: buildOrderConfirmationMessage
Cases:
  - CaseID: "U-SVC-MB-401"
    Module: "messageBuilders"
    Description: "Should build a final order confirmation message"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "confirmation"
      - "order"
    Precondition:
      - "An `order` object is provided with final details (order number, items, total price, estimated delivery time)."
    Steps:
      - "1. Call `buildOrderConfirmationMessage(order)`."
    ExpectedResult:
      - "Returns a valid WhatsApp text message object."
      - "The message text contains all the relevant order details in a clear, readable format."
