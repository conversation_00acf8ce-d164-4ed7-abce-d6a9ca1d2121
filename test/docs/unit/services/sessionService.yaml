TestFile: whatsapp/services/sessionService.js
---
TestFunction: createSession
Cases:
  - CaseID: "U-SVC-SS-101"
    Module: "sessionService"
    Description: "Should create a new session with default values and save it to Redis"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "create"
    Precondition:
      - "A `waId` and `profileName` are provided."
      - "Mock Redis client's `set` method to resolve successfully."
      - "Mock `sessionIdGenerator.generate` to return a predictable session ID."
      - "Mock FSM machine creation to ensure a state machine instance is created."
    Steps:
      - "1. Call `sessionService.createSession(waId, profileName)`."
    ExpectedResult:
      - "A new session object is created with a unique ID, the provided waId and profileName, an initialized FSM, and an empty cart."
      - "The Redis `set` command is called with the session ID as the key and the stringified session object as the value."
      - "The function returns the newly created session object."
  - CaseID: "U-SVC-SS-102"
    Module: "sessionService"
    Description: "Should handle errors during Redis set operation"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "redis"
    Precondition:
      - "Mock Redis client's `set` method to reject with an error."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `sessionService.createSession(waId, profileName)`."
    ExpectedResult:
      - "The error from Redis is caught."
      - "`logger.error` is called with the relevant error information."
      - "The function throws the error or returns null."
---
TestFunction: getSession
Cases:
  - CaseID: "U-SVC-SS-201"
    Module: "sessionService"
    Description: "Should retrieve and deserialize a session from Redis"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "read"
    Precondition:
      - "A valid `sessionId` is provided."
      - "Mock Redis client's `get` method to return a valid, stringified session object."
      - "The session object includes a persisted FSM state."
    Steps:
      - "1. Call `sessionService.getSession(sessionId)`."
    ExpectedResult:
      - "The Redis `get` command is called with the session ID."
      - "The stringified object is parsed."
      - "A new FSM instance is created and restored to its persisted state."
      - "A Cart instance is created from the persisted cart data."
      - "The fully rehydrated session object is returned."
  - CaseID: "U-SVC-SS-202"
    Module: "sessionService"
    Description: "Should return null if session is not found in Redis"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "boundary"
    Precondition:
      - "A `sessionId` is provided."
      - "Mock Redis client's `get` method to return null."
    Steps:
      - "1. Call `sessionService.getSession(sessionId)`."
    ExpectedResult:
      - "The function returns `null`."
---
TestFunction: saveSession
Cases:
  - CaseID: "U-SVC-SS-301"
    Module: "sessionService"
    Description: "Should serialize and save a session object to Redis"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "update"
    Precondition:
      - "A valid `session` object is provided, including an FSM state."
      - "Mock Redis client's `set` method to resolve successfully."
    Steps:
      - "1. Call `sessionService.saveSession(session)`."
    ExpectedResult:
      - "A storable version of the session is created (e.g., FSM state is extracted)."
      - "The Redis `set` command is called with the session ID and the stringified storable session."
---
TestFunction: clearSession
Cases:
  - CaseID: "U-SVC-SS-401"
    Module: "sessionService"
    Description: "Should delete a session from Redis"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "delete"
    Precondition:
      - "A valid `sessionId` is provided."
      - "Mock Redis client's `del` method to resolve successfully."
    Steps:
      - "1. Call `sessionService.clearSession(sessionId)`."
    ExpectedResult:
      - "The Redis `del` command is called with the session ID."
