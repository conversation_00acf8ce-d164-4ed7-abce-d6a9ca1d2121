TestFile: whatsapp/controllers/webhookController.js
---
TestFunction: processIncomingMessage
Cases:
  - CaseID: "U-WHC-PIM-101"
    Module: "webhookController"
    Description: "Should process a valid incoming text message and trigger FSM event"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "text-message"
      - "fsm"
    Precondition:
      - "Incoming request 'req.body' contains a valid WhatsApp text message structure."
      - "Mock 'extractWebhookData' returns valid data."
      - "Mock 'getOrCreateSession' returns a valid session object."
      - "Mock 'extractMessageContent' returns type 'text' and content."
      - "Mock 'sendFsmEvent' is set up to be called."
    Steps:
      - "1. Call `processIncomingMessage(req, res)` with a mock request for a text message."
      - "2. Verify `handleTextMessage` is called."
      - "3. Verify `res.sendStatus(200)` is called."
    ExpectedResult:
      - "All mocked helper functions are called as expected."
      - "FSM event 'TEXT_RECEIVED' is triggered."
      - "HTTP status 200 is sent as response."
  - CaseID: "U-WHC-PIM-102"
    Module: "webhookController"
    Description: "Should process a valid incoming button reply message"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "button-reply"
      - "fsm"
    Precondition:
      - "Incoming request 'req.body' contains a valid WhatsApp button reply structure."
      - "Mock 'extractMessageContent' returns type 'button_reply'."
    Steps:
      - "1. Call `processIncomingMessage(req, res)` with a mock request for a button reply."
    ExpectedResult:
      - "`handleButtonReply` is successfully invoked."
      - "HTTP status 200 is sent."
  - CaseID: "U-WHC-PIM-103"
    Module: "webhookController"
    Description: "Should handle errors during message processing gracefully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock 'extractWebhookData' is set to throw an error."
    Steps:
      - "1. Call `processIncomingMessage(req, res)`."
    ExpectedResult:
      - "Error is logged."
      - "HTTP status 500 is sent with an error message."
---
TestFunction: extractWebhookData
Cases:
  - CaseID: "U-WHC-EWD-201"
    Module: "webhookController"
    Description: "Should extract data correctly from a valid new format message payload"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "data-extraction"
    Precondition:
      - "A 'webhookBody' is provided that matches the new WhatsApp message format."
    Steps:
      - "1. Call `extractWebhookData(webhookBody)`."
    ExpectedResult:
      - "Returns an object with 'contact', 'messageId', 'timestamp', 'messageData', 'profileName', 'waId'."
  - CaseID: "U-WHC-EWD-202"
    Module: "webhookController"
    Description: "Should return null if webhookBody is malformed"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "data-extraction"
    Precondition:
      - "Provide a 'webhookBody' that is null, undefined, or missing essential parts like 'entry' or 'changes'."
    Steps:
      - "1. Call `extractWebhookData(webhookBody)`."
    ExpectedResult:
      - "Returns null."
---
TestFunction: getOrCreateSession
Cases:
  - CaseID: "U-WHC-GCS-301"
    Module: "webhookController"
    Description: "Should retrieve an existing session"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
    Precondition:
      - "Mock `sessionService.getSession` to return an existing session object."
    Steps:
      - "1. Call `getOrCreateSession(contact, timestamp)`."
    ExpectedResult:
      - "The existing session object is returned."
      - "`OrderFSM` constructor is NOT called."
  - CaseID: "U-WHC-GCS-302"
    Module: "webhookController"
    Description: "Should create a new session if none exists"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "creation"
    Precondition:
      - "Mock `sessionService.getSession` to return null."
      - "Mock `sessionIdGenerator.generateSessionId` to return a new ID."
      - "Mock `sessionService.createSession` to return the new session."
    Steps:
      - "1. Call `getOrCreateSession(contact, timestamp)`."
    ExpectedResult:
      - "A new session object is returned with an initialized `orderFsm` property."
      - "`OrderFSM` constructor is called."
---
TestFunction: extractMessageContent
Cases:
  - CaseID: "U-WHC-EMC-401"
    Module: "webhookController"
    Description: "Should extract content from a text message (new format)"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "content-extraction"
    Precondition:
      - "`messageData` has `content` array with `displayType: 'text'`."
    Steps:
      - "1. Call `extractMessageContent(messageData)`."
    ExpectedResult:
      - "Returns an object: { type: 'text', text: 'message text', externalID: 'ext_id' }."
  - CaseID: "U-WHC-EMC-402"
    Module: "webhookController"
    Description: "Should return null for unsupported message types"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "content-extraction"
    Precondition:
      - "`messageData` has a `type` that is not 'text', 'button_reply', or 'list_reply'."
    Steps:
      - "1. Call `extractMessageContent(messageData)`."
    ExpectedResult:
      - "Returns null."
---
TestFunction: handleTextMessage
Cases:
  - CaseID: "U-WHC-HTM-501"
    Module: "webhookController"
    Description: "Should process a special text command like 'clear'"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "command-processing"
    Precondition:
      - "`messageContent.text` is 'clear'."
      - "Mock `processTextCommand` is set up."
    Steps:
      - "1. Call `handleTextMessage(session, messageContent, orderFsm)`."
    ExpectedResult:
      - "`processTextCommand` is called with 'clear' command."
      - "`sendFsmEvent` is not called directly."
  - CaseID: "U-WHC-HTM-502"
    Module: "webhookController"
    Description: "Should send TEXT_RECEIVED event for a regular text message"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "fsm-event"
    Precondition:
      - "`messageContent.text` is a regular message (not a command)."
      - "Mock `sendFsmEvent` is set up."
    Steps:
      - "1. Call `handleTextMessage(session, messageContent, orderFsm)`."
    ExpectedResult:
      - "`sendFsmEvent` is called with type 'TEXT_RECEIVED' and correct data."
