# Session Validator Middleware 测试用例文档

## 1. 测试目的

验证 WhatsApp Session 验证中间件的各项功能，确保：
- Token 验证逻辑正确
- Session 验证逻辑正确
- 错误处理机制正常工作
- 日志记录功能正常

## 2. 被测试文件

- **文件路径**：`/whatsapp/middleware/sessionValidator.js`
- **主要功能**：验证 WhatsApp 会话的有效性
- **验证流程**：
  1. 检查 x-whatsappw-token 头部是否存在
  2. 验证 token 格式
  3. 获取并验证 session
  4. 处理各种错误情况

## 3. 依赖关系

测试需要 mock 以下模块：
```javascript
const SessionIdGenerator = require('../../../../whatsapp/utils/sessionIdGenerator');
const sessionService = require('../../../../whatsapp/services/sessionService');
const logger = require('../../../../helpers/logger');
```

## 4. 测试环境准备

### 4.1 Mock 对象设置

```javascript
// 在 beforeEach 中设置
req = {
  headers: {}
};

res = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn()
};

next = jest.fn();
```

### 4.2 依赖模块 Mock

```javascript
jest.mock('../../../../whatsapp/utils/sessionIdGenerator');
jest.mock('../../../../whatsapp/services/sessionService');
jest.mock('../../../../helpers/logger');
```

## 5. 测试用例详细说明

### 5.1 正常流程测试

#### TC001: 验证有效的 token 和 session

**目的**：验证当提供有效的 token 和存在有效的 session 时，中间件能够正确处理。

**准备工作**：
1. 设置有效的 token：
   ```javascript
   const validToken = 'valid-token';
   req.headers['x-whatsappw-token'] = validToken;
   ```

2. Mock SessionIdGenerator 返回值：
   ```javascript
   SessionIdGenerator.validateToken.mockReturnValue(true);
   ```

3. Mock sessionService 返回值：
   ```javascript
   const validSession = { dialogueId: 'dialogue-123' };
   sessionService.getSessionByToken.mockResolvedValue(validSession);
   ```

**预期结果**：
- SessionIdGenerator.validateToken 被调用，参数为 validToken
- sessionService.getSessionByToken 被调用，参数为 validToken
- req.session 被设置为 validSession
- next() 被调用

### 5.2 错误场景测试

#### TC002: 缺少 Token

**目的**：验证当请求头中缺少 token 时的错误处理。

**准备工作**：
- 不设置请求头中的 token

**预期结果**：
- 返回 401 状态码
- 返回错误信息：'Missing or invalid X-WhatsAppW-Token header'
- next() 不被调用

#### TC003: Token 格式无效

**目的**：验证当提供的 token 格式无效时的错误处理。

**准备工作**：
1. 设置无效的 token：
   ```javascript
   const invalidToken = 'invalid-token';
   req.headers['x-whatsappw-token'] = invalidToken;
   ```

2. Mock SessionIdGenerator 返回值：
   ```javascript
   SessionIdGenerator.validateToken.mockReturnValue(false);
   ```

**预期结果**：
- 返回 401 状态码
- 返回错误信息：'Invalid token format'
- logger.debug 被调用，记录无效的 token
- next() 不被调用

#### TC004: Session 不存在

**目的**：验证当 token 有效但 session 不存在时的错误处理。

**准备工作**：
1. 设置有效的 token
2. Mock SessionIdGenerator 返回 true
3. Mock sessionService 返回 null：
   ```javascript
   sessionService.getSessionByToken.mockResolvedValue(null);
   ```

**预期结果**：
- 返回 404 状态码
- 返回错误信息：'URL expired. Please send message in dialog to get new URL'
- logger.error 被调用，记录错误信息
- next() 不被调用

#### TC005: Session 缺少 DialogueId

**目的**：验证当 session 存在但缺少 dialogueId 时的错误处理。

**准备工作**：
1. 设置有效的 token
2. Mock SessionIdGenerator 返回 true
3. Mock sessionService 返回无效的 session：
   ```javascript
   const invalidSession = { dialogueId: null };
   sessionService.getSessionByToken.mockResolvedValue(invalidSession);
   ```

**预期结果**：
- 返回 404 状态码
- 返回错误信息：'URL expired. Please send message in dialog to get new URL'
- logger.error 被调用，记录错误信息
- next() 不被调用

#### TC006: 内部错误处理

**目的**：验证当发生内部错误时的错误处理机制。

**准备工作**：
1. 设置有效的 token
2. Mock SessionIdGenerator 返回 true
3. Mock sessionService 抛出错误：
   ```javascript
   const error = new Error('Database connection failed');
   sessionService.getSessionByToken.mockRejectedValue(error);
   ```

**预期结果**：
- 返回 500 状态码
- 返回错误信息：'Internal server error'
- logger.error 被调用，记录错误详情
- next() 不被调用

## 6. 边界测试用例（建议）

### 6.1 特殊字符 Token

建议添加以下测试场景：
- Token 包含特殊字符
- Token 长度超出限制
- Token 包含 SQL 注入字符
- Token 包含 XSS 攻击字符

### 6.2 性能测试场景

建议添加以下测试场景：
- 并发请求处理
- 大量请求时的响应时间
- 内存使用监控

## 7. 测试执行说明

1. 确保所有依赖都已正确 mock
2. 每个测试用例执行前重置所有 mock：
   ```javascript
   beforeEach(() => {
     jest.clearAllMocks();
   });
   ```
3. 使用异步测试方式执行：
   ```javascript
   test('test case description', async () => {
     // test implementation
   });
   ```
4. 确保检查所有预期的函数调用和返回值

## 8. 注意事项

1. 所有 mock 函数调用次数应该被验证
2. 错误处理应该确保不会泄露敏感信息
3. 日志记录应该包含适当的信息
4. 测试应该是独立的，不应该依赖其他测试的状态
