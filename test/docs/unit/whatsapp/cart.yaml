TestFile: whatsapp/cart.js
---
TestFunction: constructor
Cases:
  - CaseID: "U-CART-C-101"
    Module: "cart"
    Description: "Should initialize an empty cart when no old cart is provided"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "initialization"
    Precondition:
      - "No 'oldCart' object is passed to the constructor."
    Steps:
      - "1. Instantiate Cart without arguments."
    ExpectedResult:
      - "`this.items` is an empty object: {}"
      - "`this.totalQty` is 0."
      - "`this.totalPrice` is 0."
  - CaseID: "U-CART-C-102"
    Module: "cart"
    Description: "Should initialize cart from an existing old cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "initialization"
    Precondition:
      - "An 'oldCart' object exists with predefined items, totalQty, and totalPrice."
      - "Example oldCart: { items: { 'item1': { item: { id: 'item1', price: 10 }, qty: 1, price: 10 } }, totalQty: 1, totalPrice: 10 }"
    Steps:
      - "1. Instantiate Cart with the 'oldCart' object."
    ExpectedResult:
      - "`this.items` is a deep copy of `oldCart.items`."
      - "`this.totalQty` is equal to `oldCart.totalQty`."
      - "`this.totalPrice` is equal to `oldCart.totalPrice`."
---
TestFunction: getCart
Cases:
  - CaseID: "U-CART-GC-201"
    Module: "cart"
    Description: "Should return the current state of the cart"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "retrieval"
    Precondition:
      - "Cart instance exists."
      - "Cart has been populated with items (e.g., via constructor or addItem)."
    Steps:
      - "1. Call `getCart()` method on the cart instance."
    ExpectedResult:
      - "Returns an object that is a shallow copy of the cart instance itself."
      - "The returned object contains the current `items`, `totalQty`, and `totalPrice`."
---
TestFunction: addItem
Cases:
  - CaseID: "U-CART-AI-301"
    Module: "cart"
    Description: "Should add a new item to an empty cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
    Precondition:
      - "Cart is empty (`this.items` is {}, `this.totalQty` is 0, `this.totalPrice` is 0)."
      - "A new 'item' object with 'id' and 'price' is provided."
      - "Example item: { id: 'newItem1', price: 25 }"
    Steps:
      - "1. Call `addItem(item)`."
    ExpectedResult:
      - "The 'item' is added to `this.items` with qty 1."
      - "`this.items[item.id]` structure: { item: item, qty: 1, price: item.price }"
      - "`this.totalQty` becomes 1."
      - "`this.totalPrice` becomes `item.price`."
  - CaseID: "U-CART-AI-302"
    Module: "cart"
    Description: "Should increase quantity and price for an existing item"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
    Precondition:
      - "Cart contains an item with a specific 'id'."
      - "Example existing item: { id: 'item1', price: 10 }, current qty: 1, current total price for item: 10."
      - "The same 'item' object (or one with the same 'id') is passed to `addItem`."
    Steps:
      - "1. Call `addItem(item)` for the existing item's ID."
    ExpectedResult:
      - "The quantity of `this.items[item.id].qty` is incremented by 1."
      - "The price `this.items[item.id].price` is updated to `item.price * new_qty`."
      - "`this.totalQty` is incremented by 1."
      - "`this.totalPrice` is increased by `item.price`."
  - CaseID: "U-CART-AI-303"
    Module: "cart"
    Description: "Should handle adding an item with price zero"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "edge-case"
      - "modification"
    Precondition:
      - "Cart is empty."
      - "A new 'item' object with 'id' and 'price: 0' is provided."
      - "Example item: { id: 'freeItem', price: 0 }"
    Steps:
      - "1. Call `addItem(item)`."
    ExpectedResult:
      - "The 'item' is added to `this.items` with qty 1 and price 0."
      - "`this.totalQty` becomes 1."
      - "`this.totalPrice` remains 0 or is unchanged if cart was not empty."
---
TestFunction: updateItem
Cases:
  - CaseID: "U-CART-UI-401"
    Module: "cart"
    Description: "Should update the quantity of an existing item"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
    Precondition:
      - "Cart contains an item with 'id' (e.g., 'itemToUpdate')."
      - "Initial quantity of 'itemToUpdate' is Q1, initial price is P1."
      - "Cart totalQty is TQ1, cart totalPrice is TP1."
      - "New quantity 'newQty' (e.g., 3) is provided."
    Steps:
      - "1. Call `updateItem(id, newQty)`."
    ExpectedResult:
      - "`this.items[id].qty` is updated to `newQty`."
      - "`this.items[id].price` is updated to `item.price * newQty`."
      - "`this.totalQty` is updated to `TQ1 - Q1 + newQty`."
      - "`this.totalPrice` is updated to `TP1 - (P1 * Q1) + (P1 * newQty)`."
  - CaseID: "U-CART-UI-402"
    Module: "cart"
    Description: "Should remove item if quantity is updated to 0"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
      - "removal"
    Precondition:
      - "Cart contains an item with 'id' (e.g., 'itemToRemove')."
      - "Initial quantity of 'itemToRemove' is Q1 > 0."
    Steps:
      - "1. Call `updateItem(id, 0)`."
    ExpectedResult:
      - "The item with 'id' is removed from `this.items` (i.e., `this.items[id]` is undefined)."
      - "`this.totalQty` is reduced by Q1."
      - "`this.totalPrice` is reduced by the original total price of that item."
  - CaseID: "U-CART-UI-403"
    Module: "cart"
    Description: "Should not change cart if item to update does not exist"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "boundary"
    Precondition:
      - "Cart instance exists."
      - "The 'id' passed to `updateItem` does not exist in `this.items`."
      - "Store initial state of cart (items, totalQty, totalPrice)."
    Steps:
      - "1. Call `updateItem('nonExistentId', 5)`."
    ExpectedResult:
      - "`this.items` remains unchanged."
      - "`this.totalQty` remains unchanged."
      - "`this.totalPrice` remains unchanged."
  - CaseID: "U-CART-UI-404"
    Module: "cart"
    Description: "Should throw error or handle gracefully if quantity is negative"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Cart contains an item with 'id'."
    Steps:
      - "1. Call `updateItem(id, -1)`."
    ExpectedResult:
      - "Behavior depends on implementation: either an error is thrown, or the item is removed (treated as <= 0), or quantity remains unchanged."
      - "If an error is expected: verify error type/message."
      - "If item is removed: verify cart state as in U-CART-UI-402."
      - "If unchanged: verify cart state remains the same."
---
TestFunction: removeItem
Cases:
  - CaseID: "U-CART-RI-501"
    Module: "cart"
    Description: "Should remove an existing item from the cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
      - "removal"
    Precondition:
      - "Cart contains an item with 'id' (e.g., 'itemToRemove')."
      - "Item 'itemToRemove' has quantity Q1 and total item price P1_total."
      - "Cart totalQty is TQ1, cart totalPrice is TP1."
    Steps:
      - "1. Call `removeItem(id)`."
    ExpectedResult:
      - "The item with 'id' is removed from `this.items`."
      - "`this.totalQty` is updated to `TQ1 - Q1`."
      - "`this.totalPrice` is updated to `TP1 - P1_total`."
  - CaseID: "U-CART-RI-502"
    Module: "cart"
    Description: "Should not change cart if item to remove does not exist"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "boundary"
    Precondition:
      - "Cart instance exists."
      - "The 'id' passed to `removeItem` does not exist in `this.items`."
      - "Store initial state of cart."
    Steps:
      - "1. Call `removeItem('nonExistentId')`."
    ExpectedResult:
      - "Cart state remains unchanged."
---
TestFunction: clearCart
Cases:
  - CaseID: "U-CART-CC-601"
    Module: "cart"
    Description: "Should clear all items from a populated cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "modification"
      - "reset"
    Precondition:
      - "Cart contains multiple items."
      - "`this.totalQty` > 0."
      - "`this.totalPrice` > 0."
    Steps:
      - "1. Call `clearCart()`."
    ExpectedResult:
      - "`this.items` is an empty object: {}"
      - "`this.totalQty` is 0."
      - "`this.totalPrice` is 0."
  - CaseID: "U-CART-CC-602"
    Module: "cart"
    Description: "Should correctly handle clearing an already empty cart"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "boundary"
      - "reset"
    Precondition:
      - "Cart is already empty."
    Steps:
      - "1. Call `clearCart()`."
    ExpectedResult:
      - "`this.items` remains an empty object."
      - "`this.totalQty` remains 0."
      - "`this.totalPrice` remains 0."
      - "No errors are thrown."
