TestFile: helpers/logger.js
---
TestFunction: <PERSON> Configuration
Cases:
  - CaseID: "U-HLP-LOG-101"
    Module: "loggerHelper"
    Description: "Should create a winston logger instance with the correct configuration"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "configuration"
      - "logging"
    Precondition:
      - "Spy on `winston.createLogger` and `winston.transports`."
    Steps:
      - "1. Import the `logger` module."
    ExpectedResult:
      - "`winston.createLogger` is called once."
      - "The configuration passed to `createLogger` includes the correct `level` ('silly')."
      - "The configuration includes a `format` object using `winston.format.combine`, `timestamp`, and `printf`."
      - "The `transports` array contains three instances: a `Console` transport and two `File` transports (for 'error.log' and 'combined.log')."
