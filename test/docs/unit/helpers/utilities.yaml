TestFile: helpers/utilities.js
---
TestFunction: isTimeBetween
Cases:
  - CaseID: "U-HLP-UTL-101"
    Module: "utilitiesHelper"
    Description: "Should return true when the time is within the specified range"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "time"
      - "logic"
    Precondition:
      - "`startTime` = '10:00', `endTime` = '18:00', `time` = '14:00'."
    Steps:
      - "1. Call `isTimeBetween` with the predefined times."
    ExpectedResult:
      - "The function returns `true`."

  - CaseID: "U-HLP-UTL-102"
    Module: "utilitiesHelper"
    Description: "Should return false when the time is outside the specified range"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
    Precondition:
      - "`startTime` = '10:00', `endTime` = '18:00', `time` = '08:00'."
    Steps:
      - "1. Call `isTimeBetween`."
    ExpectedResult:
      - "The function returns `false`."

  - CaseID: "U-HLP-UTL-103"
    Module: "utilitiesHelper"
    Description: "Should correctly handle time ranges that span across midnight"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "edge-case"
      - "overnight"
    Precondition:
      - "`startTime` = '22:00', `endTime` = '04:00', `time` = '01:00'."
    Steps:
      - "1. Call `isTimeBetween`."
    ExpectedResult:
      - "The function returns `true`."
---
TestFunction: checkImage
Cases:
  - CaseID: "U-HLP-UTL-201"
    Module: "utilitiesHelper"
    Description: "Should return true for valid image extensions (jpg, jpeg, png)"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "validation"
      - "file"
    Precondition:
      - "Provide filenames like 'test.jpg', 'image.jpeg', 'photo.png'."
    Steps:
      - "1. Call `checkImage` for each valid filename."
    ExpectedResult:
      - "The function returns `true` for all cases."

  - CaseID: "U-HLP-UTL-202"
    Module: "utilitiesHelper"
    Description: "Should return false for invalid file extensions"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
    Precondition:
      - "Provide filenames like 'document.pdf', 'archive.zip'."
    Steps:
      - "1. Call `checkImage` for each invalid filename."
    ExpectedResult:
      - "The function returns `false` for all cases."
