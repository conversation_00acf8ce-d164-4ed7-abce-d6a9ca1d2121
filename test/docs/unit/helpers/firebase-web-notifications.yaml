TestFile: helpers/firebase-web-notifications.js
---
TestFunction: sendPushNotification
Cases:
  - CaseID: "U-HLP-FBN-101"
    Module: "firebaseWebNotificationsHelper"
    Description: "Should call the Firebase Admin SDK to send a notification successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "integration"
      - "firebase"
      - "push-notification"
    Precondition:
      - "A `registrationToken` and a `payload` object are provided."
      - "Mock `admin.messaging().sendToDevice` to resolve successfully."
      - "Mock the logger to spy on the success log."
    Steps:
      - "1. Call `sendPushNotification` with the token and payload."
    ExpectedResult:
      - "`admin.messaging().sendToDevice` is called once with the correct token and payload."
      - "A success message is logged."

  - CaseID: "U-HLP-FBN-102"
    Module: "firebaseWebNotificationsHelper"
    Description: "Should log an error if sending the notification fails"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `admin.messaging().sendToDevice` to reject with a new `Error('FCM Error')`."
      - "Mock the logger to spy on the error log."
    Steps:
      - "1. Call `sendPushNotification`."
    ExpectedResult:
      - "`admin.messaging().sendToDevice` is called."
      - "The error ('FCM Error') is caught and logged."
      - "The function does not throw an exception."
