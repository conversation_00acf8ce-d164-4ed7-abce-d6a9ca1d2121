TestFile: helpers/date.js
---
TestFunction: dateToString
Cases:
  - CaseID: "U-HLP-DTE-101"
    Module: "dateHelper"
    Description: "Should convert a provided Date object to its ISO string representation"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "date-formatting"
    Precondition:
      - "A specific Date object is created, e.g., `new Date('2023-01-01T00:00:00.000Z')`."
    Steps:
      - "1. Call `dateToString` with the created Date object."
    ExpectedResult:
      - "The function returns the correct ISO string: '2023-01-01T00:00:00.000Z'."

  - CaseID: "U-HLP-DTE-102"
    Module: "dateHelper"
    Description: "Should return the current date as an ISO string if no date is provided"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
      - "default-behavior"
    Precondition:
      - "Mock the global `Date` constructor to return a fixed date (e.g., `new Date('2025-01-15T10:00:00.000Z')`) to ensure a predictable result."
    Steps:
      - "1. Call `dateToString` with `null` or `undefined` as the argument."
    ExpectedResult:
      - "The function returns the ISO string of the mocked current date: '2025-01-15T10:00:00.000Z'."
