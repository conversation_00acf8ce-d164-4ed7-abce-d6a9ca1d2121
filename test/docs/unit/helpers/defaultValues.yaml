TestFile: helpers/defaultValues.js
---
TestFunction: addDefaultOptions
Cases:
  - CaseID: "U-HLP-DFV-101"
    Module: "defaultValuesHelper"
    Description: "Should add default options to a food item if default variations exist"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "database"
      - "side-effect"
    Precondition:
      - "A mock `food` object is provided, with a `restaurant` ID and a mock `save` method."
      - "Mock `Variation.find` to return an array of default variation documents."
      - "Mock `Option.find` to return an array of option documents linked to the variations."
    Steps:
      - "1. Call `addDefaultOptions` with the mock `food` object."
    ExpectedResult:
      - "`Variation.find` is called with `{ restaurant: food.restaurant, isDefault: true }`."
      - "`Option.find` is called with the IDs of the default variations."
      - "The `food.options` array is populated with the IDs of the found options."
      - "The `food.save` method is called once."

  - CaseID: "U-HLP-DFV-102"
    Module: "defaultValuesHelper"
    Description: "Should do nothing if no default variations exist for the restaurant"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
      - "no-op"
    Precondition:
      - "A mock `food` object is provided with a mock `save` method."
      - "Mock `Variation.find` to return an empty array `[]`."
      - "Spy on `Option.find` to ensure it's not called."
    Steps:
      - "1. Call `addDefaultOptions` with the mock `food` object."
    ExpectedResult:
      - "`Variation.find` is called."
      - "`Option.find` is never called."
      - "The `food.save` method is never called."
      - "The `food.options` array remains unchanged."
