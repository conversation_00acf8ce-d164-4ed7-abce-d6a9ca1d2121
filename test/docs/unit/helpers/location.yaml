TestFile: helpers/location.js
---
TestFunction: calculateDistance
Cases:
  - CaseID: "U-HLP-LOC-101"
    Module: "locationHelper"
    Description: "Should correctly calculate the distance between two known geographical points"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "pure-function"
      - "geospatial"
    Precondition:
      - "Provide coordinates for two known points (e.g., Paris and London)."
    Steps:
      - "1. Call `calculateDistance` with the coordinates."
    ExpectedResult:
      - "The function returns the expected distance (approximately 344 km), allowing for minor floating-point inaccuracies."

  - CaseID: "U-HLP-LOC-102"
    Module: "locationHelper"
    Description: "Should return 0 when calculating the distance between the same point"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "edge-case"
    Precondition:
      - "Provide the same latitude and longitude for both points."
    Steps:
      - "1. Call `calculateDatabase` with identical coordinates."
    ExpectedResult:
      - "The function returns 0."
---
TestFunction: isPointInPolygon
Cases:
  - CaseID: "U-HLP-LOC-201"
    Module: "locationHelper"
    Description: "Should return true for a point that is inside the polygon"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "polygon"
    Precondition:
      - "Define a simple square polygon."
      - "Define a point that is clearly inside the square."
    Steps:
      - "1. Call `isPointInPolygon` with the point and the polygon."
    ExpectedResult:
      - "The function returns `true`."

  - CaseID: "U-HLP-LOC-202"
    Module: "locationHelper"
    Description: "Should return false for a point that is outside the polygon"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
    Precondition:
      - "Define a simple square polygon."
      - "Define a point that is clearly outside the square."
    Steps:
      - "1. Call `isPointInPolygon`."
    ExpectedResult:
      - "The function returns `false`."
---
TestFunction: findNearest
Cases:
  - CaseID: "U-HLP-LOC-301"
    Module: "locationHelper"
    Description: "Should correctly find the nearest restaurant from a list"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "search"
    Precondition:
      - "A user's location (lat, lon) is defined."
      - "A list of mock restaurant objects is provided, each with a `location` property (coordinates)."
      - "The nearest restaurant in the list is known beforehand."
    Steps:
      - "1. Call `findNearest` with the user's location and the list of restaurants."
    ExpectedResult:
      - "The function returns the restaurant object that is closest to the user's location."

  - CaseID: "U-HLP-LOC-302"
    Module: "locationHelper"
    Description: "Should return null if the list of restaurants is empty"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "edge-case"
      - "empty-list"
    Precondition:
      - "A user's location is defined."
      - "An empty array `[]` is provided as the list of restaurants."
    Steps:
      - "1. Call `findNearest` with an empty list."
    ExpectedResult:
      - "The function returns `null`."
