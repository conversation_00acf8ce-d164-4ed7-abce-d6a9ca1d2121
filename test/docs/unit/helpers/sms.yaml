TestFile: helpers/sms.js
---
TestFunction: sendSms
Cases:
  - CaseID: "U-HLP-SMS-101"
    Module: "smsHelper"
    Description: "Should call the Twilio API with the correct parameters to send an SMS"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "integration"
      - "twilio"
      - "sms"
    Precondition:
      - "The `to` and `body` arguments are provided."
      - "Environment variables for Twilio (SID, TOKEN, FROM_NUMBER) are set."
      - "Mock the `twilio` client's `messages.create` method to resolve successfully."
    Steps:
      - "1. Call `sendSms` with the specified arguments."
    ExpectedResult:
      - "The `messages.create` method is called once."
      - "The argument passed to `create` is an object containing the correct `to`, `from`, and `body` fields."
      - "A success message is logged."

  - CaseID: "U-HLP-SMS-102"
    Module: "smsHelper"
    Description: "Should log an error if the Twilio API call fails"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `messages.create` to reject with a new `Error('Twilio API Error')`."
      - "Mock the logger to spy on the error log."
    Steps:
      - "1. Call `sendSms`."
    ExpectedResult:
      - "The error from the Twilio API is caught and logged."
      - "The function does not throw an exception."
