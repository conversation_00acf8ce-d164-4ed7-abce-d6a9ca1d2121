TestFile: helpers/email.js
---
TestFunction: sendEmail
Cases:
  - CaseID: "U-HLP-EML-101"
    Module: "emailHelper"
    Description: "Should call the SendGrid API with the correct parameters to send an email"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "integration"
      - "sendgrid"
    Precondition:
      - "Mock `sgMail.send` to resolve successfully."
      - "The `to`, `subject`, and `html` arguments are provided."
    Steps:
      - "1. Call `sendEmail` with the specified arguments."
    ExpectedResult:
      - "`sgMail.send` is called once."
      - "The argument passed to `sgMail.send` is an object containing the correct `to`, `from`, `subject`, and `html` fields."

  - CaseID: "U-HLP-EML-102"
    Module: "emailHelper"
    Description: "Should log an error if the SendGrid API call fails"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `sgMail.send` to reject with a new `Error('API Error')`."
      - "Mock the logger to spy on the error logging."
    Steps:
      - "1. Call `sendEmail`."
    ExpectedResult:
      - "`sgMail.send` is called."
      - "The error ('API Error') is caught and logged."
      - "The function does not throw an exception."
