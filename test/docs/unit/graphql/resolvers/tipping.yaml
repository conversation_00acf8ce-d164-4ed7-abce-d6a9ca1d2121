TestFile: graphql/resolvers/tipping.js
---
TestFunction: tippings (Query)
Cases:
  - CaseID: "U-GQL-TIP-101"
    Module: "tippingResolver"
    Description: "Should fetch all available tipping options"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "tipping"
    Precondition:
      - "Mock `Tipping.find` to return an array of tipping documents."
    Steps:
      - "1. Call the `tippings` resolver."
    ExpectedResult:
      - "`Tipping.find` is called."
      - "Returns an array of transformed tipping objects."
---
TestFunction: createTipping (Mutation)
Cases:
  - CaseID: "U-GQL-TIP-201"
    Module: "tippingResolver"
    Description: "Should create a new tipping option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "tipping"
    Precondition:
      - "User is authenticated."
      - "Valid `tippingInput` is provided."
      - "Mock the `save` method of the Tipping model."
    Steps:
      - "1. Call `createTipping` with `tippingInput`."
    ExpectedResult:
      - "A new `Tipping` instance is created."
      - "The `save` method is called."
      - "Returns the transformed new tipping object."
---
TestFunction: updateTipping (Mutation)
Cases:
  - CaseID: "U-GQL-TIP-301"
    Module: "tippingResolver"
    Description: "Should update an existing tipping option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid tipping `id` and `tippingInput` are provided."
      - "Mock `Tipping.findByIdAndUpdate` to return the updated document."
    Steps:
      - "1. Call `updateTipping` with the ID and new data."
    ExpectedResult:
      - "`Tipping.findByIdAndUpdate` is called."
      - "Returns the transformed, updated tipping object."
---
TestFunction: deleteTipping (Mutation)
Cases:
  - CaseID: "U-GQL-TIP-401"
    Module: "tippingResolver"
    Description: "Should delete a tipping option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid tipping `id` is provided."
      - "Mock `Tipping.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteTipping` with the ID."
    ExpectedResult:
      - "`Tipping.findByIdAndRemove` is called."
      - "Returns `true`."
