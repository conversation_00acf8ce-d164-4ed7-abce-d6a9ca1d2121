TestFile: graphql/resolvers/address.js
---
TestFunction: createAddress
Cases:
  - CaseID: "U-ADDRESS-101"
    Module: "graphql-resolver"
    Description: "认证用户成功创建一个新地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回一个有效的用户实例"
      - "Mock User.updateMany() 成功执行"
      - "用户实例的 save() 方法能成功执行"
      - "Mock transformUser() 能正确转换用户对象"
    Steps:
      - "调用 createAddress mutation 并提供有效的 addressInput"
    ExpectedResult:
      - "新地址被添加到用户实例的 addresses 数组中"
      - "用户实例的 save() 方法被调用一次"
      - "返回转换后的用户对象"
  - CaseID: "U-ADDRESS-102"
    Module: "graphql-resolver"
    Description: "未认证用户尝试创建地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "auth"]
    Precondition:
      - "请求未认证 (req.isAuth = false)"
    Steps:
      - "调用 createAddress mutation"
    ExpectedResult:
      - "抛出 'Unauthenticated' 错误"
  - CaseID: "U-ADDRESS-103"
    Module: "graphql-resolver"
    Description: "认证用户在数据库中不存在"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "error-handling"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回 null"
    Steps:
      - "调用 createAddress mutation"
    ExpectedResult:
      - "抛出 'User not found' 错误"
---
TestFunction: editAddress
Cases:
  - CaseID: "U-ADDRESS-201"
    Module: "graphql-resolver"
    Description: "认证用户成功编辑一个现有地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回一个包含目标地址的用户实例"
      - "用户实例的 save() 方法能成功执行"
    Steps:
      - "调用 editAddress mutation 并提供有效的 addressInput (包括 _id)"
    ExpectedResult:
      - "目标地址的数据被成功更新"
      - "用户实例的 save() 方法被调用一次"
      - "返回转换后的用户对象"
  - CaseID: "U-ADDRESS-202"
    Module: "graphql-resolver"
    Description: "未认证用户尝试编辑地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "auth"]
    Precondition:
      - "请求未认证 (req.isAuth = false)"
    Steps:
      - "调用 editAddress mutation"
    ExpectedResult:
      - "抛出 'Unauthenticated' 错误"
---
TestFunction: deleteAddress
Cases:
  - CaseID: "U-ADDRESS-301"
    Module: "graphql-resolver"
    Description: "认证用户成功删除一个地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回一个包含目标地址的用户实例"
      - "用户实例的 save() 方法能成功执行"
    Steps:
      - "调用 deleteAddress mutation 并提供有效的地址 id"
    ExpectedResult:
      - "目标地址从用户实例的 addresses 数组中被移除"
      - "用户实例的 save() 方法被调用一次"
      - "返回转换后的用户对象"
  - CaseID: "U-ADDRESS-302"
    Module: "graphql-resolver"
    Description: "未认证用户尝试删除地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "auth"]
    Precondition:
      - "请求未认证 (req.isAuth = false)"
    Steps:
      - "调用 deleteAddress mutation"
    ExpectedResult:
      - "抛出 'Unauthenticated' 错误"
---
TestFunction: deleteBulkAddresses
Cases:
  - CaseID: "U-ADDRESS-401"
    Module: "graphql-resolver"
    Description: "认证用户成功批量删除多个地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回一个包含所有目标地址的用户实例"
      - "用户实例的 save() 方法能成功执行"
    Steps:
      - "调用 deleteBulkAddresses mutation 并提供一个有效的 id 数组"
    ExpectedResult:
      - "所有目标地址从用户实例的 addresses 数组中被移除"
      - "用户实例的 save() 方法被调用一次"
      - "返回转换后的用户对象"
  - CaseID: "U-ADDRESS-402"
    Module: "graphql-resolver"
    Description: "批量删除时提供的 id 数组为空"
    Importance: "Medium"
    Status: "Planned"
    Tags: ["edge-case", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.findById() 返回一个有效的用户实例"
    Steps:
      - "调用 deleteBulkAddresses mutation 并提供一个空数组"
    ExpectedResult:
      - "用户实例的 save() 方法不被调用"
      - "直接返回转换后的、未被修改的用户对象"
---
TestFunction: selectAddress
Cases:
  - CaseID: "U-ADDRESS-501"
    Module: "graphql-resolver"
    Description: "认证用户成功选择一个地址作为当前地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "请求已认证 (req.isAuth = true)"
      - "Mock User.update() 和 User.findById() 成功执行"
      - "Mock User.findById() 返回一个包含目标地址的用户实例"
      - "用户实例的 save() 方法能成功执行"
    Steps:
      - "调用 selectAddress mutation 并提供有效的地址 id"
    ExpectedResult:
      - "User.update() 被调用以取消所有地址的选中状态"
      - "目标地址的 selected 属性被设置为 true"
      - "用户实例的 save() 方法被调用一次"
      - "返回转换后的用户对象"
  - CaseID: "U-ADDRESS-502"
    Module: "graphql-resolver"
    Description: "未认证用户尝试选择地址"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "auth"]
    Precondition:
      - "请求未认证 (req.isAuth = false)"
    Steps:
      - "调用 selectAddress mutation"
    ExpectedResult:
      - "抛出 'Unauthenticated' 错误"
