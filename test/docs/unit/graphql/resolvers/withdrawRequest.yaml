TestFile: graphql/resolvers/withdrawRequest.js
---
TestFunction: withdrawRequests (Query)
Cases:
  - CaseID: "U-GQL-WDR-101"
    Module: "withdrawRequestResolver"
    Description: "Should allow an admin to fetch all withdraw requests with a status filter"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "admin"
      - "authorization"
    Precondition:
      - "User is authenticated with 'ADMIN' role."
      - "A `status` argument is provided."
      - "Mock `WithdrawRequest.find` to be called with the status filter."
    Steps:
      - "1. Call `withdrawRequests` as an admin with a status filter."
    ExpectedResult:
      - "Returns a paginated list of all requests matching the status."
  - CaseID: "U-GQL-WDR-102"
    Module: "withdrawRequestResolver"
    Description: "Should allow a rider/restaurant to fetch their own withdraw requests"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "authorization"
      - "owner"
    Precondition:
      - "User is authenticated with 'RIDER' or 'RESTAURANT' role."
      - "Mock `WithdrawRequest.find` to be called with a filter for the user's ID."
    Steps:
      - "1. Call `withdrawRequests` as a rider or restaurant."
    ExpectedResult:
      - "Returns a list of their own requests only."
---
TestFunction: createWithdrawRequest (Mutation)
Cases:
  - CaseID: "U-GQL-WDR-201"
    Module: "withdrawRequestResolver"
    Description: "Should create a withdraw request and update the user's wallet balance"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "withdraw"
      - "wallet"
    Precondition:
      - "User is authenticated."
      - "Mock `User.findById` to return a user with sufficient `wallet.balance`."
      - "Mock `WithdrawRequest.findOne` to return `null` (no pending requests)."
      - "Mock the `save` methods for the User and WithdrawRequest models."
    Steps:
      - "1. Call `createWithdrawRequest` with an amount."
    ExpectedResult:
      - "A new `WithdrawRequest` is created with 'PENDING' status."
      - "The user's `wallet.balance` is decreased by the amount."
      - "The user's `wallet.withdrawn` is increased by the amount."
      - "The user's `save` method is called."
      - "Returns the transformed new request object."
  - CaseID: "U-GQL-WDR-202"
    Module: "withdrawRequestResolver"
    Description: "Should throw an error if the user has insufficient balance"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
      - "balance"
    Precondition:
      - "User is authenticated."
      - "Mock `User.findById` to return a user with `wallet.balance` less than the requested amount."
    Steps:
      - "1. Call `createWithdrawRequest`."
    ExpectedResult:
      - "Throws an error with the message 'Insufficient balance'."
---
TestFunction: updateWithdrawRequest (Mutation)
Cases:
  - CaseID: "U-GQL-WDR-301"
    Module: "withdrawRequestResolver"
    Description: "Should allow an admin to approve a request and update balances accordingly"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "admin"
      - "approval"
    Precondition:
      - "User is authenticated as 'ADMIN'."
      - "Mock `WithdrawRequest.findById` to return a 'PENDING' request."
      - "Mock `User.findById` to return the associated user."
      - "Mock the `save` methods for both models."
    Steps:
      - "1. Call `updateWithdrawRequest` with the request ID and status 'COMPLETED'."
    ExpectedResult:
      - "The request's status is updated to 'COMPLETED'."
      - "The user's `wallet.withdrawn` is decreased by the request amount."
      - "Returns the transformed, updated request object."
  - CaseID: "U-GQL-WDR-302"
    Module: "withdrawRequestResolver"
    Description: "Should allow an admin to cancel a request and refund the balance"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "admin"
      - "cancellation"
    Precondition:
      - "User is authenticated as 'ADMIN'."
      - "Mock `WithdrawRequest.findById` to return a 'PENDING' request."
      - "Mock `User.findById` to return the associated user."
    Steps:
      - "1. Call `updateWithdrawRequest` with the request ID and status 'CANCELLED'."
    ExpectedResult:
      - "The request's status is updated to 'CANCELLED'."
      - "The user's `wallet.withdrawn` is decreased."
      - "The user's `wallet.balance` is increased (refunded)."
      - "Returns the transformed, updated request object."
