TestFile: graphql/resolvers/option.js
---
TestFunction: options (Query)
Cases:
  - CaseID: "U-GQL-OPT-101"
    Module: "optionResolver"
    Description: "Should fetch all options for a specific restaurant"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "option"
      - "filter"
    Precondition:
      - "A valid `restaurant` ID is provided."
      - "Mock `Option.find` to return an array of option documents for that restaurant."
    Steps:
      - "1. Call the `options` resolver with a `restaurant` ID."
    ExpectedResult:
      - "`Option.find` is called with `{ restaurant: restaurantId }`."
      - "Returns an array of transformed option objects."
---
TestFunction: createOption (Mutation)
Cases:
  - CaseID: "U-GQL-OPT-201"
    Module: "optionResolver"
    Description: "Should create a new option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "option"
    Precondition:
      - "User is authenticated."
      - "Valid `optionInput` is provided."
      - "Mock the `save` method of the Option model."
    Steps:
      - "1. Call `createOption` with `optionInput`."
    ExpectedResult:
      - "A new `Option` instance is created."
      - "The `save` method is called."
      - "Returns the transformed new option object."
---
TestFunction: updateOption (Mutation)
Cases:
  - CaseID: "U-GQL-OPT-301"
    Module: "optionResolver"
    Description: "Should update an existing option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid option `id` and `optionInput` are provided."
      - "Mock `Option.findByIdAndUpdate` to return the updated document."
    Steps:
      - "1. Call `updateOption` with the ID and new data."
    ExpectedResult:
      - "`Option.findByIdAndUpdate` is called."
      - "Returns the transformed, updated option object."
---
TestFunction: deleteOption (Mutation)
Cases:
  - CaseID: "U-GQL-OPT-401"
    Module: "optionResolver"
    Description: "Should delete an option successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid option `id` is provided."
      - "Mock `Option.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteOption` with the ID."
    ExpectedResult:
      - "`Option.findByIdAndRemove` is called."
      - "Returns `true`."
