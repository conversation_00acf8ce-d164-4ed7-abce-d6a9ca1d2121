TestFile: graphql/resolvers/zone.js
---
TestFunction: zones (Query)
Cases:
  - CaseID: "U-GQL-ZON-101"
    Module: "zoneResolver"
    Description: "Should fetch all available zones"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "zone"
      - "geospatial"
    Precondition:
      - "Mock `Zone.find` to return an array of zone documents."
    Steps:
      - "1. Call the `zones` resolver."
    ExpectedResult:
      - "`Zone.find` is called."
      - "Returns an array of transformed zone objects."
---
TestFunction: createZone (Mutation)
Cases:
  - CaseID: "U-GQL-ZON-201"
    Module: "zoneResolver"
    Description: "Should create a new zone with valid coordinates successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "zone"
    Precondition:
      - "User is authenticated."
      - "Valid `zoneInput` is provided, including a name and a GeoJSON-compliant coordinates array."
      - "Mock the `save` method of the Zone model."
    Steps:
      - "1. Call `createZone` with `zoneInput`."
    ExpectedResult:
      - "A new `Zone` instance is created with the provided name and coordinates."
      - "The `save` method is called."
      - "Returns the transformed new zone object."
---
TestFunction: updateZone (Mutation)
Cases:
  - CaseID: "U-GQL-ZON-301"
    Module: "zoneResolver"
    Description: "Should update an existing zone successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid zone `id` and `zoneInput` are provided."
      - "Mock `Zone.findByIdAndUpdate` to return the updated document."
    Steps:
      - "1. Call `updateZone` with the ID and new data."
    ExpectedResult:
      - "`Zone.findByIdAndUpdate` is called."
      - "Returns the transformed, updated zone object."
---
TestFunction: deleteZone (Mutation)
Cases:
  - CaseID: "U-GQL-ZON-401"
    Module: "zoneResolver"
    Description: "Should delete a zone successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid zone `id` is provided."
      - "Mock `Zone.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteZone` with the ID."
    ExpectedResult:
      - "`Zone.findByIdAndRemove` is called."
      - "Returns `true`."
