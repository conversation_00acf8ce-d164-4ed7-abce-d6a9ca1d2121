TestFile: graphql/resolvers/customer.js
---
TestFunction: customer (Query)
Cases:
  - CaseID: "U-GQL-CUS-101"
    Module: "customerResolver"
    Description: "Should fetch a single customer by ID successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "customer"
    Precondition:
      - "User is authenticated (req.isAuth is true)."
      - "A valid customer `id` is provided in the arguments."
      - "Mock `Customer.findById` to return a valid customer document."
    Steps:
      - "1. Call the `customer` resolver with args containing the customer ID."
    ExpectedResult:
      - "`Customer.findById` is called with the provided ID."
      - "The `transformCustomer` function is called with the found customer document."
      - "Returns a transformed customer object matching the GraphQL schema."
  - CaseID: "U-GQL-CUS-102"
    Module: "customerResolver"
    Description: "Should throw an error if the customer is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Customer.findById` to return null."
    Steps:
      - "1. Call the `customer` resolver."
    ExpectedResult:
      - "Throws an error with the message 'Customer not found.'."
---
TestFunction: createCustomer (Mutation)
Cases:
  - CaseID: "U-GQL-CUS-201"
    Module: "customerResolver"
    Description: "Should create a new customer successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "customer"
    Precondition:
      - "User is authenticated."
      - "Valid `customerInput` is provided."
      - "Mock `Customer.findOne` for email check to return null."
      - "Mock `Customer.save` to return the new customer document."
    Steps:
      - "1. Call the `createCustomer` resolver with `customerInput`."
    ExpectedResult:
      - "A new `Customer` instance is created."
      - "The `save` method is called."
      - "Returns a transformed customer object."
  - CaseID: "U-GQL-CUS-202"
    Module: "customerResolver"
    Description: "Should throw an error if the email already exists"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "User is authenticated."
      - "Mock `Customer.findOne` to return an existing customer document."
    Steps:
      - "1. Call `createCustomer` with an existing email."
    ExpectedResult:
      - "Throws an error with the message 'E-mail address already exists.'."
---
TestFunction: addAddress (Mutation)
Cases:
  - CaseID: "U-GQL-CUS-301"
    Module: "customerResolver"
    Description: "Should add a new address to a customer"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "address"
    Precondition:
      - "User is authenticated (req.userId is set)."
      - "Valid `addressInput` is provided."
      - "Mock `Customer.findById` to return a customer document."
      - "Mock `validateAddressInput` to resolve successfully."
      - "Mock `decodePostcode` to return location data."
      - "Mock `customer.save` to resolve successfully."
    Steps:
      - "1. Call `addAddress` with `addressInput`."
    ExpectedResult:
      - "The customer's `addresses` array contains the new address."
      - "If `isDefault` is true, other addresses are set to not be default."
      - "The `save` method is called on the customer document."
      - "Returns the transformed customer object."
  - CaseID: "U-GQL-CUS-302"
    Module: "customerResolver"
    Description: "Should throw an error if address validation fails"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "User is authenticated."
      - "Mock `validateAddressInput` to throw an error."
    Steps:
      - "1. Call `addAddress` with invalid input."
    ExpectedResult:
      - "Throws the error from `validateAddressInput`."
---
TestFunction: getCustomerByWhatsApp (Query)
Cases:
  - CaseID: "U-GQL-CUS-401"
    Module: "customerResolver"
    Description: "Should fetch a customer by WhatsApp ID with valid session"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "whatsapp"
    Precondition:
      - "The resolver is wrapped with `withWhatsAppAuth`."
      - "A valid WhatsApp session is established (context has `waId`)."
      - "Mock `Customer.findOne` to return a customer document based on `waId`."
    Steps:
      - "1. Call the `getCustomerByWhatsApp` resolver via a GraphQL query."
    ExpectedResult:
      - "`Customer.findOne` is called with `{ 'addresses.phone': waId }`."
      - "Returns the transformed customer object."
  - CaseID: "U-GQL-CUS-402"
    Module: "customerResolver"
    Description: "Should be protected by withWhatsAppAuth wrapper"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "whatsapp"
    Precondition:
      - "No valid WhatsApp session ID is provided in the request headers."
    Steps:
      - "1. Call the `getCustomerByWhatsApp` resolver."
    ExpectedResult:
      - "The `withWhatsAppAuth` wrapper throws an 'Unauthorized' error."
      - "The core logic of `getCustomerByWhatsApp` is not executed."
---
TestFunction: transformCustomer (Helper)
Cases:
  - CaseID: "U-GQL-CUS-501"
    Module: "customerResolver"
    Description: "Should correctly transform a Mongoose customer document"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "helper"
      - "transform"
    Precondition:
      - "A raw customer document from Mongoose is provided, including nested addresses and orders."
    Steps:
      - "1. Call `transformCustomer` with the Mongoose document."
    ExpectedResult:
      - "Returns a clean JavaScript object."
      - "`_id` is mapped to `id`."
      - "Date fields are converted to ISO strings."
      - "Nested `addresses` and `orders` are also transformed correctly via their respective transform functions."
      - "No Mongoose-specific properties (`__v`, etc.) are present in the output."
