TestFile: graphql/resolvers/countries.js
---
TestFunction: countries (Query)
Cases:
  - CaseID: "U-GQL-GEO-101"
    Module: "countriesResolver"
    Description: "Should return a list of all countries"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "countries"
    Precondition:
      - "Mock the `getCountries` function from 'countries-states-cities' to return a predefined array of country objects."
    Steps:
      - "1. Call the `countries` resolver."
    ExpectedResult:
      - "The resolver returns the mocked array of countries."
---
TestFunction: country (Query)
Cases:
  - CaseID: "U-GQL-GEO-201"
    Module: "countriesResolver"
    Description: "Should return a single country for a valid ISO code"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "countries"
    Precondition:
      - "A valid `isoCode` is provided."
      - "Mock `getCountry` to return a specific country object for the given code."
    Steps:
      - "1. Call the `country` resolver with the `isoCode`."
    ExpectedResult:
      - "The resolver returns the mocked country object."
  - CaseID: "U-GQL-GEO-202"
    Module: "countriesResolver"
    Description: "Should throw an ApolloError if the country is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "An invalid `isoCode` is provided."
      - "Mock `getCountry` to return null."
    Steps:
      - "1. Call the `country` resolver with the invalid `isoCode`."
    ExpectedResult:
      - "Throws an ApolloError with the message 'Country not found'."
---
TestFunction: states (Query)
Cases:
  - CaseID: "U-GQL-GEO-301"
    Module: "countriesResolver"
    Description: "Should return a list of states for a valid country ISO code"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "states"
    Precondition:
      - "A valid `countryIsoCode` is provided."
      - "Mock `getStates` to return a predefined array of state objects."
    Steps:
      - "1. Call the `states` resolver with the `countryIsoCode`."
    ExpectedResult:
      - "The resolver returns the mocked array of states."
---
TestFunction: cities (Query)
Cases:
  - CaseID: "U-GQL-GEO-401"
    Module: "countriesResolver"
    Description: "Should return a list of cities for a valid country and state"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "cities"
    Precondition:
      - "Valid `countryIsoCode` and `stateName` are provided."
      - "Mock `getCities` to return a predefined array of city names."
    Steps:
      - "1. Call the `cities` resolver with `countryIsoCode` and `stateName`."
    ExpectedResult:
      - "The resolver returns the mocked array of city names."
