TestFile: graphql/resolvers/dashboard.js
---
TestFunction: dashboard (Query)
Cases:
  - CaseID: "U-GQL-DSH-101"
    Module: "dashboardResolver"
    Description: "Should return aggregated dashboard data for an authenticated user"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "dashboard"
      - "aggregation"
    Precondition:
      - "User is authenticated."
      - "Mock `moment` to return predictable dates to ensure consistent date range calculations."
      - "Mock `Order.aggregate` for each of the stats (totalSales, totalOrders, todaysSales, etc.) to return specific, predictable values."
      - "Mock the `Order.find` call for `latestOrders` to return an array of order documents."
      - "Mock the `transformOrder` helper function."
    Steps:
      - "1. Call the `dashboard` resolver."
    ExpectedResult:
      - "All `Order.aggregate` calls are made with the correct date ranges and match conditions (e.g., status: 'COMPLETED')."
      - "The resolver returns a `Dashboard` object containing the correctly summed and counted values from the mocked aggregation results."
      - "The `latestOrders` array contains transformed order objects."
  - CaseID: "U-GQL-DSH-102"
    Module: "dashboardResolver"
    Description: "Should return zero for sales and counts when there are no orders"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
      - "zero-state"
    Precondition:
      - "User is authenticated."
      - "Mock `Order.aggregate` for all stats to return an empty array `[]` (simulating no matching orders)."
      - "Mock `Order.find` for `latestOrders` to return an empty array."
    Steps:
      - "1. Call the `dashboard` resolver."
    ExpectedResult:
      - "The resolver returns a `Dashboard` object where all sales and order count fields are `0`."
      - "`latestOrders` is an empty array."
  - CaseID: "U-GQL-DSH-103"
    Module: "dashboardResolver"
    Description: "Should throw an error if the user is not authenticated"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "authentication"
    Precondition:
      - "The request context's `isAuth` property is `false`."
    Steps:
      - "1. Call the `dashboard` resolver."
    ExpectedResult:
      - "Throws an error with the message 'Not authenticated!'."
