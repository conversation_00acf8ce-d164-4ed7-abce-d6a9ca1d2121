TestFile: graphql/resolvers/banner.js
---
TestFunction: banners (Query)
Cases:
  - CaseID: "U-GQL-BNR-101"
    Module: "bannerResolver"
    Description: "Should fetch all active banners for a given platform"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "banner"
      - "filter"
    Precondition:
      - "User is authenticated."
      - "`isActive` is true and `platform` is 'CUSTOMER'."
      - "Mock `Banner.find` to return an array of banner documents matching the query."
    Steps:
      - "1. Call the `banners` resolver with `isActive: true` and `platform: 'CUSTOMER'`."
    ExpectedResult:
      - "`Banner.find` is called with `{ isActive: true, platform: 'CUSTOMER' }`."
      - "The resolver returns an array of transformed banner objects."
---
TestFunction: createBanner (Mutation)
Cases:
  - CaseID: "U-GQL-BNR-201"
    Module: "bannerResolver"
    Description: "Should create a new banner successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "banner"
    Precondition:
      - "User is authenticated."
      - "Valid `bannerInput` is provided."
      - "Mock the `save` method of the Banner model."
    Steps:
      - "1. Call the `createBanner` resolver with `bannerInput`."
    ExpectedResult:
      - "A new `Banner` instance is created with the input data."
      - "The `save` method is called."
      - "Returns a transformed banner object of the newly created banner."
---
TestFunction: updateBanner (Mutation)
Cases:
  - CaseID: "U-GQL-BNR-301"
    Module: "bannerResolver"
    Description: "Should update an existing banner successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "banner"
    Precondition:
      - "User is authenticated."
      - "A valid banner `id` and `bannerInput` are provided."
      - "Mock `Banner.findById` to return a banner document."
      - "Mock the `save` method on the found banner document."
    Steps:
      - "1. Call `updateBanner` with the banner ID and new data."
    ExpectedResult:
      - "The fields of the found banner document are updated."
      - "The `save` method is called."
      - "Returns the transformed, updated banner object."
  - CaseID: "U-GQL-BNR-302"
    Module: "bannerResolver"
    Description: "Should throw an error if the banner to update is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Banner.findById` to return null."
    Steps:
      - "1. Call `updateBanner` with a non-existent banner ID."
    ExpectedResult:
      - "Throws an error with the message 'Banner not found.'."
---
TestFunction: deleteBanner (Mutation)
Cases:
  - CaseID: "U-GQL-BNR-401"
    Module: "bannerResolver"
    Description: "Should delete a banner successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "banner"
    Precondition:
      - "User is authenticated."
      - "A valid banner `id` is provided."
      - "Mock `Banner.findById` to return a banner document."
      - "Mock `Banner.deleteOne` to resolve successfully."
    Steps:
      - "1. Call `deleteBanner` with the banner ID."
    ExpectedResult:
      - "`Banner.deleteOne` is called with the `_id` of the found banner."
      - "Returns `true`."
  - CaseID: "U-GQL-BNR-402"
    Module: "bannerResolver"
    Description: "Should throw an error if the banner to delete is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Banner.findById` to return null."
    Steps:
      - "1. Call `deleteBanner` with a non-existent banner ID."
    ExpectedResult:
      - "Throws an error with the message 'Banner not found.'."
