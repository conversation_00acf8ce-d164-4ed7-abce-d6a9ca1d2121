TestFile: graphql/resolvers/dispatch.js
---
TestFunction: getAvailableRiders (Query)
Cases:
  - CaseID: "U-GQL-DSP-101"
    Module: "dispatchResolver"
    Description: "Should return a list of available riders near the order's restaurant"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "dispatch"
      - "rider"
      - "geospatial"
    Precondition:
      - "User is authenticated."
      - "A valid `orderId` is provided."
      - "Mock `Order.findById` to return an order with a populated restaurant that has location coordinates."
      - "Mock `Rider.find` to return an array of available, online riders within the search radius."
    Steps:
      - "1. Call the `getAvailableRiders` resolver with the `orderId`."
    ExpectedResult:
      - "`Rider.find` is called with a `$nearSphere` geospatial query."
      - "Returns an array of transformed rider objects."
  - CaseID: "U-GQL-DSP-102"
    Module: "dispatchResolver"
    Description: "Should return an empty array if no riders are found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
    Precondition:
      - "Mock `Rider.find` to return an empty array."
    Steps:
      - "1. Call `getAvailableRiders`."
    ExpectedResult:
      - "Returns an empty array `[]`."
  - CaseID: "U-GQL-DSP-103"
    Module: "dispatchResolver"
    Description: "Should throw an error if the order is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "Mock `Order.findById` to return null."
    Steps:
      - "1. Call `getAvailableRiders` with a non-existent order ID."
    ExpectedResult:
      - "Throws an error with the message 'Order not found'."
---
TestFunction: requestRiderForOrder (Mutation)
Cases:
  - CaseID: "U-GQL-DSP-201"
    Module: "dispatchResolver"
    Description: "Should successfully assign a rider to an order"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "dispatch"
      - "assignment"
    Precondition:
      - "User is authenticated."
      - "Valid `orderId` and `riderId` are provided."
      - "Mock `Order.findById` to return an unassigned order."
      - "Mock `Rider.findById` to return an available rider."
      - "Mock the `save` method for both the order and rider documents."
      - "Mock `pubsub.publish`."
    Steps:
      - "1. Call `requestRiderForOrder`."
    ExpectedResult:
      - "The order's `rider` field is set to the rider's ID."
      - "The order's `status` is updated to 'ACCEPTED'."
      - "The rider's `available` status is set to `false`."
      - "`pubsub.publish` is called with the 'ORDER_STATUS_CHANGED' topic."
      - "Returns the transformed, updated order object."
  - CaseID: "U-GQL-DSP-202"
    Module: "dispatchResolver"
    Description: "Should throw an error if the order is already assigned"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "conflict"
    Precondition:
      - "Mock `Order.findById` to return an order that already has a `rider` assigned."
    Steps:
      - "1. Call `requestRiderForOrder`."
    ExpectedResult:
      - "Throws an error with the message 'Order already assigned'."
