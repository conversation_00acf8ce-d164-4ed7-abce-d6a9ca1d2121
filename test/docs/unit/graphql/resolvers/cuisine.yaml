TestFile: graphql/resolvers/cuisine.js
---
TestFunction: cuisines (Query)
Cases:
  - CaseID: "U-GQL-CUI-101"
    Module: "cuisineResolver"
    Description: "Should fetch a list of all cuisines without any search term"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "cuisine"
    Precondition:
      - "Mock `Cuisine.find` to return an array of cuisine documents."
    Steps:
      - "1. Call the `cuisines` resolver without a `search` argument."
    ExpectedResult:
      - "`Cuisine.find` is called with an empty query object."
      - "Returns an array of transformed cuisine objects."
  - CaseID: "U-GQL-CUI-102"
    Module: "cuisineResolver"
    Description: "Should fetch cuisines filtered by a search term"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "search"
    Precondition:
      - "A `search` term is provided."
      - "Mock `Cuisine.find` to handle a regex search on the 'name' field."
    Steps:
      - "1. Call the `cuisines` resolver with a `search` argument."
    ExpectedResult:
      - "`Cuisine.find` is called with a regex query."
      - "Returns a filtered list of transformed cuisine objects."
---
TestFunction: createCuisine (Mutation)
Cases:
  - CaseID: "U-GQL-CUI-201"
    Module: "cuisineResolver"
    Description: "Should create a new cuisine successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "cuisine"
    Precondition:
      - "User is authenticated."
      - "Valid `cuisineInput` is provided."
      - "Mock the `save` method of the Cuisine model."
    Steps:
      - "1. Call `createCuisine` with `cuisineInput`."
    ExpectedResult:
      - "A new `Cuisine` instance is created."
      - "The `save` method is called."
      - "Returns the transformed new cuisine object."
---
TestFunction: updateCuisine (Mutation)
Cases:
  - CaseID: "U-GQL-CUI-301"
    Module: "cuisineResolver"
    Description: "Should update an existing cuisine successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid cuisine `id` and `cuisineInput` are provided."
      - "Mock `Cuisine.findByIdAndUpdate` to return the updated document."
    Steps:
      - "1. Call `updateCuisine` with the ID and new data."
    ExpectedResult:
      - "`Cuisine.findByIdAndUpdate` is called."
      - "Returns the transformed, updated cuisine object."
---
TestFunction: deleteCuisine (Mutation)
Cases:
  - CaseID: "U-GQL-CUI-401"
    Module: "cuisineResolver"
    Description: "Should delete a cuisine successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid cuisine `id` is provided."
      - "Mock `Cuisine.findById` to return a document."
      - "Mock `Cuisine.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteCuisine` with the ID."
    ExpectedResult:
      - "`Cuisine.findByIdAndRemove` is called."
      - "Returns the transformed object of the deleted cuisine."
