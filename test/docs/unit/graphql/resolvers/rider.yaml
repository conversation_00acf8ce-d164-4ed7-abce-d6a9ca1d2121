TestFile: graphql/resolvers/rider.js
---
TestFunction: riderLogin (Mutation)
Cases:
  - CaseID: "U-GQL-RDR-101"
    Module: "riderResolver"
    Description: "Should log in a rider with correct credentials and return a token"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "authentication"
      - "login"
    Precondition:
      - "Valid `email` and `password` are provided."
      - "Mock `Rider.findOne` to return a rider document with a hashed password."
      - "Mock `bcrypt.compare` to return `true`."
      - "Mock `jwt.sign`."
    Steps:
      - "1. Call `riderLogin` with correct credentials."
    ExpectedResult:
      - "Returns an object containing `userId`, `token`, and `tokenExpiration` with the role 'RIDER'."
---
TestFunction: updateRiderLocation (Mutation)
Cases:
  - CaseID: "U-GQL-RDR-201"
    Module: "riderResolver"
    Description: "Should update a rider's location and publish an event to PubSub"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "location"
      - "pubsub"
      - "real-time"
    Precondition:
      - "User is authenticated as a rider."
      - "Valid `latitude` and `longitude` are provided."
      - "Mock `Rider.findByIdAndUpdate` to update the rider's location field."
      - "Mock `pubsub.publish`."
    Steps:
      - "1. Call `updateRiderLocation` with new coordinates."
    ExpectedResult:
      - "`Rider.findByIdAndUpdate` is called with the rider's ID and a GeoJSON Point object for the location."
      - "`pubsub.publish` is called with the 'RIDER_LOCATION_UPDATED' topic and a payload containing the rider's ID and new location."
      - "Returns `true`."
---
TestFunction: onRiderLocationUpdated (Subscription)
Cases:
  - CaseID: "U-GQL-RDR-301"
    Module: "riderResolver"
    Description: "Should receive location updates for riders within the subscribed zone"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "subscription"
      - "filter"
      - "geospatial"
    Precondition:
      - "Subscription is initiated with a `zoneId`."
      - "Mock `Zone.findById` to return a zone document with a GeoJSON polygon for its coordinates."
      - "A `RIDER_LOCATION_UPDATED` event is published with a rider's location that is inside the zone's polygon."
    Steps:
      - "1. Initiate the `onRiderLocationUpdated` subscription."
      - "2. Trigger a PubSub event for a rider inside the zone."
    ExpectedResult:
      - "The subscription's `withFilter` function uses a geospatial library (like `point-in-polygon`) to check if the rider's location is within the zone's coordinates."
      - "The filter returns `true`, and the client receives the location update payload."
  - CaseID: "U-GQL-RDR-302"
    Module: "riderResolver"
    Description: "Should not receive location updates for riders outside the subscribed zone"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "subscription"
      - "filter"
    Precondition:
      - "Subscription is initiated with a `zoneId`."
      - "Mock `Zone.findById` as in the positive case."
      - "A `RIDER_LOCATION_UPDATED` event is published with a rider's location that is outside the zone's polygon."
    Steps:
      - "1. Initiate the subscription."
      - "2. Trigger a PubSub event for a rider outside the zone."
    ExpectedResult:
      - "The `withFilter` function returns `false`, and the client does not receive the update."
