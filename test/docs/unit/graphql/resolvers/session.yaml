TestFile: graphql/resolvers/session.js
---
TestFunction: getSessionState (Query)
Cases:
  - CaseID: "U-GQL-SES-101"
    Module: "sessionResolver"
    Description: "Should return the current state for a valid session ID"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "session"
      - "whatsapp"
    Precondition:
      - "A valid `sessionId` is provided."
      - "Mock `sessionService.getSession` to return a session object with a `currentState` property (e.g., `{ currentState: 'AWAITING_ADDRESS' }`)."
    Steps:
      - "1. Call the `getSessionState` resolver with the session ID."
    ExpectedResult:
      - "`sessionService.getSession` is called with the correct `sessionId`."
      - "The resolver returns the value of the `currentState` property from the session object."

  - CaseID: "U-GQL-SES-102"
    Module: "sessionResolver"
    Description: "Should throw an error if the session is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "An invalid or non-existent `sessionId` is provided."
      - "Mock `sessionService.getSession` to return `null`."
    Steps:
      - "1. Call the `getSessionState` resolver."
    ExpectedResult:
      - "The resolver throws an error with the message 'Session not found'."
