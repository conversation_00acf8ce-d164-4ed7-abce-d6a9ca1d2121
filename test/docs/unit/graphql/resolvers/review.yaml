TestFile: graphql/resolvers/review.js
---
TestFunction: reviews (Query)
Cases:
  - CaseID: "U-GQL-REV-101"
    Module: "reviewResolver"
    Description: "Should fetch all reviews for a specific restaurant"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "review"
      - "filter"
    Precondition:
      - "A valid `restaurant` ID is provided."
      - "Mock `Review.find` to return an array of review documents for that restaurant."
    Steps:
      - "1. Call the `reviews` resolver with a `restaurant` ID."
    ExpectedResult:
      - "`Review.find` is called with `{ restaurant: restaurantId }`."
      - "Returns an array of transformed review objects."
---
TestFunction: addReview (Mutation)
Cases:
  - CaseID: "U-GQL-REV-201"
    Module: "reviewResolver"
    Description: "Should add a new review, update the order, and recalculate the restaurant's average rating"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "review"
      - "aggregation"
      - "side-effect"
    Precondition:
      - "User is authenticated."
      - "Valid `reviewInput` (orderId, rating, comment) is provided."
      - "Mock `Order.findById` to return a valid, unreviewed order belonging to the user."
      - "Mock the `save` method on the Review and Order models."
      - "Mock `Review.aggregate` to return a new average rating and count (e.g., `[{ avgrat: 4.5, total: 10 }]`)."
      - "Mock `Restaurant.findByIdAndUpdate` to be called with the new rating."
    Steps:
      - "1. Call `addReview` with `reviewInput`."
    ExpectedResult:
      - "A new `Review` is created and saved."
      - "The associated `Order` is updated with `isReviewed: true` and saved."
      - "`Review.aggregate` is called to calculate the new average rating for the restaurant."
      - "`Restaurant.findByIdAndUpdate` is called to update the restaurant's `rating` and `totalReviews`."
      - "Returns the transformed new review object."
  - CaseID: "U-GQL-REV-202"
    Module: "reviewResolver"
    Description: "Should throw an error if the user tries to review an order that has already been reviewed"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "User is authenticated."
      - "Mock `Order.findById` to return an order with `isReviewed: true`."
    Steps:
      - "1. Call `addReview`."
    ExpectedResult:
      - "Throws an error with the message 'Order already reviewed'."
  - CaseID: "U-GQL-REV-203"
    Module: "reviewResolver"
    Description: "Should throw an error if the user tries to review an order not belonging to them"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "authorization"
    Precondition:
      - "User is authenticated."
      - "Mock `Order.findById` to return an order where `order.user._id` does not match `req.userId`."
    Steps:
      - "1. Call `addReview`."
    ExpectedResult:
      - "Throws an error with the message 'Not Authorized'."
