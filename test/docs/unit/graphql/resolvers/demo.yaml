TestFile: graphql/resolvers/demo.js
---
TestFunction: createDemoRequest (Mutation)
Cases:
  - CaseID: "U-GQL-DEM-101"
    Module: "demoResolver"
    Description: "Should successfully create a demo request and trigger an email notification"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "demo"
    Precondition:
      - "Valid `demoInput` (name, email, phone, restaurantName) is provided."
      - "Mock the `save` method of the Demo model to resolve successfully."
      - "Mock the `sendEmail` utility function to resolve successfully."
    Steps:
      - "1. Call the `createDemoRequest` resolver with the `demoInput`."
    ExpectedResult:
      - "A new `Demo` instance is created with the provided input."
      - "The `save` method is called on the new instance."
      - "The `sendEmail` function is called with appropriate parameters (e.g., admin notification, user confirmation)."
      - "The resolver returns `{ success: true, message: 'We have received your request and will get back to you shortly.' }`."
  - CaseID: "U-GQL-DEM-102"
    Module: "demoResolver"
    Description: "Should throw an error if saving the demo request fails"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "database"
    Precondition:
      - "Mock the `save` method of the Demo model to throw an error (e.g., a validation error)."
    Steps:
      - "1. Call the `createDemoRequest` resolver."
    ExpectedResult:
      - "The resolver catches the error and throws an ApolloError with the message 'Something went wrong'."
