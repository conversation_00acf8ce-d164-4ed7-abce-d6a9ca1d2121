TestFile: graphql/resolvers/offer.js
---
TestFunction: offers (Query)
Cases:
  - CaseID: "U-GQL-OFR-101"
    Module: "offerResolver"
    Description: "Should fetch all offers for a specific restaurant"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "offer"
      - "filter"
    Precondition:
      - "A valid `restaurant` ID is provided."
      - "Mock `Offer.find` to return an array of offer documents for that restaurant."
    Steps:
      - "1. Call the `offers` resolver with a `restaurant` ID."
    ExpectedResult:
      - "`Offer.find` is called with `{ restaurant: restaurantId }`."
      - "Returns an array of transformed offer objects."
---
TestFunction: createOffer (Mutation)
Cases:
  - CaseID: "U-GQL-OFR-201"
    Module: "offerResolver"
    Description: "Should create a new offer successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "offer"
    Precondition:
      - "User is authenticated."
      - "Valid `offerInput` is provided."
      - "Mock the `save` method of the Offer model."
    Steps:
      - "1. Call `createOffer` with `offerInput`."
    ExpectedResult:
      - "A new `Offer` instance is created."
      - "The `save` method is called."
      - "Returns the transformed new offer object."
---
TestFunction: updateOffer (Mutation)
Cases:
  - CaseID: "U-GQL-OFR-301"
    Module: "offerResolver"
    Description: "Should update an existing offer successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid offer `id` and `offerInput` are provided."
      - "Mock `Offer.findByIdAndUpdate` to return the updated document."
    Steps:
      - "1. Call `updateOffer` with the ID and new data."
    ExpectedResult:
      - "`Offer.findByIdAndUpdate` is called."
      - "Returns the transformed, updated offer object."
---
TestFunction: deleteOffer (Mutation)
Cases:
  - CaseID: "U-GQL-OFR-401"
    Module: "offerResolver"
    Description: "Should delete an offer successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
    Precondition:
      - "User is authenticated."
      - "A valid offer `id` is provided."
      - "Mock `Offer.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteOffer` with the ID."
    ExpectedResult:
      - "`Offer.findByIdAndRemove` is called."
      - "Returns `true`."
