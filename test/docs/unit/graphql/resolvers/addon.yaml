TestFile: graphql/resolvers/addon.js
---
TestFunction: addons
Cases:
  - CaseID: "U-ADDON-101"
    Module: "graphql-resolver"
    Description: "成功获取所有激活的插件"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "query"]
    Precondition:
      - "数据库中有多个激活的插件 (isActive: true)"
      - "Mock Addon.find() 返回一个插件列表"
      - "Mock transformAddon() 能正确转换每个插件对象"
    Steps:
      - "调用 addons 查询"
    ExpectedResult:
      - "返回一个转换后的插件对象数组"
      - "Addon.find() 被以 { isActive: true } 为参数调用一次"
  - CaseID: "U-ADDON-102"
    Module: "graphql-resolver"
    Description: "数据库中没有激活的插件"
    Importance: "Medium"
    Status: "Planned"
    Tags: ["edge-case", "query"]
    Precondition:
      - "Mock Addon.find() 返回一个空数组"
    Steps:
      - "调用 addons 查询"
    ExpectedResult:
      - "返回一个空数组"
  - CaseID: "U-ADDON-103"
    Module: "graphql-resolver"
    Description: "数据库查询时发生错误"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "error-handling"]
    Precondition:
      - "Mock Addon.find() 抛出一个错误"
    Steps:
      - "调用 addons 查询"
    ExpectedResult:
      - "向上抛出该错误"
---
TestFunction: createAddons
Cases:
  - CaseID: "U-ADDON-201"
    Module: "graphql-resolver"
    Description: "成功为餐厅创建新的插件"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "输入参数中提供有效的 restaurant ID 和插件数据"
      - "Mock Restaurant.findById() 返回一个有效的餐厅实例"
      - "餐厅实例的 save() 方法能成功执行"
      - "Mock transformRestaurant() 能正确转换餐厅对象"
    Steps:
      - "调用 createAddons mutation"
    ExpectedResult:
      - "新的插件被添加到餐厅的 addons 数组中"
      - "restaurant.save() 被调用一次"
      - "返回转换后的餐厅对象"
  - CaseID: "U-ADDON-202"
    Module: "graphql-resolver"
    Description: "创建插件时餐厅不存在"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "error-handling"]
    Precondition:
      - "输入参数中提供一个无效的 restaurant ID"
      - "Mock Restaurant.findById() 返回 null"
    Steps:
      - "调用 createAddons mutation"
    ExpectedResult:
      - "抛出一个错误 (e.g., TypeError: Cannot read properties of null)"
---
TestFunction: editAddon
Cases:
  - CaseID: "U-ADDON-301"
    Module: "graphql-resolver"
    Description: "成功编辑一个已存在的插件"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "输入参数中提供有效的 restaurant ID 和要更新的插件数据 (包括 _id)"
      - "Mock Restaurant.findById() 返回一个包含目标插件的餐厅实例"
      - "餐厅实例的 save() 方法能成功执行"
      - "Mock transformRestaurant() 能正确转换餐厅对象"
    Steps:
      - "调用 editAddon mutation"
    ExpectedResult:
      - "目标插件的数据被成功更新"
      - "restaurant.save() 被调用一次"
      - "返回转换后的餐厅对象"
  - CaseID: "U-ADDON-302"
    Module: "graphql-resolver"
    Description: "编辑插件时餐厅不存在"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "error-handling"]
    Precondition:
      - "输入参数中提供一个无效的 restaurant ID"
      - "Mock Restaurant.findById() 返回 null"
    Steps:
      - "调用 editAddon mutation"
    ExpectedResult:
      - "抛出一个错误"
---
TestFunction: deleteAddon
Cases:
  - CaseID: "U-ADDON-401"
    Module: "graphql-resolver"
    Description: "成功删除一个插件"
    Importance: "High"
    Status: "Planned"
    Tags: ["positive", "mutation"]
    Precondition:
      - "输入参数中提供有效的 restaurant ID 和 addon ID"
      - "Mock Restaurant.findById() 返回一个包含目标插件的餐厅实例"
      - "餐厅实例的 save() 方法能成功执行"
      - "Mock transformRestaurant() 能正确转换餐厅对象"
    Steps:
      - "调用 deleteAddon mutation"
    ExpectedResult:
      - "目标插件从餐厅的 addons 数组中被移除"
      - "其他关联了该插件的 variation.addons 数组中，该插件 ID 也被移除"
      - "restaurant.save() 被调用一次"
      - "返回转换后的餐厅对象"
  - CaseID: "U-ADDON-402"
    Module: "graphql-resolver"
    Description: "删除插件时餐厅不存在"
    Importance: "High"
    Status: "Planned"
    Tags: ["negative", "error-handling"]
    Precondition:
      - "输入参数中提供一个无效的 restaurant ID"
      - "Mock Restaurant.findById() 返回 null"
    Steps:
      - "调用 deleteAddon mutation"
    ExpectedResult:
      - "抛出一个错误"
