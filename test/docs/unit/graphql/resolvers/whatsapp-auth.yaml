TestFile: graphql/resolvers/whatsapp-auth.js
---
TestFunction: getWhatsappAuthToken (Query)
Cases:
  - CaseID: "U-GQL-WAP-101"
    Module: "whatsappAuthResolver"
    Description: "Should generate a JWT with WHATSAPP role for a given user ID"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "whatsapp"
      - "authentication"
      - "jwt"
    Precondition:
      - "A valid `userId` is provided as an argument."
      - "Mock `jwt.sign` to be called."
    Steps:
      - "1. Call the `getWhatsappAuthToken` resolver with a `userId`."
    ExpectedResult:
      - "`jwt.sign` is called with a payload containing `{ userId: providedUserId, role: 'WHATSAPP' }`."
      - "The resolver returns the signed JWT string."

  - CaseID: "U-GQL-WAP-102"
    Module: "whatsappAuthResolver"
    Description: "Should handle cases where no userId is provided"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "The `userId` argument is null or undefined."
    Steps:
      - "1. Call `getWhatsappAuthToken` without a `userId`."
    ExpectedResult:
      - "The resolver should ideally throw a validation error, or `jwt.sign` might throw an error due to an undefined payload. The exact behavior depends on implementation, but it should fail gracefully."
