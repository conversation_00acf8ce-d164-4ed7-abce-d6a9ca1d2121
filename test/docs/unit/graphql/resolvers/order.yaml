TestFile: graphql/resolvers/order.js
---
TestFunction: orders (Query)
Cases:
  - CaseID: "U-GQL-ORD-101"
    Module: "orderResolver"
    Description: "Should fetch orders for an authenticated customer"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "order"
      - "authorization"
      - "customer"
    Precondition:
      - "User is authenticated with 'CUSTOMER' role."
      - "Mock `Order.find` to be called with `{ user: req.userId }`."
    Steps:
      - "1. Call the `orders` resolver as a customer."
    ExpectedResult:
      - "Returns a paginated list of orders belonging only to that customer."
  - CaseID: "U-GQL-ORD-102"
    Module: "orderResolver"
    Description: "Should fetch orders for an authenticated restaurant owner"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "order"
      - "authorization"
      - "restaurant"
    Precondition:
      - "User is authenticated with 'RESTAURANT' role."
      - "Mock `Order.find` to be called with `{ restaurant: req.userId }`."
    Steps:
      - "1. Call the `orders` resolver as a restaurant owner."
    ExpectedResult:
      - "Returns a paginated list of orders belonging to that restaurant."
---
TestFunction: createOrder (Mutation)
Cases:
  - CaseID: "U-GQL-ORD-201"
    Module: "orderResolver"
    Description: "Should successfully create an order with correct amount calculations and trigger side effects"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "order"
      - "payment"
      - "notification"
      - "pubsub"
    Precondition:
      - "User is authenticated."
      - "Valid `orderInput` is provided, including items, restaurant, address, and a valid coupon code."
      - "Mock `Restaurant.findOne` to return an available restaurant with delivery charge info."
      - "Mock `Coupon.findOne` to return a valid, active coupon."
      - "Mock `Taxation.findOne` to return tax information."
      - "Mock `User.findById` to return the user document."
      - "Mock `createStripePayment` helper to return a payment intent string."
      - "Mock `sendNotification` helper."
      - "Mock `pubsub.publish`."
    Steps:
      - "1. Call `createOrder` with `orderInput`."
    ExpectedResult:
      - "All amounts (order, delivery, tax, discount, total) are calculated correctly."
      - "A new `Order` is created and saved."
      - "`createStripePayment` is called with the correct total amount."
      - "`sendNotification` is called for the restaurant."
      - "`pubsub.publish` is called with the 'NEW_ORDER' topic."
      - "Returns a transformed order object including the `paymentIntent`."
  - CaseID: "U-GQL-ORD-202"
    Module: "orderResolver"
    Description: "Should throw an error if the specified coupon is invalid or expired"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
      - "coupon"
    Precondition:
      - "User is authenticated."
      - "`orderInput` contains a coupon code."
      - "Mock `Coupon.findOne` to return `null` or an inactive coupon."
    Steps:
      - "1. Call `createOrder`."
    ExpectedResult:
      - "Throws an error with the message 'Invalid Coupon'."
---
TestFunction: updateOrder (Mutation)
Cases:
  - CaseID: "U-GQL-ORD-301"
    Module: "orderResolver"
    Description: "Should allow a restaurant to update an order status and notify the customer"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "status-update"
    Precondition:
      - "User is authenticated as a restaurant owner."
      - "Mock `Order.findById` to return an order belonging to this restaurant."
      - "Mock `order.save()`."
      - "Mock `sendNotification` and `pubsub.publish`."
    Steps:
      - "1. Call `updateOrder` with a new status (e.g., 'ACCEPTED')."
    ExpectedResult:
      - "The order's status is updated."
      - "`sendNotification` is called to inform the customer."
      - "`pubsub.publish` is called with 'ORDER_STATUS_CHANGED' topic."
      - "Returns the transformed, updated order."
---
TestFunction: orderStatusChanged (Subscription)
Cases:
  - CaseID: "U-GQL-ORD-401"
    Module: "orderResolver"
    Description: "Should subscribe to and receive status changes for a specific order"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "subscription"
      - "real-time"
    Precondition:
      - "A valid `orderId` is provided."
      - "Mock `pubsub.asyncIterator` to filter for the 'ORDER_STATUS_CHANGED' topic."
    Steps:
      - "1. Initiate the `orderStatusChanged` subscription with the `orderId`."
      - "2. (In test setup) Simulate a call to `pubsub.publish` for that `orderId`."
    ExpectedResult:
      - "The subscription's `withFilter` function correctly returns `true`."
      - "The client receives the payload published to the topic."
