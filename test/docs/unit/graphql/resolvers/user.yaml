TestFile: graphql/resolvers/user.js
---
TestFunction: users (Query)
Cases:
  - CaseID: "U-GQL-USR-101"
    Module: "userResolver"
    Description: "Should allow an admin to fetch a paginated list of all users"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "user"
      - "admin"
      - "authorization"
    Precondition:
      - "User is authenticated with 'ADMIN' role."
      - "Mock `User.find` to be called with the search filter."
      - "Mock `User.countDocuments`."
    Steps:
      - "1. Call the `users` resolver as an admin."
    ExpectedResult:
      - "Returns a paginated list of all users."
  - CaseID: "U-GQL-USR-102"
    Module: "userResolver"
    Description: "Should prevent a non-admin user from fetching the user list"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "authorization"
    Precondition:
      - "User is authenticated with a role other than 'ADMIN' (e.g., 'CUSTOMER')."
    Steps:
      - "1. Call the `users` resolver as a non-admin."
    ExpectedResult:
      - "Throws an error with the message 'Not Authorized'."
---
TestFunction: updateUser (Mutation)
Cases:
  - CaseID: "U-GQL-USR-201"
    Module: "userResolver"
    Description: "Should allow a user to update their own profile information"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "user"
      - "profile"
    Precondition:
      - "User is authenticated."
      - "Valid `userInput` is provided (e.g., name, phone)."
      - "Mock `User.findById` to return the current user document."
      - "Mock the `save` method on the user document."
    Steps:
      - "1. Call `updateUser` with the user's own ID and new data."
    ExpectedResult:
      - "The user's profile fields are updated."
      - "The `save` method is called."
      - "Returns the transformed, updated user object."
---
TestFunction: changePassword (Mutation)
Cases:
  - CaseID: "U-GQL-USR-301"
    Module: "userResolver"
    Description: "Should allow a user to change their password with the correct old password"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "security"
      - "password"
    Precondition:
      - "User is authenticated."
      - "`password` and `newPassword` are provided."
      - "Mock `User.findById` to return the user document with a hashed password."
      - "Mock `bcrypt.compare` to return `true`."
      - "Mock `bcrypt.hash` to return the new hashed password."
    Steps:
      - "1. Call `changePassword`."
    ExpectedResult:
      - "`bcrypt.compare` confirms the old password is correct."
      - "The user's `password` field is updated with the new hashed password."
      - "Returns `true`."
  - CaseID: "U-GQL-USR-302"
    Module: "userResolver"
    Description: "Should throw an error if the old password is incorrect"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "security"
    Precondition:
      - "User is authenticated."
      - "Mock `User.findById` to return the user document."
      - "Mock `bcrypt.compare` to return `false`."
    Steps:
      - "1. Call `changePassword` with an incorrect old password."
    ExpectedResult:
      - "Throws an error with the message 'Incorrect Password'."
