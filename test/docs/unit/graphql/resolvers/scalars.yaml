TestFile: graphql/resolvers/scalars.js
---
TestFunction: Date (GraphQLScalarType)
Cases:
  - CaseID: "U-GQL-SCL-101"
    Module: "dateScalar"
    Description: "Should serialize a Date object into an integer timestamp"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "scalar"
      - "date"
      - "serialize"
    Precondition:
      - "A JavaScript `Date` object is provided to the `serialize` function."
    Steps:
      - "1. Call the `serialize` method of the `Date` scalar with a `Date` object."
    ExpectedResult:
      - "The function returns the integer timestamp representation of the date (e.g., `date.getTime()`)."

  - CaseID: "U-GQL-SCL-102"
    Module: "dateScalar"
    Description: "Should parse an integer timestamp value into a Date object"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "scalar"
      - "date"
      - "parseValue"
    Precondition:
      - "An integer representing a valid timestamp is provided to the `parseValue` function."
    Steps:
      - "1. Call the `parseValue` method of the `Date` scalar with the integer timestamp."
    ExpectedResult:
      - "The function returns a new `Date` object corresponding to that timestamp."

  - CaseID: "U-GQL-SCL-103"
    Module: "dateScalar"
    Description: "Should parse an integer literal from the AST into a Date object"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "scalar"
      - "date"
      - "parseLiteral"
    Precondition:
      - "An AST (Abstract Syntax Tree) node with `kind: Kind.INT` and a string value of an integer is provided to the `parseLiteral` function."
    Steps:
      - "1. Call the `parseLiteral` method with the AST node."
    ExpectedResult:
      - "The function returns a new `Date` object corresponding to the parsed integer value."

  - CaseID: "U-GQL-SCL-104"
    Module: "dateScalar"
    Description: "Should return null when trying to parse a non-integer literal from the AST"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "scalar"
      - "validation"
    Precondition:
      - "An AST node with a kind other than `Kind.INT` (e.g., `Kind.STRING`) is provided."
    Steps:
      - "1. Call the `parseLiteral` method with the non-integer AST node."
    ExpectedResult:
      - "The function returns `null`."
