TestFile: graphql/resolvers/merge.js
---
TestFunction: transformOrder
Cases:
  - CaseID: "U-GQL-MRG-101"
    Module: "mergeHelpers"
    Description: "Should correctly transform a Mongoose order document into a GraphQL Order object"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "transform"
      - "order"
      - "relationship"
    Precondition:
      - "A mock Mongoose `order` document is provided."
      - "The document contains IDs for `user`, `restaurant`, `rider`, `coupon`, and an `items` array with `food` IDs."
      - "Mock the helper functions (`user`, `restaurant`, `rider`, `coupon`, `foods`, `address`) to return predictable, transformed objects when called with the respective IDs."
    Steps:
      - "1. Call `transformOrder` with the mock order document."
    ExpectedResult:
      - "The function returns an object with all basic fields correctly mapped (e.g., `_id` to `id`)."
      - "The `user`, `restaurant`, `rider`, `coupon`, and `deliveryAddress` fields are populated with the results from their respective mocked helper functions."
      - "The `items` array in the result contains objects where the `food` field is populated by the result of the mocked `foods` helper."
---
TestFunction: transformFood
Cases:
  - CaseID: "U-GQL-MRG-201"
    Module: "mergeHelpers"
    Description: "Should correctly transform a Mongoose food document into a GraphQL Food object"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "transform"
      - "food"
    Precondition:
      - "A mock Mongoose `food` document is provided, containing `restaurant` and `category` IDs."
      - "Mock the `restaurant` and `category` helper functions."
    Steps:
      - "1. Call `transformFood` with the mock food document."
    ExpectedResult:
      - "Returns an object with basic fields mapped."
      - "The `restaurant` and `category` fields are populated with results from their mocked helper functions."
---
TestFunction: transformUser
Cases:
  - CaseID: "U-GQL-MRG-301"
    Module: "mergeHelpers"
    Description: "Should correctly transform a Mongoose user document into a GraphQL User object"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "transform"
      - "user"
    Precondition:
      - "A mock Mongoose `user` document is provided, containing arrays of `addresses` and `orders` IDs."
      - "Mock the `addresses` and `orders` helper functions."
    Steps:
      - "1. Call `transformUser` with the mock user document."
    ExpectedResult:
      - "Returns an object with basic fields mapped."
      - "The `addresses` and `orders` fields are populated with arrays of transformed objects from their mocked helper functions."
---
TestFunction: foods (data loader helper)
Cases:
  - CaseID: "U-GQL-MRG-401"
    Module: "mergeHelpers"
    Description: "Should fetch multiple food documents based on an array of IDs"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "data-loader"
    Precondition:
      - "An array of `foodIds` is provided."
      - "Mock `Food.find` to be called with a query like `{ _id: { $in: foodIds } }`."
      - "The mock should return an array of food documents."
    Steps:
      - "1. Call the `foods` helper function with the array of IDs."
    ExpectedResult:
      - "`Food.find` is called once with the correct `$in` query."
      - "The function returns an array of transformed food objects."
