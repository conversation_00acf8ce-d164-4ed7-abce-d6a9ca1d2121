TestFile: graphql/resolvers/category.js
---
TestFunction: categories (Query)
Cases:
  - CaseID: "U-GQL-CAT-101"
    Module: "categoryResolver"
    Description: "Should fetch all categories for a specific restaurant"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "category"
      - "filter"
    Precondition:
      - "A valid `restaurant` ID is provided."
      - "Mock `Category.find` to return an array of category documents for that restaurant."
    Steps:
      - "1. Call the `categories` resolver with a `restaurant` ID argument."
    ExpectedResult:
      - "`Category.find` is called with `{ restaurant: restaurantId }`."
      - "Returns an array of transformed category objects."
---
TestFunction: createCategory (Mutation)
Cases:
  - CaseID: "U-GQL-CAT-201"
    Module: "categoryResolver"
    Description: "Should create a new category successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "category"
    Precondition:
      - "User is authenticated."
      - "Valid `categoryInput` is provided (including name and restaurant ID)."
      - "Mock the `save` method of the Category model."
    Steps:
      - "1. Call `createCategory` with `categoryInput`."
    ExpectedResult:
      - "A new `Category` instance is created with the input data."
      - "The `save` method is called."
      - "Returns a transformed category object of the newly created category."
---
TestFunction: updateCategory (Mutation)
Cases:
  - CaseID: "U-GQL-CAT-301"
    Module: "categoryResolver"
    Description: "Should update an existing category successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "category"
    Precondition:
      - "User is authenticated."
      - "A valid category `id` and `categoryInput` are provided."
      - "Mock `Category.findByIdAndUpdate` to return the updated category document."
    Steps:
      - "1. Call `updateCategory` with the category ID and new data."
    ExpectedResult:
      - "`Category.findByIdAndUpdate` is called with the ID, input data, and `{ new: true }`."
      - "Returns the transformed, updated category object."
  - CaseID: "U-GQL-CAT-302"
    Module: "categoryResolver"
    Description: "Should throw an error if the category to update is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Category.findByIdAndUpdate` to return null."
    Steps:
      - "1. Call `updateCategory` with a non-existent category ID."
    ExpectedResult:
      - "Throws an error with the message 'Category not found.'."
---
TestFunction: deleteCategory (Mutation)
Cases:
  - CaseID: "U-GQL-CAT-401"
    Module: "categoryResolver"
    Description: "Should delete a category successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "category"
    Precondition:
      - "User is authenticated."
      - "A valid category `id` is provided."
      - "Mock `Category.findById` to return a category document."
      - "Mock `Category.findByIdAndRemove` to resolve successfully."
    Steps:
      - "1. Call `deleteCategory` with the category ID."
    ExpectedResult:
      - "`Category.findByIdAndRemove` is called with the ID."
      - "Returns the transformed object of the deleted category."
  - CaseID: "U-GQL-CAT-402"
    Module: "categoryResolver"
    Description: "Should throw an error if the category to delete is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Category.findById` to return null."
    Steps:
      - "1. Call `deleteCategory` with a non-existent category ID."
    ExpectedResult:
      - "Throws an error with the message 'Category not found.'."
