TestFile: graphql/resolvers/chat.js
---
TestFunction: chat (Query)
Cases:
  - CaseID: "U-GQL-CHT-101"
    Module: "chatResolver"
    Description: "Should fetch chat messages for a specific order if user is authorized"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "chat"
      - "authorization"
    Precondition:
      - "User is authenticated and is either the customer or the rider for the specified order."
      - "A valid `orderId` is provided."
      - "Mock `Order.findById` to return an order document that includes the authenticated user's ID as either `user` or `rider`."
      - "Mock `ChatMessage.find` to return an array of chat message documents."
    Steps:
      - "1. Call the `chat` resolver with the `orderId`."
    ExpectedResult:
      - "`Order.findById` is called to verify user's access right."
      - "`ChatMessage.find` is called with `{ order: orderId }`."
      - "Returns an array of transformed chat message objects."
  - CaseID: "U-GQL-CHT-102"
    Module: "chatResolver"
    Description: "Should throw an error if user is not authorized to view the chat"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "authorization"
    Precondition:
      - "User is authenticated but is not part of the specified order."
      - "Mock `Order.findById` to return an order document where the user is neither the customer nor the rider."
    Steps:
      - "1. Call the `chat` resolver."
    ExpectedResult:
      - "Throws an error with the message 'Not authorized to view this chat.'."
---
TestFunction: createChatMessage (Mutation)
Cases:
  - CaseID: "U-GQL-CHT-201"
    Module: "chatResolver"
    Description: "Should create a chat message and publish it via PubSub"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "chat"
      - "pubsub"
    Precondition:
      - "User is authenticated and authorized for the order chat."
      - "Valid `orderId` and `message` text are provided."
      - "Mock `Order.findById` to confirm authorization."
      - "Mock the `save` method of the ChatMessage model."
      - "Mock `pubsub.publish`."
    Steps:
      - "1. Call `createChatMessage` with `orderId` and `message`."
    ExpectedResult:
      - "A new `ChatMessage` is created with the correct sender, order, and message text."
      - "The `save` method is called."
      - "`pubsub.publish` is called with the correct topic (`NEW_CHAT_MESSAGE`) and payload."
      - "Returns the transformed new chat message object."
---
TestFunction: newChatMessage (Subscription)
Cases:
  - CaseID: "U-GQL-CHT-301"
    Module: "chatResolver"
    Description: "Should subscribe a user to new chat messages for an authorized order"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "subscription"
      - "chat"
      - "pubsub"
    Precondition:
      - "The subscription resolver is set up with `withFilter`."
      - "A payload (a new chat message) is published on the `NEW_CHAT_MESSAGE` topic."
      - "The `withFilter`'s filter function is called with the payload and the subscription variables (`orderId`)."
      - "The authenticated user (from context) is authorized for the order in the payload."
    Steps:
      - "1. Initiate a GraphQL subscription to `newChatMessage` with a specific `orderId`."
      - "2. Simulate a `pubsub.publish` event for that `orderId`."
    ExpectedResult:
      - "The `withFilter` function returns `true`."
      - "The client receives the new chat message payload."
  - CaseID: "U-GQL-CHT-302"
    Module: "chatResolver"
    Description: "Should not send a message if the user is not subscribed to that order's chat"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "subscription"
      - "filter"
    Precondition:
      - "A payload is published for an `orderId` that the subscribing user is not authorized for, or is different from their subscription variable."
    Steps:
      - "1. Initiate a subscription for `orderId: 'A'`."
      - "2. Simulate a `pubsub.publish` event for `orderId: 'B'`."
    ExpectedResult:
      - "The `withFilter` function returns `false`."
      - "The client does not receive the chat message payload."
