TestFile: graphql/resolvers/coupon.js
---
TestFunction: verifyCoupon (Query)
Cases:
  - CaseID: "U-GQL-CPN-101"
    Module: "couponResolver"
    Description: "Should successfully verify a valid and applicable coupon"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "coupon"
      - "verification"
    Precondition:
      - "User is authenticated."
      - "A valid `code` and `restaurant` ID are provided."
      - "Mock `Coupon.findOne` to return a valid, active, non-expired coupon applicable to the restaurant."
      - "The coupon's usage limits have not been reached."
      - "Mock `Order.countDocuments` to return a count less than the coupon's `maxUsesPerUser`."
    Steps:
      - "1. Call the `verifyCoupon` resolver with the code and restaurant ID."
    ExpectedResult:
      - "Returns the transformed coupon object, indicating success."
  - CaseID: "U-GQL-CPN-102"
    Module: "couponResolver"
    Description: "Should throw an error for an invalid coupon code"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "Mock `Coupon.findOne` to return null."
    Steps:
      - "1. Call `verifyCoupon` with a non-existent code."
    ExpectedResult:
      - "Throws an error with the message 'Invalid Coupon'."
  - CaseID: "U-GQL-CPN-103"
    Module: "couponResolver"
    Description: "Should throw an error for an expired coupon"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "expired"
    Precondition:
      - "Mock `Coupon.findOne` to return a coupon whose `expiry` date is in the past."
    Steps:
      - "1. Call `verifyCoupon`."
    ExpectedResult:
      - "Throws an error with the message 'Coupon is expired'."
  - CaseID: "U-GQL-CPN-104"
    Module: "couponResolver"
    Description: "Should throw an error if the user has already used the coupon maximum times"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "usage-limit"
    Precondition:
      - "Mock `Coupon.findOne` to return a valid coupon."
      - "Mock `Order.countDocuments` to return a count equal to or greater than `maxUsesPerUser`."
    Steps:
      - "1. Call `verifyCoupon`."
    ExpectedResult:
      - "Throws an error with the message 'You have already used this coupon'."
---
TestFunction: createCoupon (Mutation)
Cases:
  - CaseID: "U-GQL-CPN-201"
    Module: "couponResolver"
    Description: "Should create a new coupon successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "coupon"
    Precondition:
      - "User is authenticated."
      - "Valid `couponInput` is provided."
      - "Mock the `save` method of the Coupon model."
    Steps:
      - "1. Call `createCoupon` with `couponInput`."
    ExpectedResult:
      - "A new `Coupon` instance is created."
      - "The `save` method is called."
      - "Returns the transformed new coupon object."
