TestFile: graphql/resolvers/vendor.js
---
TestFunction: vendors (Query)
Cases:
  - CaseID: "U-GQL-VDR-101"
    Module: "vendorResolver"
    Description: "Should fetch a list of all vendors (owners)"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "vendor"
      - "owner"
    Precondition:
      - "Mock `Owner.find` to return an array of owner documents."
      - "Mock the `transformOwner` helper function to return a predictable transformed object."
    Steps:
      - "1. Call the `vendors` resolver."
    ExpectedResult:
      - "`Owner.find` is called with no arguments."
      - "The `transformOwner` function is called for each document returned by `find`."
      - "Returns an array of transformed owner objects."

  - CaseID: "U-GQL-VDR-102"
    Module: "vendorResolver"
    Description: "Should return an empty array if no vendors exist"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
      - "zero-state"
    Precondition:
      - "Mock `Owner.find` to return an empty array `[]`."
    Steps:
      - "1. Call the `vendors` resolver."
    ExpectedResult:
      - "Returns an empty array `[]`."
      - "The `transformOwner` function is not called."
