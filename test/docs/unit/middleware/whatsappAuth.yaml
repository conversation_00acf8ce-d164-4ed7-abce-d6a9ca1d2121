TestFile: whatsapp/middleware/whatsappAuth.js
---
TestFunction: validateWhatsappSignature (middleware)
Cases:
  - CaseID: "U-MW-WA-101"
    Module: "whatsappAuth"
    Description: "Should call next() when the signature is valid"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "security"
      - "webhook"
    Precondition:
      - "`process.env.WHATSAPP_APP_SECRET` is set to a secret key (e.g., 'my-secret')."
      - "The request body is a valid JSON object."
      - "The 'X-Hub-Signature-256' header is present and contains the correct SHA256 HMAC signature of the request body, prefixed with 'sha256='."
      - "Mock Express request, response, and next function objects."
    Steps:
      - "1. Calculate the expected signature based on the mock request body and secret."
      - "2. Set the 'X-Hub-Signature-256' header on the mock request to the calculated signature."
      - "3. Call `validateWhatsappSignature(req, res, next)`."
    ExpectedResult:
      - "The `next()` function is called exactly once."
      - "No response is sent from this middleware."
  - CaseID: "U-MW-WA-102"
    Module: "whatsappAuth"
    Description: "Should return 401 if the signature is invalid"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "security"
    Precondition:
      - "`process.env.WHATSAPP_APP_SECRET` is set."
      - "The 'X-Hub-Signature-256' header contains an incorrect signature."
    Steps:
      - "1. Call `validateWhatsappSignature(req, res, next)`."
    ExpectedResult:
      - "`res.status(401)` is called."
      - "`res.send()` is called with an error message like 'Invalid signature'."
      - "The `next()` function is not called."
  - CaseID: "U-MW-WA-103"
    Module: "whatsappAuth"
    Description: "Should return 401 if the X-Hub-Signature-256 header is missing"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "security"
    Precondition:
      - "The 'X-Hub-Signature-256' header is not present in the request."
    Steps:
      - "1. Call `validateWhatsappSignature(req, res, next)`."
    ExpectedResult:
      - "`res.status(401)` is called."
      - "`res.send()` is called with an error message like 'Missing X-Hub-Signature-256 header'."
      - "The `next()` function is not called."
  - CaseID: "U-MW-WA-104"
    Module: "whatsappAuth"
    Description: "Should return 500 if WHATSAPP_APP_SECRET is not configured"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "configuration"
      - "error-handling"
    Precondition:
      - "`process.env.WHATSAPP_APP_SECRET` is not set (is null or undefined)."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `validateWhatsappSignature(req, res, next)`."
    ExpectedResult:
      - "`logger.error` is called with a message about the missing secret."
      - "`res.status(500)` is called."
      - "`res.send()` is called with a server configuration error message."
      - "The `next()` function is not called."
  - CaseID: "U-MW-WA-105"
    Module: "whatsappAuth"
    Description: "Should handle an empty request body"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "edge-case"
      - "security"
    Precondition:
      - "The request body is empty (e.g., {} or an empty string)."
      - "Calculate the expected signature for an empty body."
      - "Set the 'X-Hub-Signature-256' header to the correct signature for the empty body."
    Steps:
      - "1. Call `validateWhatsappSignature(req, res, next)`."
    ExpectedResult:
      - "The `next()` function is called."
