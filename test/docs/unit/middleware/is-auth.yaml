TestFile: middleware/is-auth.js
---
TestFunction: isAuth (middleware)
Cases:
  - CaseID: "U-MW-IA-101"
    Module: "isAuth"
    Description: "Should call next() and attach userId to request when token is valid"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "auth"
      - "jwt"
    Precondition:
      - "A valid JWT is provided in the 'Authorization' header, prefixed with 'Bearer '."
      - "Mock `jwt.verify` to successfully decode the token and return a payload containing a `userId`."
      - "Mock Express request, response, and next function objects."
    Steps:
      - "1. Set the 'Authorization' header on the mock request object."
      - "2. Call `isAuth(req, res, next)`."
    ExpectedResult:
      - "`jwt.verify` is called with the token part of the header and the correct JWT secret."
      - "`req.userId` is set to the `userId` from the decoded token."
      - "The `next()` function is called exactly once."
      - "No error is thrown."
  - CaseID: "U-MW-IA-102"
    Module: "isAuth"
    Description: "Should throw an error if Authorization header is missing"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "jwt"
    Precondition:
      - "The 'Authorization' header is not present on the request object."
    Steps:
      - "1. Call `isAuth(req, res, next)`."
    ExpectedResult:
      - "An error with the message 'Not authenticated.' is thrown."
      - "The `next()` function is not called."
  - CaseID: "U-MW-IA-103"
    Module: "isAuth"
    Description: "Should throw an error if Authorization header is malformed (no 'Bearer ' prefix)"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "jwt"
    Precondition:
      - "The 'Authorization' header is present but does not start with 'Bearer '."
    Steps:
      - "1. Call `isAuth(req, res, next)`."
    ExpectedResult:
      - "An error with the message 'Not authenticated.' is thrown."
      - "The `next()` function is not called."
  - CaseID: "U-MW-IA-104"
    Module: "isAuth"
    Description: "Should throw an error if token is invalid or expired"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "jwt"
    Precondition:
      - "A token is provided in the 'Authorization' header."
      - "Mock `jwt.verify` to throw an error (e.g., `JsonWebTokenError` or `TokenExpiredError`)."
    Steps:
      - "1. Call `isAuth(req, res, next)`."
    ExpectedResult:
      - "The error thrown by `jwt.verify` is caught and re-thrown."
      - "The `next()` function is not called."
