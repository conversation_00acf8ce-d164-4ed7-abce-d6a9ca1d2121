TestFile: middleware/whatsapp-graphql-auth.js
---
TestFunction: withWhatsAppAuth (HOC)
Cases:
  - CaseID: "U-MW-WGA-101"
    Module: "withWhatsAppAuth"
    Description: "Should call the original resolver with an enriched context when session is valid"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "auth"
      - "session"
      - "graphql"
      - "wrapper"
    Precondition:
      - "A mock resolver function is defined."
      - "The GraphQL context (`req`) contains a valid 'X-WhatsApp-Session-ID' in its headers."
      - "Mock `sessionService.getSession` to return a valid session object (including a `waId`)."
    Steps:
      - "1. Wrap the mock resolver with `withWhatsAppAuth`."
      - "2. Call the wrapped resolver with standard GraphQL arguments (parent, args, context, info)."
    ExpectedResult:
      - "`sessionService.getSession` is called with the session ID from the headers."
      - "The original mock resolver is called."
      - "The `context` object passed to the original resolver is enriched with `session` and `waId` properties."
  - CaseID: "U-MW-WGA-102"
    Module: "withWhatsAppAuth"
    Description: "Should throw an error if X-WhatsApp-Session-ID header is missing"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "session"
    Precondition:
      - "The GraphQL context (`req`) headers do not contain 'X-WhatsApp-Session-ID'."
      - "A mock resolver is wrapped with `withWhatsAppAuth`."
    Steps:
      - "1. Call the wrapped resolver."
    ExpectedResult:
      - "An error with the message 'Unauthorized: Missing or invalid session ID' is thrown."
      - "The original mock resolver is not called."
  - CaseID: "U-MW-WGA-103"
    Module: "withWhatsAppAuth"
    Description: "Should throw an error if session is not found"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
      - "session"
    Precondition:
      - "The context headers contain a session ID."
      - "Mock `sessionService.getSession` to return null or undefined."
    Steps:
      - "1. Call the wrapped resolver."
    ExpectedResult:
      - "An error with the message 'Unauthorized: Session not found' is thrown."
      - "The original mock resolver is not called."
  - CaseID: "U-MW-WGA-104"
    Module: "withWhatsAppAuth"
    Description: "Should throw an error if sessionService fails"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "The context headers contain a session ID."
      - "Mock `sessionService.getSession` to throw an error."
      - "Mock `logger.error`."
    Steps:
      - "1. Call the wrapped resolver."
    ExpectedResult:
      - "The error from `sessionService.getSession` is caught."
      - "`logger.error` is called with the error details."
      - "The original error is re-thrown."
      - "The original mock resolver is not called."
