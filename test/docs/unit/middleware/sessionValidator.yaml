TestFile: whatsapp/middleware/sessionValidator.js
---
TestFunction: sessionValidator (middleware)
Cases:
  - CaseID: "U-MW-SV-101"
    Module: "sessionValidator"
    Description: "Should call next() when a valid session ID is provided and session is found"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session"
      - "auth"
    Precondition:
      - "Request header 'X-WhatsApp-Session-ID' is set to a valid session ID (e.g., 'valid-session-id')."
      - "Mock `sessionService.getSession` is configured to return a valid session object for 'valid-session-id'."
      - "Mock Express request, response, and next function objects."
    Steps:
      - "1. Call `sessionValidator(req, res, next)`."
    ExpectedResult:
      - "`sessionService.getSession` is called with 'valid-session-id'."
      - "The returned session object is attached to `req.session`."
      - "The `next()` function is called exactly once."
      - "No response is sent from this middleware."
  - CaseID: "U-MW-SV-102"
    Module: "sessionValidator"
    Description: "Should return 401 if X-WhatsApp-Session-ID header is missing"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "session"
      - "auth"
    Precondition:
      - "Request header 'X-WhatsApp-Session-ID' is not set."
      - "Mock Express request, response, and next function objects."
    Steps:
      - "1. Call `sessionValidator(req, res, next)`."
    ExpectedResult:
      - "`res.status(401)` is called."
      - "`res.send()` is called with an appropriate error message (e.g., 'Missing or invalid session ID')."
      - "The `next()` function is not called."
  - CaseID: "U-MW-SV-103"
    Module: "sessionValidator"
    Description: "Should return 401 if session is not found for a given ID"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "session"
      - "auth"
    Precondition:
      - "Request header 'X-WhatsApp-Session-ID' is set (e.g., 'non-existent-id')."
      - "Mock `sessionService.getSession` is configured to return null or undefined for 'non-existent-id'."
    Steps:
      - "1. Call `sessionValidator(req, res, next)`."
    ExpectedResult:
      - "`res.status(401)` is called."
      - "`res.send()` is called with an error message indicating an invalid session."
      - "The `next()` function is not called."
  - CaseID: "U-MW-SV-104"
    Module: "sessionValidator"
    Description: "Should return 500 if sessionService throws an error"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Request header 'X-WhatsApp-Session-ID' is set."
      - "Mock `sessionService.getSession` is configured to throw an error."
      - "Mock `logger.error`."
    Steps:
      - "1. Call `sessionValidator(req, res, next)`."
    ExpectedResult:
      - "`logger.error` is called with the thrown error."
      - "`res.status(500)` is called."
      - "`res.send()` is called with a generic server error message."
      - "The `next()` function is not called."
