TestFile: whatsapp/utils/sessionIdGenerator.js
---
TestFunction: generate
Cases:
  - CaseID: "U-UTL-SIG-101"
    Module: "sessionIdGenerator"
    Description: "Should generate a non-empty string ID"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "utility"
      - "uuid"
    Precondition:
      - "The `crypto` module is available in the Node.js environment."
      - "Mock `crypto.randomUUID` to return a predictable UUID string for verification."
    Steps:
      - "1. Call `generate()`."
    ExpectedResult:
      - "`crypto.randomUUID` is called exactly once."
      - "The function returns the mocked UUID string."
      - "The returned value is of type 'string' and is not empty."
  - CaseID: "U-UTL-SIG-102"
    Module: "sessionIdGenerator"
    Description: "Should generate unique IDs on subsequent calls"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "utility"
      - "uniqueness"
    Precondition:
      - "No mocking is needed for `crypto.randomUUID` to test real behavior."
    Steps:
      - "1. Call `generate()` to get the first ID."
      - "2. Call `generate()` again to get the second ID."
    ExpectedResult:
      - "The first ID and the second ID are not equal."
