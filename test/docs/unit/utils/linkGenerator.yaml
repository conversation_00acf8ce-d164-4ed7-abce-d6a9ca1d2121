TestFile: whatsapp/utils/linkGenerator.js
---
TestFunction: generateLink
Cases:
  - CaseID: "U-UTL-LG-101"
    Module: "linkGenerator"
    Description: "Should generate a correct URL with the given session ID"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "utility"
      - "url"
    Precondition:
      - "The `config.BASE_URL` is set to a base URL (e.g., 'https://example.com')."
      - "A `sessionId` string is provided (e.g., 'test-session-123')."
    Steps:
      - "1. Call `generateLink(sessionId)`."
    ExpectedResult:
      - "The function returns the string 'https://example.com?sessionId=test-session-123'."
  - CaseID: "U-UTL-LG-102"
    Module: "linkGenerator"
    Description: "Should handle cases where BASE_URL might have a trailing slash"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "edge-case"
      - "utility"
      - "url"
    Precondition:
      - "The `config.BASE_URL` is set to 'https://example.com/'."
      - "A `sessionId` is provided."
    Steps:
      - "1. Call `generateLink(sessionId)`."
    ExpectedResult:
      - "The function returns a correctly formatted URL without double slashes (e.g., 'https://example.com/?sessionId=...'). The current implementation might fail this test if it doesn't handle the trailing slash, which is a valid test case."
