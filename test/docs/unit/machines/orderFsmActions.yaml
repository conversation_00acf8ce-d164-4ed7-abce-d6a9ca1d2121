TestFile: whatsapp/machines/orderFsmActions.js
---
TestFunction: setText
Cases:
  - CaseID: "U-OFA-ST-101"
    Module: "orderFsmActions"
    Description: "Should assign the received text to the context"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "context-update"
    Precondition:
      - "An 'event' object is provided with `event.data.text` containing a string."
    Steps:
      - "1. Call `setText(context, event)`."
      - "2. The function uses `assign` from xstate."
    ExpectedResult:
      - "Returns an assignment function that updates the context's `text` property to the value of `event.data.text`."
---
TestFunction: sendMenu
Cases:
  - CaseID: "U-OFA-SM-201"
    Module: "orderFsmActions"
    Description: "Should build and send the main menu message"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "messaging"
    Precondition:
      - "A 'context' object is provided with `context.waId`."
      - "Mock `messageBuilders.buildMenuMessage` to return a valid message payload."
      - "Mock `whatsappService.sendMessage` to resolve successfully."
    Steps:
      - "1. Call `sendMenu(context, event)`."
    ExpectedResult:
      - "`messageBuilders.buildMenuMessage` is called."
      - "`whatsappService.sendMessage` is called with `context.waId` and the built message."
  - CaseID: "U-OFA-SM-202"
    Module: "orderFsmActions"
    Description: "Should log an error if sending the menu fails"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `whatsappService.sendMessage` to reject with an error."
    Steps:
      - "1. Call `sendMenu(context, event)`."
    ExpectedResult:
      - "The error from `whatsappService.sendMessage` is caught."
      - "`logger.error` is called with the error details."
---
TestFunction: setOrderItem
Cases:
  - CaseID: "U-OFA-SOI-301"
    Module: "orderFsmActions"
    Description: "Should assign the selected item to the context"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "context-update"
    Precondition:
      - "An 'event' object is provided with `event.data` containing the item details."
    Steps:
      - "1. Call `setOrderItem(context, event)`."
    ExpectedResult:
      - "Returns an assignment function that updates `context.orderItem` with `event.data`."
---
TestFunction: sendItemAddedConfirmation
Cases:
  - CaseID: "U-OFA-SIAC-401"
    Module: "orderFsmActions"
    Description: "Should send a confirmation after an item is added to the cart"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "messaging"
    Precondition:
      - "A 'context' object is provided with `waId` and `orderItem`."
      - "Mock `messageBuilders.buildItemAddedMessage` returns a message payload."
      - "Mock `whatsappService.sendMessage` resolves successfully."
    Steps:
      - "1. Call `sendItemAddedConfirmation(context, event)`."
    ExpectedResult:
      - "`messageBuilders.buildItemAddedMessage` is called with `context.orderItem`."
      - "`whatsappService.sendMessage` is called with `context.waId` and the built message."
---
TestFunction: viewCart
Cases:
  - CaseID: "U-OFA-VC-501"
    Module: "orderFsmActions"
    Description: "Should send the current cart details to the user"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "messaging"
      - "cart"
    Precondition:
      - "A 'context' object is provided with `waId` and `session.cart`."
      - "The cart in the session is not empty."
      - "Mock `messageBuilders.buildCartMessage` returns a message payload."
      - "Mock `whatsappService.sendMessage` resolves successfully."
    Steps:
      - "1. Call `viewCart(context, event)`."
    ExpectedResult:
      - "`messageBuilders.buildCartMessage` is called with the cart from `context.session.cart`."
      - "`whatsappService.sendMessage` is called."
  - CaseID: "U-OFA-VC-502"
    Module: "orderFsmActions"
    Description: "Should send an 'empty cart' message if the cart is empty"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "cart"
      - "edge-case"
    Precondition:
      - "`context.session.cart` is null, undefined, or has a total quantity of 0."
      - "Mock `messageBuilders.buildEmptyCartMessage` returns a message payload."
      - "Mock `whatsappService.sendMessage` resolves successfully."
    Steps:
      - "1. Call `viewCart(context, event)`."
    ExpectedResult:
      - "`messageBuilders.buildEmptyCartMessage` is called."
      - "`whatsappService.sendMessage` is called."
---
TestFunction: clearSession
Cases:
  - CaseID: "U-OFA-CS-601"
    Module: "orderFsmActions"
    Description: "Should clear the user's session and send a confirmation"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session-management"
      - "reset"
    Precondition:
      - "A 'context' object with `session.id` and `waId`."
      - "Mock `sessionService.clearSession` resolves successfully."
      - "Mock `whatsappService.sendMessage` resolves successfully."
    Steps:
      - "1. Call `clearSession(context, event)`."
    ExpectedResult:
      - "`sessionService.clearSession` is called with `context.session.id`."
      - "A confirmation message is sent via `whatsappService.sendMessage`."
