TestFile: whatsapp/machines/dialog.js
---
TestFunction: dialogMachine (Overall Structure)
Cases:
  - CaseID: "U-DGM-S-101"
    Module: "dialogMachine"
    Description: "Should initialize with the correct initial state"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "initialization"
    Precondition:
      - "The dialogMachine is created from the configuration."
    Steps:
      - "1. Interpret the machine configuration."
      - "2. Check the machine's initial state value."
    ExpectedResult:
      - "The initial state is 'start'."
  - CaseID: "U-DGM-S-102"
    Module: "dialogMachine"
    Description: "Should have the correct initial context"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "initialization"
      - "context"
    Precondition:
      - "The dialogMachine is created from the configuration."
    Steps:
      - "1. Interpret the machine configuration."
      - "2. Check the machine's initial context."
    ExpectedResult:
      - "The initial context matches the defined structure (e.g., restaurantId, brandId are null, order is an empty object, etc.)."
---
TestFunction: State - 'start'
Cases:
  - CaseID: "U-DGM-ST-201"
    Module: "dialogMachine"
    Description: "On TEXT_RECEIVED, should transition to 'languageDetection'"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "transition"
    Precondition:
      - "Machine is in the 'start' state."
    Steps:
      - "1. Send a 'TEXT_RECEIVED' event to the machine."
    ExpectedResult:
      - "The machine transitions to the 'languageDetection' state."
      - "The 'setText' action is triggered."
---
TestFunction: State - 'languageDetection'
Cases:
  - CaseID: "U-DGM-LD-301"
    Module: "dialogMachine"
    Description: "Should invoke the 'detectLanguage' service"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "service-invocation"
    Precondition:
      - "Machine is in the 'languageDetection' state."
    Steps:
      - "1. Verify the state's `invoke` configuration."
    ExpectedResult:
      - "The state invokes a service named 'detectLanguage'."
      - "On success (onDone), it transitions to 'menu'."
      - "On error (onError), it transitions to 'error'."
---
TestFunction: State - 'menu'
Cases:
  - CaseID: "U-DGM-M-401"
    Module: "dialogMachine"
    Description: "Should have correct entry actions"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "action"
    Precondition:
      - "Machine transitions into the 'menu' state."
    Steps:
      - "1. Check the `entry` actions for the 'menu' state."
    ExpectedResult:
      - "The 'sendMenu' action is listed in the entry actions."
  - CaseID: "U-DGM-M-402"
    Module: "dialogMachine"
    Description: "On ADD_ITEM, should transition to 'addingItem'"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "transition"
    Precondition:
      - "Machine is in the 'menu' state."
    Steps:
      - "1. Send an 'ADD_ITEM' event."
    ExpectedResult:
      - "The machine transitions to the 'addingItem' state."
      - "The 'setOrderItem' action is triggered."
  - CaseID: "U-DGM-M-403"
    Module: "dialogMachine"
    Description: "On VIEW_CART, should transition to 'viewingCart'"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "fsm"
      - "transition"
    Precondition:
      - "Machine is in the 'menu' state."
    Steps:
      - "1. Send a 'VIEW_CART' event."
    ExpectedResult:
      - "The machine transitions to the 'viewingCart' state."
---
TestFunction: State - 'checkout'
Cases:
  - CaseID: "U-DGM-CO-501"
    Module: "dialogMachine"
    Description: "Should have correct sub-states for the checkout process"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "state-structure"
    Precondition:
      - "Inspect the 'checkout' state configuration."
    Steps:
      - "1. Check the initial state of the 'checkout' state."
      - "2. List all sub-states within 'checkout'."
    ExpectedResult:
      - "The initial sub-state is 'confirmOrder'."
      - "It contains sub-states like 'confirmOrder', 'collectingAddress', 'payment', 'orderConfirmed'."
  - CaseID: "U-DGM-CO-502"
    Module: "dialogMachine"
    Description: "From 'confirmOrder', on CONFIRM_ORDER, should transition to 'collectingAddress'"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "transition"
    Precondition:
      - "Machine is in the 'checkout.confirmOrder' state."
    Steps:
      - "1. Send a 'CONFIRM_ORDER' event."
    ExpectedResult:
      - "The machine transitions to the 'checkout.collectingAddress' state."
---
TestFunction: Global Events & Actions
Cases:
  - CaseID: "U-DGM-GE-601"
    Module: "dialogMachine"
    Description: "On 'RESET' event, should transition to 'clearingSession'"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "fsm"
      - "global-event"
      - "reset"
    Precondition:
      - "Machine is in any state (e.g., 'menu', 'checkout')."
    Steps:
      - "1. Send a 'RESET' event to the machine."
    ExpectedResult:
      - "The machine transitions to the 'clearingSession' state."
      - "The 'clearSession' action is triggered."
