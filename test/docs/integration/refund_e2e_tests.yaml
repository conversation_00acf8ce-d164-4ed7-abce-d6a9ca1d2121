TestFile: refund.e2e.test.js
---
# 端到端测试 - 完整退款流程
TestFunction: completeRefundWorkflow
Cases:
  - CaseID: "E2E-REFUND-001"
    Module: "e2e"
    Description: "完整的订单取消和全额退款端到端流程"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "full-refund"
      - "critical-path"
    Precondition:
      - "启动真实MongoDB和Redis容器"
      - "创建测试餐厅和客户数据"
      - "创建已支付的Stripe订单"
      - "配置真实Stripe测试环境"
    Steps:
      - "餐厅管理员登录系统"
      - "调用cancelOrder GraphQL mutation"
      - "传入订单ID和退款原因MERCHANT_OUT_OF_STOCK"
      - "系统验证权限和订单状态"
      - "系统计算退款金额和手续费"
      - "系统调用Stripe退款API"
      - "系统创建退款记录"
      - "系统更新订单状态为CANCELLED"
      - "模拟Stripe webhook回调成功"
      - "系统处理webhook并更新退款状态"
      - "系统发送退款成功通知"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "退款记录状态为SUCCEEDED"
      - "客户收到全额退款"
      - "商家承担交易手续费"
      - "数据库状态一致性正确"
      - "通知发送成功"

  - CaseID: "E2E-REFUND-002"
    Module: "e2e"
    Description: "完整的部分退款端到端流程"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "partial-refund"
      - "critical-path"
    Precondition:
      - "启动真实数据库环境"
      - "创建已支付订单（金额100.00）"
      - "餐厅管理员已认证"
    Steps:
      - "调用refundOrder GraphQL mutation"
      - "传入退款金额30.00和原因MERCHANT_OTHER"
      - "传入退款原因文本'部分商品缺货'"
      - "系统验证退款金额有效性"
      - "系统计算手续费承担方"
      - "系统调用Stripe部分退款API"
      - "系统创建部分退款记录"
      - "系统更新订单状态为PARTIALLY_REFUNDED"
      - "模拟Stripe webhook成功回调"
      - "系统更新退款状态为SUCCEEDED"
      - "发起第二次部分退款20.00"
      - "验证多次退款的累计金额"
    ExpectedResult:
      - "第一次退款成功，金额30.00"
      - "订单状态为PARTIALLY_REFUNDED"
      - "第二次退款成功，金额20.00"
      - "订单总退款金额为50.00"
      - "订单包含2条退款记录"
      - "数据一致性正确"

  - CaseID: "E2E-REFUND-003"
    Module: "e2e"
    Description: "退款失败场景端到端处理"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "failure-handling"
      - "error-scenarios"
    Precondition:
      - "创建已支付订单"
      - "配置Stripe API模拟失败"
    Steps:
      - "尝试超额退款（150.00 > 100.00订单金额）"
      - "验证系统拒绝请求"
      - "发起有效退款请求30.00"
      - "模拟Stripe API返回失败"
      - "模拟Stripe webhook失败回调"
      - "系统处理失败状态"
      - "验证错误处理和状态回滚"
    ExpectedResult:
      - "超额退款被正确拒绝"
      - "错误信息包含可用金额提示"
      - "Stripe失败时退款状态为FAILED"
      - "错误信息正确记录"
      - "订单状态保持一致"
      - "失败通知发送成功"

---
# 端到端测试 - 权限和安全
TestFunction: authorizationAndSecurity
Cases:
  - CaseID: "E2E-AUTH-001"
    Module: "security"
    Description: "未认证用户访问退款接口"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "security"
      - "authentication"
    Precondition:
      - "创建已支付订单"
      - "不提供认证token"
    Steps:
      - "尝试调用refundOrder mutation"
      - "不设置Authorization header"
      - "系统验证认证状态"
    ExpectedResult:
      - "返回401 Unauthenticated错误"
      - "不创建退款记录"
      - "订单状态不变"

  - CaseID: "E2E-AUTH-002"
    Module: "security"
    Description: "跨餐厅访问权限验证"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "security"
      - "authorization"
    Precondition:
      - "创建餐厅A的已支付订单"
      - "使用餐厅B的认证token"
    Steps:
      - "餐厅B尝试退款餐厅A的订单"
      - "系统验证订单所有权"
      - "系统拒绝跨餐厅操作"
    ExpectedResult:
      - "返回403 Access Denied错误"
      - "不创建退款记录"
      - "订单状态不变"

  - CaseID: "E2E-AUTH-003"
    Module: "security"
    Description: "退款记录查询权限验证"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "security"
      - "data-access"
    Precondition:
      - "创建餐厅A的退款记录"
      - "使用餐厅B的认证token"
    Steps:
      - "餐厅B尝试查询餐厅A的退款记录"
      - "调用getRefund query"
      - "系统验证数据访问权限"
    ExpectedResult:
      - "返回权限错误"
      - "不返回其他餐厅的退款数据"

---
# 端到端测试 - 数据一致性和并发
TestFunction: dataConsistencyAndConcurrency
Cases:
  - CaseID: "E2E-DATA-001"
    Module: "consistency"
    Description: "多次退款的数据一致性验证"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "data-consistency"
      - "multiple-operations"
    Precondition:
      - "创建订单金额100.00的已支付订单"
    Steps:
      - "执行第一次部分退款30.00"
      - "验证订单totalRefunded更新为30.00"
      - "执行第二次部分退款20.00"
      - "验证订单totalRefunded更新为50.00"
      - "查询所有退款记录"
      - "计算退款记录总和"
      - "验证与订单totalRefunded一致"
    ExpectedResult:
      - "订单totalRefunded = 50.00"
      - "退款记录总和 = 50.00"
      - "订单包含2条退款记录"
      - "每条退款记录状态正确"
      - "数据库关联关系正确"

  - CaseID: "E2E-DATA-002"
    Module: "concurrency"
    Description: "并发退款请求处理"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "e2e"
      - "concurrency"
      - "race-conditions"
    Precondition:
      - "创建订单金额100.00的已支付订单"
    Steps:
      - "同时发起两个退款请求"
      - "第一个请求退款60.00"
      - "第二个请求退款50.00"
      - "系统处理并发请求"
      - "验证只有一个请求成功"
    ExpectedResult:
      - "只有一个退款请求成功"
      - "另一个请求返回余额不足错误"
      - "数据库状态一致"
      - "无重复退款记录"

  - CaseID: "E2E-DATA-003"
    Module: "consistency"
    Description: "退款状态变更的原子性"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "atomicity"
      - "state-management"
    Precondition:
      - "创建处理中的退款记录"
    Steps:
      - "模拟Stripe webhook成功回调"
      - "系统开始处理状态更新"
      - "验证退款记录状态更新"
      - "验证订单状态同步更新"
      - "验证完成时间戳设置"
    ExpectedResult:
      - "退款状态原子性更新为SUCCEEDED"
      - "订单状态同步更新"
      - "completedAt时间戳正确设置"
      - "无中间状态残留"

---
# 端到端测试 - 集成和通知
TestFunction: integrationAndNotifications
Cases:
  - CaseID: "E2E-INTEG-001"
    Module: "stripe-integration"
    Description: "真实Stripe API集成测试"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "stripe-integration"
      - "real-api"
    Precondition:
      - "配置真实Stripe测试密钥"
      - "创建真实的Stripe支付意图"
      - "支付意图状态为succeeded"
    Steps:
      - "使用真实支付意图ID创建订单"
      - "发起退款请求"
      - "调用真实Stripe退款API"
      - "验证Stripe返回的退款对象"
      - "查询Stripe退款状态"
      - "验证退款金额和状态"
    ExpectedResult:
      - "Stripe退款API调用成功"
      - "返回有效的退款ID"
      - "退款状态为pending或succeeded"
      - "退款金额正确"
      - "系统记录Stripe退款ID"

  - CaseID: "E2E-INTEG-002"
    Module: "webhook-processing"
    Description: "Stripe Webhook端到端处理"
    Importance: "Critical"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "webhook"
      - "async-processing"
    Precondition:
      - "创建处理中的退款记录"
      - "配置Webhook签名验证"
    Steps:
      - "模拟Stripe发送refund.updated webhook"
      - "包含正确的签名和数据"
      - "系统接收并验证webhook"
      - "系统查找对应的退款记录"
      - "系统更新退款状态"
      - "系统更新订单状态"
      - "系统发送通知"
    ExpectedResult:
      - "Webhook签名验证通过"
      - "退款记录状态正确更新"
      - "订单状态同步更新"
      - "通知发送成功"
      - "处理日志记录完整"

  - CaseID: "E2E-INTEG-003"
    Module: "notifications"
    Description: "退款通知端到端流程"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "e2e"
      - "notifications"
      - "customer-experience"
    Precondition:
      - "配置通知服务"
      - "创建退款成功记录"
    Steps:
      - "退款状态更新为SUCCEEDED"
      - "触发通知发送流程"
      - "生成客户通知内容"
      - "发送短信/WhatsApp通知"
      - "记录通知发送状态"
    ExpectedResult:
      - "客户收到退款成功通知"
      - "通知包含实际退款金额"
      - "通知包含到账时间说明"
      - "通知发送状态记录正确"

---
# 端到端测试 - 性能和压力
TestFunction: performanceAndStress
Cases:
  - CaseID: "E2E-PERF-001"
    Module: "performance"
    Description: "大量退款操作性能测试"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "e2e"
      - "performance"
      - "load-testing"
    Precondition:
      - "创建100个已支付订单"
      - "配置性能监控"
    Steps:
      - "并发发起50个退款请求"
      - "监控响应时间"
      - "监控数据库性能"
      - "监控内存使用"
      - "验证所有请求处理完成"
    ExpectedResult:
      - "平均响应时间 < 2秒"
      - "95%请求响应时间 < 5秒"
      - "无内存泄漏"
      - "数据库连接正常"
      - "所有退款状态正确"

  - CaseID: "E2E-PERF-002"
    Module: "stress"
    Description: "系统压力测试"
    Importance: "Low"
    Status: "Planned"
    Tags:
      - "e2e"
      - "stress-testing"
      - "reliability"
    Precondition:
      - "配置压力测试环境"
      - "准备大量测试数据"
    Steps:
      - "持续发送退款请求30分钟"
      - "逐步增加并发量"
      - "监控系统资源使用"
      - "监控错误率"
      - "验证系统稳定性"
    ExpectedResult:
      - "系统保持稳定运行"
      - "错误率 < 1%"
      - "内存使用稳定"
      - "数据一致性保持"
      - "服务可用性 > 99%"

---
# 端到端测试总结
TestSummary:
  TotalCases: 15
  CriticalCases: 6
  HighPriorityCases: 4
  MediumPriorityCases: 3
  LowPriorityCases: 2
  ImplementedCases: 10
  PlannedCases: 5
  
  CoverageAreas:
    - "完整退款业务流程"
    - "权限和安全验证"
    - "数据一致性保证"
    - "第三方服务集成"
    - "异步处理和通知"
    - "错误处理和恢复"
    - "性能和可靠性"
  
  TestEnvironment:
    - "真实MongoDB数据库"
    - "真实Redis缓存"
    - "真实Stripe测试API"
    - "完整应用程序栈"
    - "端到端数据流"
