# Test Cases for Whatsapp Webhook Integration

This document outlines the test cases for the WhatsApp webhook integration. It is intended for product managers to review and for junior developers to implement.

## 1. Message Reception Scenarios

| Test Case ID | Description | Preconditions | Steps | Expected Result | Status |
| :--- | :--- | :--- | :--- | :--- | :--- |
| WH-MSG-001 | Receive a valid standard text message | User session does not exist. | 1. Send a webhook event with a valid text message payload. | 1. Webhook is accepted with a `202 Accepted` response. <br> 2. A new session is created for the user. <br> 3. The message is processed by the FSM. <br> 4. A response message is sent via WhatsApp service. | `Implemented` |
| WH-MSG-002 | Receive a message with an image | User session exists. | 1. Send a webhook event with a valid image message payload. | 1. The message is identified as media. <br> 2. The FSM handles the media input. <br> 3. A `200 OK` response is returned. | `Not Implemented` |
| WH-MSG-003 | Receive a message with a video | User session exists. | 1. Send a webhook event with a valid video message payload. | 1. The message is identified as media. <br> 2. The FSM handles the media input. <br> 3. A `200 OK` response is returned. | `Not Implemented` |
| WH-MSG-004 | Receive a message with user's location | User session exists. | 1. Send a webhook event with a valid location payload. | 1. The location data is extracted correctly. <br> 2. The FSM handles the location input. <br> 3. A `200 OK` response is returned. | `Not Implemented` |
| WH-MSG-005 | User clicks a quick reply button | A message with quick replies was sent to the user. | 1. Send a webhook event for a quick reply button click. | 1. The payload's `externalID` is correctly processed. <br> 2. The FSM transitions to the appropriate state. <br> 3. A `200 OK` response is returned. | `Not Implemented` |
| WH-MSG-006 | User selects an item from a list message | A list message was sent to the user. | 1. Send a webhook event for a list selection. | 1. The selected item's data is extracted. <br> 2. The FSM transitions based on the selection. <br> 3. A `200 OK` response is returned. | `Not Implemented` |

## 2. Session Management

| Test Case ID | Description | Preconditions | Steps | Expected Result | Status |
| :--- | :--- | :--- | :--- | :--- | :--- |
| WH-SES-001 | Create a new session for a new user | The user has never interacted with the service before. | 1. Send a message from a new phone number. | 1. A new session record is created in Redis. <br> 2. The session is associated with the user's phone number. <br> 3. The OrderFSM is initialized for the session. | `Implemented` |
| WH-SES-002 | Load an existing session for a returning user | The user has an active session. | 1. Send a message from a phone number with an existing session. | 1. The existing session is retrieved from the database. <br> 2. The message is processed within the context of the existing session state. | `Not Implemented` |

## 3. FSM Interaction

| Test Case ID | Description | Preconditions | Steps | Expected Result | Status |
| :--- | :--- | :--- | :--- | :--- | :--- |
| WH-FSM-001 | Valid input causes correct state transition | Session is in the `welcomed` state. | 1. Send a text message corresponding to a valid command (e.g., 'order'). | 1. The FSM transitions to the next logical state (e.g., `ordering`). | `Not Implemented` |
| WH-FSM-002 | Invalid input does not cause state transition | Session is in the `ordering` state. | 1. Send a random, unexpected text message. | 1. The FSM remains in the `ordering` state. <br> 2. An error or help message is sent to the user. | `Not Implemented` |

## 4. Error Handling

| Test Case ID | Description | Preconditions | Steps | Expected Result | Status |
| :--- | :--- | :--- | :--- | :--- | :--- |
| WH-ERR-001 | Handle a malformed webhook payload | N/A | 1. Send a webhook request with a missing required field (e.g., no `included` array). | 1. The server returns a `4xx` client error response (e.g., 400, 401). <br> 2. The error is logged. <br> 3. No message is sent. | `Implemented` |
| WH-ERR-002 | Handle a payload with an unknown message type | N/A | 1. Send a webhook request with a message type not supported by the system. | 1. The server returns a `200 OK` (as required by WhatsApp) but logs a warning. <br> 2. The message is gracefully ignored. | `Not Implemented` |

## 5. Security

| Test Case ID | Description | Preconditions | Steps | Expected Result | Status |
| :--- | :--- | :--- | :--- | :--- | :--- |
| WH-SEC-001 | Reject a request with an invalid signature | Webhook is configured to validate signatures. | 1. Send a webhook request with an incorrect signature. | 1. The server returns a `401 Unauthorized` response. <br> 2. The request body is not processed. | `Implemented` |
| WH-SEC-002 | Accept a request with a valid signature | Webhook is configured to validate signatures. | 1. Send a webhook request with a correct `X-Hub-Signature-256` header. | 1. The server returns a `200 OK` response. <br> 2. The request is processed normally. | `Not Implemented` |
