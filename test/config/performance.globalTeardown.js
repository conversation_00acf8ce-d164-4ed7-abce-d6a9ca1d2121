/**
 * Global Teardown for Performance Tests
 * 性能测试的全局清理
 */

const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('🧹 Cleaning up Performance Test Environment...');
  
  // 更新测试元数据
  const reportsDir = path.join(__dirname, '../performance/reports');
  const metadataFile = path.join(reportsDir, 'test-metadata.json');
  
  if (fs.existsSync(metadataFile)) {
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataFile, 'utf8'));
      metadata.endTime = new Date().toISOString();
      metadata.duration = new Date(metadata.endTime) - new Date(metadata.startTime);
      metadata.finalMemoryUsage = {
        heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + 'MB',
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024) + 'MB'
      };
      
      fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));
      console.log('📊 Performance test metadata updated');
    } catch (error) {
      console.warn('⚠️ Failed to update test metadata:', error.message);
    }
  }
  
  // 强制垃圾回收
  if (global.gc) {
    global.gc();
    console.log('🗑️ Garbage collection completed');
  }
  
  // 生成性能测试摘要
  try {
    const summaryFile = path.join(reportsDir, 'performance-summary.txt');
    const summary = `
Performance Test Summary
========================
Test completed at: ${new Date().toISOString()}
Environment: ${process.env.NODE_ENV}
Test Level: ${process.env.PERFORMANCE_TEST_LEVEL}
Node Version: ${process.version}
Platform: ${process.platform} ${process.arch}

Final Memory Usage:
- Heap Used: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB
- Heap Total: ${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB
- RSS: ${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB

Reports generated in: ${reportsDir}
`;
    
    fs.writeFileSync(summaryFile, summary);
    console.log('📋 Performance summary generated');
  } catch (error) {
    console.warn('⚠️ Failed to generate performance summary:', error.message);
  }
  
  console.log('✅ Performance Test Environment Cleaned Up');
};
