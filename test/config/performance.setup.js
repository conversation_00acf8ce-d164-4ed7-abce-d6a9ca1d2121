/**
 * Performance Testing Setup
 * 性能测试的专用设置
 */

// 增加 Jest 的超时时间
jest.setTimeout(120000); // 2分钟

// 设置性能测试环境变量
process.env.NODE_ENV = 'test';
process.env.PERFORMANCE_TEST = 'true';
process.env.VERBOSE_TESTS = 'true';

// 启用垃圾回收（如果可用）
if (global.gc) {
  // 在每个测试前运行垃圾回收
  beforeEach(() => {
    global.gc();
  });
}

// 性能测试的全局配置
global.performanceConfig = {
  // 默认的性能阈值
  defaultThresholds: {
    maxLatency: 1000,      // 最大延迟 1秒
    minThroughput: 50,     // 最小吞吐量 50 req/s
    maxErrorRate: 0.05,    // 最大错误率 5%
    maxMemoryIncrease: 500 // 最大内存增长 500MB
  },
  
  // 测试级别配置
  testLevels: {
    quick: {
      duration: 10,
      connections: 10
    },
    normal: {
      duration: 30,
      connections: 50
    },
    intensive: {
      duration: 60,
      connections: 100
    }
  }
};

// 性能测试辅助函数
global.performanceHelpers = {
  // 获取当前内存使用情况
  getMemoryUsage: () => {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024), // MB
      rss: Math.round(usage.rss / 1024 / 1024) // MB
    };
  },
  
  // 等待指定时间
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 计算百分比
  calculatePercentage: (value, total) => {
    return total > 0 ? (value / total) * 100 : 0;
  },
  
  // 格式化性能结果
  formatPerformanceResult: (result) => {
    return {
      requests: {
        total: result.requests?.total || 0,
        average: result.requests?.mean || 0,
        min: result.requests?.min || 0,
        max: result.requests?.max || 0
      },
      latency: {
        average: result.latency?.mean || 0,
        p50: result.latency?.p50 || 0,
        p95: result.latency?.p95 || 0,
        p99: result.latency?.p99 || 0,
        max: result.latency?.max || 0
      },
      throughput: {
        average: result.throughput?.mean || 0,
        total: result.throughput?.total || 0
      },
      errors: {
        total: result.errors || 0,
        timeouts: result.timeouts || 0,
        non2xx: result.non2xx || 0
      }
    };
  }
};

// 控制台输出美化
const originalConsoleLog = console.log;
console.log = (...args) => {
  if (process.env.VERBOSE_TESTS === 'true') {
    const timestamp = new Date().toISOString();
    originalConsoleLog(`[${timestamp}] [PERF]`, ...args);
  }
};

// 性能测试开始前的准备
beforeAll(async () => {
  console.log('🚀 Starting Performance Tests...');
  console.log('Memory usage at start:', global.performanceHelpers.getMemoryUsage());

  // 创建性能测试报告目录
  const fs = require('fs');
  const path = require('path');
  const reportsDir = path.join(__dirname, '../performance/reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // 验证必要的依赖
  try {
    require('autocannon');
    console.log('✅ Autocannon dependency verified');
  } catch (error) {
    console.error('❌ Autocannon not found. Please install it: npm install autocannon');
    throw error;
  }
});

// 性能测试结束后的清理
afterAll(async () => {
  console.log('✅ Performance Tests Completed');
  console.log('Memory usage at end:', global.performanceHelpers.getMemoryUsage());

  // 强制垃圾回收
  if (global.gc) {
    global.gc();
  }

  // 生成性能测试摘要
  try {
    const fs = require('fs');
    const path = require('path');
    const reportsDir = path.join(__dirname, '../performance/reports');
    const summaryFile = path.join(reportsDir, 'performance-summary.txt');
    const summary = `
Performance Test Summary
========================
Test completed at: ${new Date().toISOString()}
Environment: ${process.env.NODE_ENV}
Test Level: ${process.env.PERFORMANCE_TEST_LEVEL || 'normal'}
Node Version: ${process.version}
Platform: ${process.platform} ${process.arch}

Final Memory Usage:
- Heap Used: ${global.performanceHelpers.getMemoryUsage().heapUsed}MB
- Heap Total: ${global.performanceHelpers.getMemoryUsage().heapTotal}MB
- RSS: ${global.performanceHelpers.getMemoryUsage().rss}MB

Reports generated in: ${reportsDir}
`;

    fs.writeFileSync(summaryFile, summary);
    console.log('📋 Performance summary generated');
  } catch (error) {
    console.warn('⚠️ Failed to generate performance summary:', error.message);
  }
});
