/**
 * Global setup for Jest tests
 * This file runs once before all tests start
 */

const { GenericContainer } = require('testcontainers');
const mongoose = require('mongoose');
const Redis = require('ioredis');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

module.exports = async () => {
  console.log('🚀 Setting up test environment with testcontainers...');

  try {
    // Start MongoDB container
    console.log('Starting MongoDB container...');
    const mongoContainer = await new GenericContainer('mongo:7.0')
      .withExposedPorts(27017)
      .withEnvironment({
        'MONGO_INITDB_ROOT_USERNAME': 'testuser',
        'MONGO_INITDB_ROOT_PASSWORD': 'testpass',
        'MONGO_INITDB_DATABASE': 'firespoon_test'
      })
      .start();

    // Get MongoDB connection details
    const mongoHost = mongoContainer.getHost();
    const mongoPort = mongoContainer.getMappedPort(27017);

    // Create MongoDB connection string
    const mongoUri = `mongodb://testuser:testpass@${mongoHost}:${mongoPort}/firespoon_test?authSource=admin`;
    console.log(`MongoDB 7.0 running at mongodb://${mongoHost}:${mongoPort}`);

    // Store MongoDB connection info in global variable
    global.__MONGO_URI__ = mongoUri;

    // Store MongoDB container reference for teardown
    global.__MONGO_CONTAINER__ = mongoContainer;

    // Start Redis container
    console.log('Starting Redis container...');
    const redisContainer = await new GenericContainer('redis:7.2.4')
      .withExposedPorts(6379)
      .start();

    // Get Redis connection details
    const redisHost = redisContainer.getHost();
    const redisPort = redisContainer.getMappedPort(6379);

    // Create Redis connection string
    const redisUri = `redis://${redisHost}:${redisPort}`;
    console.log(`Redis 7.2.4 running at ${redisUri}`);

    // Store Redis connection info in global variable
    global.__REDIS_URI__ = redisUri;

    // Store Redis container reference for teardown
    global.__REDIS_CONTAINER__ = redisContainer;

    // Test connections
    console.log('Testing database connections...');

    // Test MongoDB connection
    try {
      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      console.log('✅ MongoDB connection successful');
      await mongoose.disconnect();
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }

    // Test Redis connection
    try {
      const redisClient = new Redis(redisUri);
      await redisClient.ping();
      console.log('✅ Redis connection successful');
      await redisClient.quit();
    } catch (error) {
      console.error('❌ Redis connection failed:', error);
      throw error;
    }

    // Create temp directory for test artifacts if it doesn't exist
    const tempDir = path.join(process.cwd(), 'test', 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    console.log('✅ Test environment setup complete');
  } catch (error) {
    console.error('❌ Error setting up test environment:', error);
    throw error;
  }
};
