/**
 * Global teardown for Jest tests
 * This file runs once after all tests complete
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

module.exports = async () => {
  console.log('🧹 Cleaning up test environment...');

  try {
    // Close any remaining mongoose connections
    if (mongoose.connection.readyState !== 0) {
      console.log('Closing MongoDB connections...');
      await mongoose.disconnect();
    }

    // Stop MongoDB container
    if (global.__MONGO_CONTAINER__) {
      console.log('Stopping MongoDB container...');
      await global.__MONGO_CONTAINER__.stop();
    }

    // Stop Redis container
    if (global.__REDIS_CONTAINER__) {
      console.log('Stopping Redis container...');
      await global.__REDIS_CONTAINER__.stop();
    }

    // Clean up temp files
    const tempDir = path.join(process.cwd(), 'test', 'temp');
    if (fs.existsSync(tempDir)) {
      console.log('Cleaning up temporary test files...');
      // Only remove files, not the directory itself
      const files = fs.readdirSync(tempDir);
      for (const file of files) {
        fs.unlinkSync(path.join(tempDir, file));
      }
    }

    console.log('✅ Test environment cleanup complete');
  } catch (error) {
    console.error('❌ Error during test environment cleanup:', error);
    throw error;
  }
};
