/**
 * Jest configuration for Performance Testing
 * 性能测试专用的 Jest 配置
 */
const path = require('path');
const baseConfig = require('./jest.config.js');

module.exports = {
  ...baseConfig,
  
  // 性能测试需要更长的超时时间
  testTimeout: 120000, // 2分钟
  
  // 性能测试必须串行运行，避免资源竞争
  maxWorkers: 1,
  
  // 性能测试文件模式
  testMatch: [
    '<rootDir>/test/performance/**/*.test.js'
  ],
  
  // 性能测试不需要覆盖率报告
  collectCoverage: false,
  
  // 性能测试的环境变量
  setupFilesAfterEnv: [
    '<rootDir>/test/config/jest.setup.js',
    '<rootDir>/test/config/performance.setup.js'
  ],
  
  // 性能测试使用标准的全局设置来启动数据库容器
  globalSetup: '<rootDir>/test/config/globalSetup.js',
  globalTeardown: '<rootDir>/test/config/globalTeardown.js',
  
  // 强制退出，避免性能测试后进程挂起
  forceExit: true,
  
  // 禁用内存泄漏检测，性能测试会消耗大量内存
  detectLeaks: false,
  
  // 性能测试的报告器
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test/performance/reports',
      outputName: 'performance-test-results.xml',
      suiteName: 'Performance Tests'
    }]
  ],
  
  // 详细输出
  verbose: true,
  
  // 显示每个测试的执行时间
  displayName: {
    name: 'PERFORMANCE',
    color: 'yellow'
  }
};
