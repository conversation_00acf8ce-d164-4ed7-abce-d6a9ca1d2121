/**
 * Jest setup file - runs before each test file
 */

// Set test timeout
jest.setTimeout(30000);

// Configure environment variables for testing
process.env.NODE_ENV = 'test';

// Optional: Silence console output during tests
// Uncomment if you want to suppress console output
/*
global.console = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};
*/

// Mock external services that should not be called during tests
jest.mock('../../whatsapp/services/whatsappService', () => {
  return {
    getAccessToken: jest.fn().mockResolvedValue('mock-access-token'),
    sendMessage: jest.fn().mockResolvedValue({ 
      status: 202, 
      data: { data: { id: 'mock-message-id' } } 
    }),
    buildBasicTextMessageData: jest.fn().mockImplementation((phone, text) => ({
      type: 'notification-messages',
      attributes: {
        channelType: 'WHATSAPP',
        context: { var_text: text }
      },
      relationships: {
        recipient: {
          data: {
            type: 'phones',
            id: phone
          }
        }
      }
    })),
    buildQuickReplyMessageData: jest.fn().mockImplementation((phone, options) => ({
      type: 'notification-messages',
      attributes: {
        channelType: 'WHATSAPP',
        context: { 
          var_text: options.text,
          var_buttons: options.buttons
        }
      },
      relationships: {
        recipient: {
          data: {
            type: 'phones',
            id: phone
          }
        }
      }
    })),
    sendBasicText: jest.fn().mockResolvedValue({ 
      status: 202, 
      data: { data: { id: 'mock-message-id' } } 
    }),
    sendQuickReply: jest.fn().mockResolvedValue({ 
      status: 202, 
      data: { data: { id: 'mock-message-id' } } 
    })
  };
});

// Mock Stripe API
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => {
    return {
      checkout: {
        sessions: {
          create: jest.fn().mockResolvedValue({
            id: 'mock-checkout-session-id',
            url: 'https://mock-checkout-url.com'
          })
        }
      },
      webhooks: {
        constructEvent: jest.fn().mockImplementation((body, signature, secret) => {
          return JSON.parse(body);
        })
      }
    };
  });
});

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
});

// Clean up after all tests
afterAll(async () => {
  // Clean up test app if it was initialized
  try {
    const { cleanupTestApp } = require('../helpers/testApp');
    await cleanupTestApp();
  } catch (error) {
    // Ignore cleanup errors
  }

  // Clean up API server if it was initialized
  try {
    const { cleanupApp } = require('../helpers/apiHelper');
    await cleanupApp();
  } catch (error) {
    // Ignore cleanup errors
  }

  // Restore all mocks
  jest.restoreAllMocks();

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
});
