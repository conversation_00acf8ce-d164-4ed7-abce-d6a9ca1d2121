/**
 * Global Setup for Performance Tests
 * 性能测试的全局设置
 */

const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('🔧 Setting up Performance Test Environment...');
  
  // 创建性能测试报告目录
  const reportsDir = path.join(__dirname, '../performance/reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
    console.log('📁 Created performance reports directory');
  }
  
  // 设置性能测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.PERFORMANCE_TEST = 'true';
  process.env.TEST_TIMEOUT = '120000';
  
  // 根据 CI 环境调整配置
  if (process.env.CI) {
    process.env.PERFORMANCE_TEST_LEVEL = 'quick';
    console.log('🏗️ CI environment detected, using quick test mode');
  } else {
    process.env.PERFORMANCE_TEST_LEVEL = process.env.PERFORMANCE_TEST_LEVEL || 'normal';
    console.log(`🎯 Using ${process.env.PERFORMANCE_TEST_LEVEL} test mode`);
  }
  
  // 启用详细日志
  process.env.VERBOSE_TESTS = 'true';
  
  // 创建性能测试开始时间戳文件
  const startTime = new Date().toISOString();
  const metadataFile = path.join(reportsDir, 'test-metadata.json');
  const metadata = {
    startTime,
    environment: process.env.NODE_ENV,
    testLevel: process.env.PERFORMANCE_TEST_LEVEL,
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    memoryLimit: process.env.NODE_OPTIONS?.includes('--max-old-space-size') ? 
      process.env.NODE_OPTIONS.match(/--max-old-space-size=(\d+)/)?.[1] + 'MB' : 'default'
  };
  
  fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2));
  console.log('📊 Performance test metadata saved');
  
  // 验证必要的依赖
  try {
    require('autocannon');
    console.log('✅ Autocannon dependency verified');
  } catch (error) {
    console.error('❌ Autocannon not found. Please install it: npm install autocannon');
    throw error;
  }
  
  // 检查可用内存
  const memoryUsage = process.memoryUsage();
  console.log('💾 Initial memory usage:', {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
    rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB'
  });
  
  console.log('✅ Performance Test Environment Ready');
};
