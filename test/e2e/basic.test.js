/**
 * 基础 E2E 测试
 * 逐步构建真正的端到端测试
 */

const request = require('supertest');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');

describe('Basic E2E Tests', () => {
  let app;
  let server;

  beforeAll(async () => {
    await connectTestDB();
    const testAppResult = await createTestApp();
    app = testAppResult.app;
    server = testAppResult.server;
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('Application Health', () => {
    test('should respond to health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(response.body.timestamp).toBeDefined();
    });

    test('should have working GraphQL endpoint', async () => {
      const introspectionQuery = `
        query {
          __schema {
            queryType {
              name
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ query: introspectionQuery })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__schema.queryType.name).toBe('Query');
    });

    test('should have WhatsApp endpoints', async () => {
      // 测试 WhatsApp webhook 端点（可能返回 405 Method Not Allowed，这是正常的）
      const response = await request(app)
        .get('/whatsapp/webhook');

      // WhatsApp webhook 通常只接受 POST，所以 GET 返回 405 是正常的
      expect([200, 405, 404]).toContain(response.status);

      console.log('WhatsApp webhook endpoint status:', response.status);
    });
  });

  describe('Database Operations', () => {
    test('should be able to create and query customer directly', async () => {
      const Customer = require('../../models/customer');
      
      // 直接创建客户，不使用 factory
      const mongoose = require('mongoose');
      const customerData = {
        customerId: new mongoose.Types.ObjectId(), // 修复：使用 ObjectId 而不是字符串
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+1234567890',
        addresses: [{
          addressId: new mongoose.Types.ObjectId(),
          placeId: 'test_place_1',
          formattedAddress: '123 Test Street, Test City',
          streetAddress: '123 Test Street',
          city: 'Test City',
          state: 'TC',
          postcode: '12345',
          country: 'Test Country',
          coordinates: {
            longitude: -73.935242,
            latitude: 40.730610
          },
          recipientName: 'Test Customer',
          phone: '+1234567890',
          isDefault: true,
          tag: 'Home'
        }]
      };

      const customer = new Customer(customerData);
      await customer.save();

      // 验证客户已创建
      const foundCustomer = await Customer.findOne({ phone: '+1234567890' });
      expect(foundCustomer).toBeDefined();
      expect(foundCustomer.name).toBe('Test Customer');
      expect(foundCustomer.addresses.length).toBe(1);
      
      console.log('✅ Customer creation test passed');
    });

    test('should be able to create restaurant owner directly', async () => {
      const Owner = require('../../models/owner');
      
      const ownerData = {
        name: 'Test Owner',
        email: '<EMAIL>',
        phone: '+1234567891',
        password: 'test_password_123', // 添加必需字段
        userType: 'RESTAURANT_OWNER' // 添加必需字段
      };

      const owner = new Owner(ownerData);
      await owner.save();

      const foundOwner = await Owner.findOne({ email: '<EMAIL>' });
      expect(foundOwner).toBeDefined();
      expect(foundOwner.name).toBe('Test Owner');
      
      console.log('✅ Owner creation test passed');
    });
  });

  describe('GraphQL Schema Validation', () => {
    test('should have required types in schema', async () => {
      const typesQuery = `
        query {
          __schema {
            types {
              name
              kind
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ query: typesQuery })
        .expect(200);

      const typeNames = response.body.data.__schema.types.map(type => type.name);
      
      // 检查基本类型
      expect(typeNames).toContain('String');
      expect(typeNames).toContain('ID');
      expect(typeNames).toContain('Boolean');
      
      console.log('Available types count:', typeNames.length);
    });
  });
});
