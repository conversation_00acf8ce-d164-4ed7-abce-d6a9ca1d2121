/**
 * 完整的退款系统端到端测试
 * 解决了之前的技术问题，提供完整的E2E测试覆盖
 */

const mongoose = require('mongoose');
const Order = require('../../../models/order');
const Refund = require('../../../models/refund');
const Restaurant = require('../../../models/restaurant');
const Customer = require('../../../models/customer');
const refundService = require('../../../services/refundService');
const restaurantResolver = require('../../../graphql/resolvers/restaurant');
const refundResolver = require('../../../graphql/resolvers/refund');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../../helpers/enum');

// 使用内存数据库避免容器问题
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('Complete Refund E2E Tests', () => {
  let mongoServer;
  let testData = {};

  beforeAll(async () => {
    // 设置Mongoose严格查询模式
    mongoose.set('strictQuery', false);
    
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
    console.log('🗄️ E2E MongoDB connected');
  }, 30000);

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
    console.log('🧹 E2E MongoDB stopped');
  }, 30000);

  beforeEach(async () => {
    // 清理数据
    await Promise.all([
      Order.deleteMany({}),
      Refund.deleteMany({}),
      Restaurant.deleteMany({}),
      Customer.deleteMany({})
    ]);

    // 重置所有Mock
    jest.clearAllMocks();

    // 创建测试数据
    testData = await createTestData();
  });

  describe('E2E-REFUND-001: 完整的订单取消和全额退款端到端流程', () => {
    test('should complete full order cancellation workflow end-to-end', async () => {
      // Precondition: 创建已支付的Stripe订单
      const order = await createPaidOrder(100.00);
      console.log('📦 Created paid order:', order.orderId);

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_e2e_full_refund_001',
        amount: 10000, // $100.00 in cents
        status: 'pending',
        payment_intent: order.paymentId
      });

      // Step 1: 餐厅管理员登录并取消订单
      const context = createGraphQLContext(testData.restaurant._id);
      
      const cancelResult = await restaurantResolver.Mutation.cancelOrder(
        null,
        {
          _id: order._id.toString(),
          reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
        },
        context
      );

      // Step 2: 验证取消结果
      expect(cancelResult.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      expect(cancelResult.reason).toBe(REFUND_REASON.MERCHANT_OUT_OF_STOCK);
      console.log('✅ Order cancelled successfully');

      // Step 3: 验证退款记录创建
      const refund = await Refund.findOne({ orderId: order.orderId });
      expect(refund).toBeTruthy();
      expect(refund.reason).toBe(REFUND_REASON.MERCHANT_OUT_OF_STOCK);
      expect(refund.status).toBe(REFUND_STATUS.PROCESSING);
      expect(refund.finalRefundAmount).toBe(100.00);
      expect(refund.feeBearer).toBe('MERCHANT');
      console.log('💰 Refund record created:', refund.refundId);

      // Step 4: 验证Stripe API调用
      expect(stripeRefundService.createRefund).toHaveBeenCalledWith(
        order.paymentId,
        100.00,
        'requested_by_customer',
        expect.any(Object)
      );

      // Step 5: 模拟Stripe Webhook成功回调
      await refundService.processRefundWebhook(
        refund.stripeRefundId,
        'succeeded',
        {
          id: refund.stripeRefundId,
          status: 'succeeded',
          amount: 10000
        }
      );

      // Step 6: 验证最终状态
      const finalOrder = await Order.findById(order._id);
      const finalRefund = await Refund.findById(refund._id);

      expect(finalOrder.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      expect(finalOrder.refundStatus).toBe('FULL');
      expect(finalOrder.totalRefunded).toBe(100.00);

      expect(finalRefund.status).toBe(REFUND_STATUS.SUCCEEDED);
      expect(finalRefund.completedAt).toBeTruthy();

      console.log('🎉 Full cancellation E2E workflow completed successfully');
    });
  });

  describe('E2E-REFUND-002: 完整的部分退款端到端流程', () => {
    test('should complete partial refund workflow end-to-end', async () => {
      // Precondition: 创建已支付订单
      const order = await createPaidOrder(100.00);
      const context = createGraphQLContext(testData.restaurant._id);

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund')
        .mockResolvedValueOnce({
          id: 're_e2e_partial_001',
          amount: 3000, // $30.00
          status: 'pending'
        })
        .mockResolvedValueOnce({
          id: 're_e2e_partial_002',
          amount: 2000, // $20.00
          status: 'pending'
        });

      // Step 1: 发起第一次部分退款
      const refundAmount1 = 30.00;
      const refundResult1 = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: refundAmount1,
          reason: REFUND_REASON.MERCHANT_OTHER,
          reasonText: '部分商品缺货'
        },
        context
      );

      // Step 2: 验证第一次部分退款结果
      expect(refundResult1.success).toBe(true);
      expect(refundResult1.refund.finalRefundAmount).toBe(refundAmount1);
      expect(refundResult1.refund.reasonText).toBe('部分商品缺货');
      console.log('💸 First partial refund initiated:', refundResult1.refund.refundId);

      // Step 3: 模拟第一次Webhook成功回调
      const refund1 = await Refund.findOne({ refundId: refundResult1.refund.refundId });
      await refundService.processRefundWebhook(
        refund1.stripeRefundId,
        'succeeded',
        {
          id: refund1.stripeRefundId,
          status: 'succeeded',
          amount: 3000
        }
      );

      // Step 4: 验证第一次退款后的状态
      let updatedOrder = await Order.findById(order._id);
      expect(updatedOrder.orderStatus).toBe(ORDER_STATUS.PARTIALLY_REFUNDED);
      expect(updatedOrder.refundStatus).toBe('PARTIAL');
      expect(updatedOrder.totalRefunded).toBe(refundAmount1);

      // Step 5: 发起第二次部分退款
      const refundAmount2 = 20.00;
      const refundResult2 = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: refundAmount2,
          reason: REFUND_REASON.MERCHANT_OTHER,
          reasonText: '再次退款部分商品'
        },
        context
      );

      expect(refundResult2.success).toBe(true);
      console.log('💸 Second partial refund initiated:', refundResult2.refund.refundId);

      // Step 6: 模拟第二次Webhook成功回调
      const refund2 = await Refund.findOne({ refundId: refundResult2.refund.refundId });
      await refundService.processRefundWebhook(
        refund2.stripeRefundId,
        'succeeded',
        {
          id: refund2.stripeRefundId,
          status: 'succeeded',
          amount: 2000
        }
      );

      // Step 7: 验证多次退款的最终结果
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.totalRefunded).toBe(refundAmount1 + refundAmount2);
      expect(finalOrder.refunds).toHaveLength(2);

      const allRefunds = await Refund.find({ orderId: order.orderId });
      expect(allRefunds).toHaveLength(2);
      expect(allRefunds.every(r => r.status === REFUND_STATUS.SUCCEEDED)).toBe(true);

      console.log('🎯 Multiple partial refunds E2E workflow completed successfully');
    });
  });

  describe('E2E-REFUND-003: 退款失败场景端到端处理', () => {
    test('should handle refund failure scenarios end-to-end', async () => {
      const order = await createPaidOrder(100.00);
      const context = createGraphQLContext(testData.restaurant._id);

      // Test 1: 超额退款验证
      const excessiveRefundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: 150.00, // 超过订单金额
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(excessiveRefundResult.success).toBe(false);
      expect(excessiveRefundResult.message).toContain('exceed order amount');
      console.log('❌ Excessive refund correctly rejected');

      // Test 2: Stripe API失败处理
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockRejectedValue(
        new Error('Stripe API error: insufficient_funds')
      );

      const validRefundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: 30.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(validRefundResult.success).toBe(false);
      expect(validRefundResult.message).toContain('Stripe refund failed');
      console.log('❌ Stripe failure correctly handled');

      // Test 3: Webhook失败处理
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_e2e_failure_test',
        amount: 3000,
        status: 'pending'
      });

      const refundForFailure = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: 30.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(refundForFailure.success).toBe(true);

      // 模拟失败的Webhook
      const refund = await Refund.findOne({ refundId: refundForFailure.refund.refundId });
      await refundService.processRefundWebhook(
        refund.stripeRefundId,
        'failed',
        {
          id: refund.stripeRefundId,
          status: 'failed',
          failure_reason: 'insufficient_funds'
        }
      );

      // 验证失败处理
      const failedRefund = await Refund.findById(refund._id);
      expect(failedRefund.status).toBe(REFUND_STATUS.FAILED);
      expect(failedRefund.errorMessage).toBe('insufficient_funds');

      console.log('🛡️ Failure scenarios E2E handling completed successfully');
    });
  });

  describe('E2E-AUTH-001/002: 权限和安全端到端验证', () => {
    test('should enforce authentication and authorization end-to-end', async () => {
      const order = await createPaidOrder(100.00);

      // Test 1: 未认证访问
      const unauthenticatedContext = { req: { restaurantId: null }, res: {} };

      await expect(
        refundResolver.Mutation.refundOrder(
          null,
          {
            _id: order._id.toString(),
            amount: 30.00,
            reason: REFUND_REASON.MERCHANT_OTHER
          },
          unauthenticatedContext
        )
      ).rejects.toThrow('Unauthenticated');

      console.log('🔒 Unauthenticated access correctly blocked');

      // Test 2: 跨餐厅访问
      const otherRestaurant = await createTestRestaurant('Other Restaurant');
      const unauthorizedContext = createGraphQLContext(otherRestaurant._id);

      await expect(
        refundResolver.Mutation.refundOrder(
          null,
          {
            _id: order._id.toString(),
            amount: 30.00,
            reason: REFUND_REASON.MERCHANT_OTHER
          },
          unauthorizedContext
        )
      ).rejects.toThrow('access denied');

      console.log('🔒 Cross-restaurant access correctly blocked');

      // Test 3: 查询权限验证
      const refund = new Refund({
        refundId: 'REF-AUTH-TEST',
        orderId: order.orderId,
        originalOrderId: order._id,
        refundType: 'PARTIAL',
        requestAmount: 30.00,
        finalRefundAmount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER,
        feeBearer: 'MERCHANT',
        status: REFUND_STATUS.SUCCEEDED,
        requestedBy: testData.restaurant._id
      });
      await refund.save();

      await expect(
        refundResolver.Query.getRefund(
          null,
          { refundId: refund.refundId },
          unauthorizedContext
        )
      ).rejects.toThrow('Access denied');

      console.log('🔒 Query authorization correctly enforced');
    });
  });

  describe('E2E-DATA-001: 数据一致性端到端验证', () => {
    test('should maintain data consistency across operations end-to-end', async () => {
      const order = await createPaidOrder(100.00);
      const context = createGraphQLContext(testData.restaurant._id);

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund')
        .mockResolvedValueOnce({ id: 're_consistency_1', amount: 3000, status: 'pending' })
        .mockResolvedValueOnce({ id: 're_consistency_2', amount: 2000, status: 'pending' })
        .mockResolvedValueOnce({ id: 're_consistency_3', amount: 1500, status: 'pending' });

      // 执行多个退款操作
      const refund1 = await refundResolver.Mutation.refundOrder(
        null,
        { _id: order._id.toString(), amount: 30.00, reason: REFUND_REASON.MERCHANT_OTHER },
        context
      );

      const refund2 = await refundResolver.Mutation.refundOrder(
        null,
        { _id: order._id.toString(), amount: 20.00, reason: REFUND_REASON.MERCHANT_OTHER },
        context
      );

      const refund3 = await refundResolver.Mutation.refundOrder(
        null,
        { _id: order._id.toString(), amount: 15.00, reason: REFUND_REASON.MERCHANT_OTHER },
        context
      );

      // 验证所有操作成功
      expect(refund1.success).toBe(true);
      expect(refund2.success).toBe(true);
      expect(refund3.success).toBe(true);

      // 验证数据一致性
      const updatedOrder = await Order.findById(order._id);
      const allRefunds = await Refund.find({ orderId: order.orderId });

      expect(updatedOrder.refunds).toHaveLength(3);
      expect(allRefunds).toHaveLength(3);

      const totalRefundAmount = allRefunds.reduce((sum, r) => sum + r.finalRefundAmount, 0);
      expect(totalRefundAmount).toBe(updatedOrder.totalRefunded);
      expect(totalRefundAmount).toBe(65.00); // 30 + 20 + 15

      // 验证关联关系
      const refundIds = allRefunds.map(r => r._id.toString());
      const orderRefundIds = updatedOrder.refunds.map(id => id.toString());
      expect(refundIds.sort()).toEqual(orderRefundIds.sort());

      console.log('📊 Data consistency E2E verification completed successfully');
    });
  });

  // 辅助函数
  async function createTestData() {
    const restaurant = await createTestRestaurant('E2E Test Restaurant');
    const customer = await createTestCustomer('E2E Test Customer');
    return { restaurant, customer };
  }

  async function createTestRestaurant(name) {
    const restaurant = new Restaurant({
      name,
      address: 'Test Address',
      phone: '+**********',
      email: '<EMAIL>',
      commissionRate: 15,
      deliveryTime: 30,
      isAvailable: true,
      stripeAccountId: 'acct_test_e2e',
      stripeDetailsSubmitted: true
    });
    return await restaurant.save();
  }

  async function createTestCustomer(name) {
    const customer = new Customer({
      customerId: `CUST-E2E-${Date.now()}`,
      phone: '+**********',
      email: '<EMAIL>',
      name,
      orderHistory: []
    });
    return await customer.save();
  }

  async function createPaidOrder(amount = 100.00) {
    const order = new Order({
      orderId: `ORDER-E2E-${Date.now()}`,
      orderAmount: amount,
      restaurantId: testData.restaurant._id.toString(),
      restaurantName: testData.restaurant.name,
      customerId: testData.customer.customerId,
      customerPhone: testData.customer.phone,
      items: [{
        _id: new mongoose.Types.ObjectId(),
        title: 'Test Item',
        quantity: 2,
        variation: { price: amount / 2 }
      }],
      deliveryAddress: 'Test Delivery Address',
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: amount,
      paymentId: `pi_test_e2e_${Date.now()}`,
      refunds: [],
      totalRefunded: 0,
      refundStatus: 'NONE'
    });
    return await order.save();
  }

  function createGraphQLContext(restaurantId) {
    return {
      req: {
        restaurantId: restaurantId.toString(),
        isAuth: true
      },
      res: {}
    };
  }
});
