/**
 * 最小化退款系统测试
 * 验证核心功能而不使用容器
 */

const mongoose = require('mongoose');
const Order = require('../../../models/order');
const Refund = require('../../../models/refund');
const Restaurant = require('../../../models/restaurant');
const refundService = require('../../../services/refundService');
const refundCalculationService = require('../../../services/refundCalculationService');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../../helpers/enum');

// 使用内存数据库
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('Minimal Refund System Tests', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
    console.log('🗄️ In-memory MongoDB connected');
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
    console.log('🧹 In-memory MongoDB stopped');
  });

  beforeEach(async () => {
    await Promise.all([
      Order.deleteMany({}),
      Refund.deleteMany({}),
      Restaurant.deleteMany({})
    ]);
  });

  describe('Core Refund Logic', () => {
    test('should calculate refund amounts correctly', () => {
      const mockOrder = {
        orderAmount: 100.00
      };

      // 商家原因退款
      const merchantRefund = refundCalculationService.calculateRefundAmount(
        mockOrder,
        30.00,
        REFUND_REASON.MERCHANT_OUT_OF_STOCK
      );

      expect(merchantRefund.customerReceives).toBe(30.00);
      expect(merchantRefund.feeBearer).toBe('MERCHANT');
      expect(merchantRefund.finalRefundAmount).toBe(30.00);

      // 客户原因退款
      const customerRefund = refundCalculationService.calculateRefundAmount(
        mockOrder,
        30.00,
        REFUND_REASON.CUSTOMER_CANCELLED
      );

      expect(customerRefund.feeBearer).toBe('CUSTOMER');
      expect(customerRefund.finalRefundAmount).toBeLessThan(30.00);

      console.log('✅ Refund calculation logic verified');
    });

    test('should validate refund amounts correctly', () => {
      const mockOrder = {
        orderAmount: 100.00,
        totalRefunded: 20.00
      };

      // 有效退款
      const validResult = refundCalculationService.validateRefundAmount(mockOrder, 50.00);
      expect(validResult.isValid).toBe(true);
      expect(validResult.availableAmount).toBe(80.00);

      // 超额退款
      const invalidResult = refundCalculationService.validateRefundAmount(mockOrder, 90.00);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Refund amount cannot exceed available amount: 80');

      console.log('✅ Refund validation logic verified');
    });

    test('should create refund records correctly', async () => {
      // 创建测试餐厅
      const restaurant = new Restaurant({
        name: 'Test Restaurant',
        address: 'Test Address',
        phone: '+**********',
        email: '<EMAIL>',
        stripeAccountId: 'acct_test',
        stripeDetailsSubmitted: true
      });
      await restaurant.save();

      // 创建测试订单
      const order = new Order({
        orderId: 'ORDER-TEST-001',
        orderAmount: 100.00,
        restaurantId: restaurant._id.toString(),
        restaurantName: restaurant.name,
        customerId: 'CUST-001',
        customerPhone: '+**********',
        items: [{ title: 'Test Item', quantity: 1, variation: { price: 100.00 } }],
        deliveryAddress: 'Test Address',
        orderStatus: ORDER_STATUS.PENDING,
        paymentMethod: 'STRIPE',
        paymentStatus: 'PAID',
        paidAmount: 100.00,
        paymentId: 'pi_test_123',
        refunds: [],
        totalRefunded: 0,
        refundStatus: 'NONE'
      });
      await order.save();

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_mock',
        amount: 3000,
        status: 'pending'
      });

      // 发起退款
      const refundResult = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        '测试退款',
        'PARTIAL',
        restaurant._id
      );

      // 验证结果
      expect(refundResult.success).toBe(true);
      expect(refundResult.refund).toBeTruthy();
      expect(refundResult.refund.finalRefundAmount).toBe(30.00);
      expect(refundResult.refund.reason).toBe(REFUND_REASON.MERCHANT_OTHER);
      expect(refundResult.refund.reasonText).toBe('测试退款');

      // 验证数据库记录
      const savedRefund = await Refund.findById(refundResult.refund._id);
      expect(savedRefund).toBeTruthy();
      expect(savedRefund.status).toBe(REFUND_STATUS.PROCESSING);

      const updatedOrder = await Order.findById(order._id);
      expect(updatedOrder.refunds).toHaveLength(1);
      expect(updatedOrder.totalRefunded).toBe(30.00);
      expect(updatedOrder.refundStatus).toBe('PARTIAL');

      console.log('✅ Refund record creation verified');
    });

    test('should handle webhook processing correctly', async () => {
      // 创建退款记录
      const refund = new Refund({
        refundId: 'REF-TEST-001',
        orderId: 'ORDER-TEST-001',
        originalOrderId: new mongoose.Types.ObjectId(),
        refundType: 'PARTIAL',
        requestAmount: 30.00,
        finalRefundAmount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER,
        feeBearer: 'MERCHANT',
        transactionFee: 3.20,
        status: REFUND_STATUS.PROCESSING,
        stripeRefundId: 're_test_webhook',
        requestedBy: new mongoose.Types.ObjectId()
      });
      await refund.save();

      // 处理成功webhook
      await refundService.processRefundWebhook(
        're_test_webhook',
        'succeeded',
        { id: 're_test_webhook', status: 'succeeded' }
      );

      // 验证状态更新
      const updatedRefund = await Refund.findById(refund._id);
      expect(updatedRefund.status).toBe(REFUND_STATUS.SUCCEEDED);
      expect(updatedRefund.completedAt).toBeTruthy();

      console.log('✅ Webhook processing verified');
    });

    test('should handle multiple partial refunds correctly', async () => {
      // 创建测试数据
      const restaurant = new Restaurant({
        name: 'Test Restaurant',
        stripeAccountId: 'acct_test'
      });
      await restaurant.save();

      const order = new Order({
        orderId: 'ORDER-MULTI-001',
        orderAmount: 100.00,
        restaurantId: restaurant._id.toString(),
        paymentMethod: 'STRIPE',
        paymentStatus: 'PAID',
        paymentId: 'pi_test_multi',
        refunds: [],
        totalRefunded: 0,
        refundStatus: 'NONE'
      });
      await order.save();

      // Mock Stripe
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund')
        .mockResolvedValueOnce({ id: 're_test_1', amount: 3000, status: 'pending' })
        .mockResolvedValueOnce({ id: 're_test_2', amount: 2000, status: 'pending' });

      // 第一次退款
      const refund1 = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        '第一次退款',
        'PARTIAL',
        restaurant._id
      );

      // 第二次退款
      const refund2 = await refundService.initiateRefund(
        order._id,
        20.00,
        REFUND_REASON.MERCHANT_OTHER,
        '第二次退款',
        'PARTIAL',
        restaurant._id
      );

      // 验证结果
      expect(refund1.success).toBe(true);
      expect(refund2.success).toBe(true);

      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.totalRefunded).toBe(50.00);
      expect(finalOrder.refunds).toHaveLength(2);

      const allRefunds = await Refund.find({ orderId: order.orderId });
      expect(allRefunds).toHaveLength(2);

      console.log('✅ Multiple partial refunds verified');
    });

    test('should reject invalid refund requests', async () => {
      const restaurant = new Restaurant({
        name: 'Test Restaurant',
        stripeAccountId: 'acct_test'
      });
      await restaurant.save();

      const order = new Order({
        orderId: 'ORDER-INVALID-001',
        orderAmount: 100.00,
        restaurantId: restaurant._id.toString(),
        paymentMethod: 'STRIPE',
        paymentStatus: 'PAID',
        totalRefunded: 80.00, // 已退款80
        refundStatus: 'PARTIAL'
      });
      await order.save();

      // 尝试超额退款
      await expect(
        refundService.initiateRefund(
          order._id,
          30.00, // 超过剩余的20
          REFUND_REASON.MERCHANT_OTHER,
          null,
          'PARTIAL',
          restaurant._id
        )
      ).rejects.toThrow('available amount');

      console.log('✅ Invalid refund rejection verified');
    });
  });

  describe('Integration Points', () => {
    test('should integrate with GraphQL resolvers', async () => {
      const refundResolver = require('../../../graphql/resolvers/refund');
      
      expect(refundResolver.Query.getRefund).toBeDefined();
      expect(refundResolver.Query.getOrderRefunds).toBeDefined();
      expect(refundResolver.Mutation.refundOrder).toBeDefined();

      console.log('✅ GraphQL resolver integration verified');
    });

    test('should integrate with restaurant resolver', async () => {
      const restaurantResolver = require('../../../graphql/resolvers/restaurant');
      
      expect(restaurantResolver.Mutation.cancelOrder).toBeDefined();

      console.log('✅ Restaurant resolver integration verified');
    });
  });
});
