module.exports = {
  displayName: 'Refund E2E Tests',
  testEnvironment: 'node',
  rootDir: '/home/<USER>/firespoon/Firespoon_API_TF',
  testMatch: [
    '<rootDir>/test/e2e/refund/**/*.test.js'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/test/e2e/refund/setup.js'
  ],
  testTimeout: 120000, // 2分钟超时，E2E测试需要更长时间
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
  maxWorkers: 1, // E2E测试串行运行
  collectCoverage: false, // E2E测试不收集覆盖率
  globalSetup: '<rootDir>/test/e2e/refund/globalSetup.js',
  globalTeardown: '<rootDir>/test/e2e/refund/globalTeardown.js'
};
