/**
 * 简化的退款系统E2E测试
 * 专注于核心业务流程验证
 */

const mongoose = require('mongoose');
const { GenericContainer } = require('testcontainers');
const Order = require('../../../models/order');
const Refund = require('../../../models/refund');
const Restaurant = require('../../../models/restaurant');
const Customer = require('../../../models/customer');
const refundService = require('../../../services/refundService');
const restaurantResolver = require('../../../graphql/resolvers/restaurant');
const refundResolver = require('../../../graphql/resolvers/refund');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../../helpers/enum');

describe('Simple Refund E2E Tests', () => {
  let mongoContainer;
  let testData = {};

  beforeAll(async () => {
    // 启动MongoDB容器
    mongoContainer = await new GenericContainer('mongo:5.0')
      .withExposedPorts(27017)
      .withEnvironment({
        MONGO_INITDB_ROOT_USERNAME: 'admin',
        MONGO_INITDB_ROOT_PASSWORD: 'password'
      })
      .start();

    // 连接数据库
    const mongoUri = `mongodb://admin:password@${mongoContainer.getHost()}:${mongoContainer.getMappedPort(27017)}/test_simple_e2e?authSource=admin`;
    await mongoose.connect(mongoUri);

    console.log('🐳 Simple E2E MongoDB container started');
  }, 60000);

  afterAll(async () => {
    await mongoose.disconnect();
    if (mongoContainer) await mongoContainer.stop();
    console.log('🧹 Simple E2E container stopped');
  }, 30000);

  beforeEach(async () => {
    // 清理数据
    await Promise.all([
      Order.deleteMany({}),
      Refund.deleteMany({}),
      Restaurant.deleteMany({}),
      Customer.deleteMany({})
    ]);

    // 创建测试数据
    testData = await createTestData();
  });

  describe('Core Refund Workflows', () => {
    test('should complete full order cancellation workflow', async () => {
      // Step 1: 创建已支付订单
      const order = await createPaidOrder();
      console.log('📦 Created paid order:', order.orderId);

      // Step 2: 模拟餐厅管理员取消订单
      const context = createGraphQLContext(testData.restaurant._id);
      
      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_e2e_full_refund',
        amount: order.orderAmount * 100,
        status: 'pending'
      });

      const cancelResult = await restaurantResolver.Mutation.cancelOrder(
        null,
        {
          _id: order._id.toString(),
          reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
        },
        context
      );

      // Step 3: 验证取消结果
      expect(cancelResult.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      console.log('✅ Order cancelled successfully');

      // Step 4: 验证退款记录创建
      const refund = await Refund.findOne({ orderId: order.orderId });
      expect(refund).toBeTruthy();
      expect(refund.reason).toBe(REFUND_REASON.MERCHANT_OUT_OF_STOCK);
      expect(refund.status).toBe(REFUND_STATUS.PROCESSING);
      console.log('💰 Refund record created:', refund.refundId);

      // Step 5: 模拟Stripe Webhook成功回调
      await refundService.processRefundWebhook(
        refund.stripeRefundId,
        'succeeded',
        {
          id: refund.stripeRefundId,
          status: 'succeeded',
          amount: order.orderAmount * 100
        }
      );

      // Step 6: 验证最终状态
      const finalOrder = await Order.findById(order._id);
      const finalRefund = await Refund.findById(refund._id);

      expect(finalOrder.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      expect(finalOrder.refundStatus).toBe('FULL');
      expect(finalOrder.totalRefunded).toBe(order.orderAmount);

      expect(finalRefund.status).toBe(REFUND_STATUS.SUCCEEDED);
      expect(finalRefund.completedAt).toBeTruthy();

      console.log('🎉 Full cancellation workflow completed successfully');
    });

    test('should complete partial refund workflow', async () => {
      // Step 1: 创建已支付订单
      const order = await createPaidOrder();
      const context = createGraphQLContext(testData.restaurant._id);

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_e2e_partial_refund',
        amount: 3000, // $30.00
        status: 'pending'
      });

      // Step 2: 发起部分退款
      const refundAmount = 30.00;
      const refundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: refundAmount,
          reason: REFUND_REASON.MERCHANT_OTHER,
          reasonText: '部分商品缺货'
        },
        context
      );

      // Step 3: 验证部分退款结果
      expect(refundResult.success).toBe(true);
      expect(refundResult.refund.finalRefundAmount).toBe(refundAmount);
      expect(refundResult.refund.reasonText).toBe('部分商品缺货');
      console.log('💸 Partial refund initiated:', refundResult.refund.refundId);

      // Step 4: 模拟Webhook成功回调
      const refund = await Refund.findOne({ refundId: refundResult.refund.refundId });
      await refundService.processRefundWebhook(
        refund.stripeRefundId,
        'succeeded',
        {
          id: refund.stripeRefundId,
          status: 'succeeded',
          amount: refundAmount * 100
        }
      );

      // Step 5: 验证部分退款状态
      const updatedOrder = await Order.findById(order._id);
      expect(updatedOrder.orderStatus).toBe(ORDER_STATUS.PARTIALLY_REFUNDED);
      expect(updatedOrder.refundStatus).toBe('PARTIAL');
      expect(updatedOrder.totalRefunded).toBe(refundAmount);

      // Step 6: 发起第二次部分退款
      const secondRefundAmount = 20.00;
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_e2e_second_refund',
        amount: 2000, // $20.00
        status: 'pending'
      });

      const secondRefundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: secondRefundAmount,
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(secondRefundResult.success).toBe(true);

      // Step 7: 验证多次退款结果
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.totalRefunded).toBe(refundAmount + secondRefundAmount);
      expect(finalOrder.refunds).toHaveLength(2);

      console.log('🎯 Multiple partial refunds completed successfully');
    });

    test('should handle refund validation and errors', async () => {
      const order = await createPaidOrder();
      const context = createGraphQLContext(testData.restaurant._id);

      // Test 1: 超额退款验证
      const excessiveRefundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: 150.00, // 超过订单金额
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(excessiveRefundResult.success).toBe(false);
      expect(excessiveRefundResult.message).toContain('exceed order amount');
      console.log('❌ Excessive refund correctly rejected');

      // Test 2: 负数金额验证
      const negativeRefundResult = await refundResolver.Mutation.refundOrder(
        null,
        {
          _id: order._id.toString(),
          amount: -10.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        },
        context
      );

      expect(negativeRefundResult.success).toBe(false);
      expect(negativeRefundResult.message).toContain('greater than 0');
      console.log('❌ Negative refund correctly rejected');

      // Test 3: 权限验证
      const otherRestaurant = await createTestRestaurant('Other Restaurant');
      const unauthorizedContext = createGraphQLContext(otherRestaurant._id);

      await expect(
        refundResolver.Mutation.refundOrder(
          null,
          {
            _id: order._id.toString(),
            amount: 30.00,
            reason: REFUND_REASON.MERCHANT_OTHER
          },
          unauthorizedContext
        )
      ).rejects.toThrow('access denied');

      console.log('🔒 Authorization correctly enforced');
    });
  });

  describe('Data Consistency and State Management', () => {
    test('should maintain data consistency across operations', async () => {
      const order = await createPaidOrder();
      const context = createGraphQLContext(testData.restaurant._id);

      // Mock Stripe服务
      const stripeRefundService = require('../../../services/stripeRefundService');
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_consistency',
        amount: 3000,
        status: 'pending'
      });

      // 执行多个退款操作
      const refund1 = await refundResolver.Mutation.refundOrder(
        null,
        { _id: order._id.toString(), amount: 30.00, reason: REFUND_REASON.MERCHANT_OTHER },
        context
      );

      const refund2 = await refundResolver.Mutation.refundOrder(
        null,
        { _id: order._id.toString(), amount: 20.00, reason: REFUND_REASON.MERCHANT_OTHER },
        context
      );

      // 验证数据一致性
      const updatedOrder = await Order.findById(order._id);
      const allRefunds = await Refund.find({ orderId: order.orderId });

      expect(updatedOrder.refunds).toHaveLength(2);
      expect(allRefunds).toHaveLength(2);

      const totalRefundAmount = allRefunds.reduce((sum, r) => sum + r.finalRefundAmount, 0);
      expect(totalRefundAmount).toBe(updatedOrder.totalRefunded);

      console.log('📊 Data consistency verified');
    });
  });

  // 辅助函数
  async function createTestData() {
    const restaurant = await createTestRestaurant('E2E Test Restaurant');
    const customer = await createTestCustomer('E2E Test Customer');
    return { restaurant, customer };
  }

  async function createTestRestaurant(name) {
    const restaurant = new Restaurant({
      name,
      address: 'Test Address',
      phone: '+**********',
      email: '<EMAIL>',
      commissionRate: 15,
      deliveryTime: 30,
      isAvailable: true,
      stripeAccountId: 'acct_test_e2e',
      stripeDetailsSubmitted: true
    });
    return await restaurant.save();
  }

  async function createTestCustomer(name) {
    const customer = new Customer({
      customerId: `CUST-E2E-${Date.now()}`,
      phone: '+**********',
      email: '<EMAIL>',
      name,
      orderHistory: []
    });
    return await customer.save();
  }

  async function createPaidOrder() {
    const order = new Order({
      orderId: `ORDER-E2E-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: testData.restaurant._id.toString(),
      restaurantName: testData.restaurant.name,
      customerId: testData.customer.customerId,
      customerPhone: testData.customer.phone,
      items: [{
        _id: new mongoose.Types.ObjectId(),
        title: 'Test Item',
        quantity: 2,
        variation: { price: 25.00 }
      }],
      deliveryAddress: 'Test Delivery Address',
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00,
      paymentId: `pi_test_e2e_${Date.now()}`,
      refunds: [],
      totalRefunded: 0,
      refundStatus: 'NONE'
    });
    return await order.save();
  }

  function createGraphQLContext(restaurantId) {
    return {
      req: {
        restaurantId: restaurantId.toString(),
        isAuth: true
      },
      res: {}
    };
  }
});
