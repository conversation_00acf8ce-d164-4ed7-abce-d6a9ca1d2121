/**
 * 退款系统端到端测试
 * 
 * 测试完整的退款流程：
 * 1. 创建订单 → 支付 → 取消/退款 → Webhook处理 → 通知发送
 * 2. 使用真实的数据库、Redis、Stripe测试环境
 * 3. 模拟真实的用户操作流程
 */

const { GenericContainer } = require('testcontainers');
const mongoose = require('mongoose');
const request = require('supertest');
const app = require('../../../app'); // 假设有Express app
const Order = require('../../../models/order');
const Refund = require('../../../models/refund');
const Restaurant = require('../../../models/restaurant');
const Customer = require('../../../models/customer');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../../helpers/enum');

describe('Refund System E2E Tests', () => {
  let mongoContainer;
  let redisContainer;
  let testData = {};

  beforeAll(async () => {
    // 启动测试容器
    mongoContainer = await new GenericContainer('mongo:5.0')
      .withExposedPorts(27017)
      .withEnvironment({
        MONGO_INITDB_ROOT_USERNAME: 'admin',
        MONGO_INITDB_ROOT_PASSWORD: 'password'
      })
      .start();

    redisContainer = await new GenericContainer('redis:7-alpine')
      .withExposedPorts(6379)
      .start();

    // 连接数据库
    const mongoUri = `mongodb://admin:password@${mongoContainer.getHost()}:${mongoContainer.getMappedPort(27017)}/test_e2e_refund?authSource=admin`;
    await mongoose.connect(mongoUri);

    console.log('🐳 E2E test containers started');
  }, 120000);

  afterAll(async () => {
    await mongoose.disconnect();
    if (mongoContainer) await mongoContainer.stop();
    if (redisContainer) await redisContainer.stop();
    console.log('🧹 E2E test containers stopped');
  }, 60000);

  beforeEach(async () => {
    // 清理测试数据
    await Promise.all([
      Order.deleteMany({}),
      Refund.deleteMany({}),
      Restaurant.deleteMany({}),
      Customer.deleteMany({})
    ]);

    // 创建测试数据
    testData = await createTestData();
  });

  describe('Complete Refund Workflow E2E', () => {
    test('should complete full order cancellation with refund workflow', async () => {
      // Step 1: 创建并支付订单（模拟Stripe支付成功）
      const order = await createPaidOrder();
      expect(order.paymentStatus).toBe('PAID');
      expect(order.orderStatus).toBe('PENDING');

      // Step 2: 餐厅管理员登录并取消订单
      const authToken = await getRestaurantAuthToken(testData.restaurant._id);
      
      const cancelResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: `
            mutation CancelOrder($orderId: String!, $reason: RefundReason!) {
              cancelOrder(_id: $orderId, reason: $reason) {
                _id
                orderId
                orderStatus
                refundStatus
                totalRefunded
                refunds {
                  _id
                  refundId
                  finalRefundAmount
                  status
                  reason
                }
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            reason: 'MERCHANT_OUT_OF_STOCK'
          }
        });

      expect(cancelResponse.status).toBe(200);
      expect(cancelResponse.body.data.cancelOrder.orderStatus).toBe('CANCELLED');
      expect(cancelResponse.body.data.cancelOrder.refunds).toHaveLength(1);

      // Step 3: 验证退款记录创建
      const refund = await Refund.findOne({ orderId: order.orderId });
      expect(refund).toBeTruthy();
      expect(refund.status).toBe('PROCESSING');
      expect(refund.reason).toBe('MERCHANT_OUT_OF_STOCK');

      // Step 4: 模拟Stripe Webhook回调
      const webhookResponse = await request(app)
        .post('/stripe/webhook')
        .set('stripe-signature', 'test-signature')
        .send({
          type: 'refund.updated',
          data: {
            object: {
              id: refund.stripeRefundId,
              status: 'succeeded',
              amount: order.orderAmount * 100, // Stripe uses cents
              payment_intent: order.paymentId
            }
          }
        });

      expect(webhookResponse.status).toBe(200);

      // Step 5: 验证最终状态
      const finalOrder = await Order.findById(order._id);
      const finalRefund = await Refund.findById(refund._id);

      expect(finalOrder.orderStatus).toBe('CANCELLED');
      expect(finalOrder.refundStatus).toBe('FULL');
      expect(finalOrder.totalRefunded).toBe(order.orderAmount);

      expect(finalRefund.status).toBe('SUCCEEDED');
      expect(finalRefund.completedAt).toBeTruthy();

      console.log('✅ Full cancellation workflow completed successfully');
    });

    test('should complete partial refund workflow', async () => {
      // Step 1: 创建已支付订单
      const order = await createPaidOrder();
      const authToken = await getRestaurantAuthToken(testData.restaurant._id);

      // Step 2: 发起部分退款
      const refundAmount = 30.00;
      const partialRefundResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: `
            mutation RefundOrder(
              $orderId: String!
              $amount: Float!
              $reason: RefundReason!
              $reasonText: String
            ) {
              refundOrder(
                _id: $orderId
                amount: $amount
                reason: $reason
                reasonText: $reasonText
              ) {
                success
                message
                refund {
                  _id
                  refundId
                  finalRefundAmount
                  status
                  reason
                  reasonText
                  feeBearer
                }
                order {
                  _id
                  orderStatus
                  refundStatus
                  totalRefunded
                }
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: refundAmount,
            reason: 'MERCHANT_OTHER',
            reasonText: '部分商品缺货，退还2个商品费用'
          }
        });

      expect(partialRefundResponse.status).toBe(200);
      expect(partialRefundResponse.body.data.refundOrder.success).toBe(true);
      expect(partialRefundResponse.body.data.refundOrder.refund.finalRefundAmount).toBe(refundAmount);

      // Step 3: 模拟Webhook处理
      const refund = await Refund.findOne({ orderId: order.orderId });
      
      await request(app)
        .post('/stripe/webhook')
        .set('stripe-signature', 'test-signature')
        .send({
          type: 'refund.updated',
          data: {
            object: {
              id: refund.stripeRefundId,
              status: 'succeeded',
              amount: refundAmount * 100
            }
          }
        });

      // Step 4: 验证部分退款结果
      const updatedOrder = await Order.findById(order._id);
      expect(updatedOrder.orderStatus).toBe('PARTIALLY_REFUNDED');
      expect(updatedOrder.refundStatus).toBe('PARTIAL');
      expect(updatedOrder.totalRefunded).toBe(refundAmount);

      // Step 5: 发起第二次部分退款
      const secondRefundAmount = 20.00;
      const secondRefundResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: `
            mutation RefundOrder(
              $orderId: String!
              $amount: Float!
              $reason: RefundReason!
            ) {
              refundOrder(
                _id: $orderId
                amount: $amount
                reason: $reason
              ) {
                success
                order {
                  totalRefunded
                  refunds {
                    finalRefundAmount
                  }
                }
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: secondRefundAmount,
            reason: 'MERCHANT_OTHER'
          }
        });

      expect(secondRefundResponse.status).toBe(200);
      expect(secondRefundResponse.body.data.refundOrder.success).toBe(true);

      // Step 6: 验证多次退款结果
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.totalRefunded).toBe(refundAmount + secondRefundAmount);
      expect(finalOrder.refunds).toHaveLength(2);

      console.log('✅ Partial refund workflow completed successfully');
    });

    test('should handle refund failure scenarios', async () => {
      const order = await createPaidOrder();
      const authToken = await getRestaurantAuthToken(testData.restaurant._id);

      // Step 1: 尝试超额退款
      const excessiveRefundResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: `
            mutation RefundOrder($orderId: String!, $amount: Float!, $reason: RefundReason!) {
              refundOrder(_id: $orderId, amount: $amount, reason: $reason) {
                success
                message
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: 150.00, // 超过订单金额
            reason: 'MERCHANT_OTHER'
          }
        });

      expect(excessiveRefundResponse.status).toBe(200);
      expect(excessiveRefundResponse.body.data.refundOrder.success).toBe(false);
      expect(excessiveRefundResponse.body.data.refundOrder.message).toContain('exceed order amount');

      // Step 2: 模拟Stripe退款失败
      const validRefundResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: `
            mutation RefundOrder($orderId: String!, $amount: Float!, $reason: RefundReason!) {
              refundOrder(_id: $orderId, amount: $amount, reason: $reason) {
                success
                refund { refundId stripeRefundId }
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: 30.00,
            reason: 'MERCHANT_OTHER'
          }
        });

      const refund = await Refund.findOne({ orderId: order.orderId });

      // 模拟失败的Webhook
      await request(app)
        .post('/stripe/webhook')
        .send({
          type: 'refund.updated',
          data: {
            object: {
              id: refund.stripeRefundId,
              status: 'failed',
              failure_reason: 'insufficient_funds'
            }
          }
        });

      // 验证失败处理
      const failedRefund = await Refund.findById(refund._id);
      expect(failedRefund.status).toBe('FAILED');
      expect(failedRefund.errorMessage).toBe('insufficient_funds');

      console.log('✅ Failure scenarios handled correctly');
    });
  });

  describe('Authorization and Security E2E', () => {
    test('should prevent unauthorized refund operations', async () => {
      const order = await createPaidOrder();
      
      // 尝试无认证访问
      const unauthorizedResponse = await request(app)
        .post('/graphql')
        .send({
          query: `
            mutation RefundOrder($orderId: String!, $amount: Float!, $reason: RefundReason!) {
              refundOrder(_id: $orderId, amount: $amount, reason: $reason) {
                success
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: 30.00,
            reason: 'MERCHANT_OTHER'
          }
        });

      expect(unauthorizedResponse.status).toBe(200);
      expect(unauthorizedResponse.body.errors).toBeTruthy();
      expect(unauthorizedResponse.body.errors[0].message).toContain('Unauthenticated');

      // 尝试其他餐厅的token访问
      const otherRestaurant = await createTestRestaurant('Other Restaurant');
      const otherToken = await getRestaurantAuthToken(otherRestaurant._id);

      const crossAccessResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${otherToken}`)
        .send({
          query: `
            mutation RefundOrder($orderId: String!, $amount: Float!, $reason: RefundReason!) {
              refundOrder(_id: $orderId, amount: $amount, reason: $reason) {
                success
              }
            }
          `,
          variables: {
            orderId: order._id.toString(),
            amount: 30.00,
            reason: 'MERCHANT_OTHER'
          }
        });

      expect(crossAccessResponse.body.errors).toBeTruthy();
      expect(crossAccessResponse.body.errors[0].message).toContain('access denied');

      console.log('✅ Authorization security verified');
    });
  });

  // 辅助函数
  async function createTestData() {
    const restaurant = await createTestRestaurant('Test Restaurant E2E');
    const customer = await createTestCustomer('E2E Customer');
    
    return { restaurant, customer };
  }

  async function createTestRestaurant(name) {
    const restaurant = new Restaurant({
      name,
      address: 'Test Address',
      phone: '+**********',
      email: '<EMAIL>',
      commissionRate: 15,
      deliveryTime: 30,
      isAvailable: true,
      stripeAccountId: 'acct_test_e2e',
      stripeDetailsSubmitted: true
    });
    return await restaurant.save();
  }

  async function createTestCustomer(name) {
    const customer = new Customer({
      customerId: `CUST-E2E-${Date.now()}`,
      phone: '+**********',
      email: '<EMAIL>',
      name,
      orderHistory: []
    });
    return await customer.save();
  }

  async function createPaidOrder() {
    const order = new Order({
      orderId: `ORDER-E2E-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: testData.restaurant._id.toString(),
      restaurantName: testData.restaurant.name,
      customerId: testData.customer.customerId,
      customerPhone: testData.customer.phone,
      items: [{
        _id: new mongoose.Types.ObjectId(),
        title: 'Test Item',
        quantity: 2,
        variation: { price: 25.00 }
      }],
      deliveryAddress: 'Test Delivery Address',
      orderStatus: 'PENDING',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00,
      paymentId: `pi_test_e2e_${Date.now()}`,
      refunds: [],
      totalRefunded: 0,
      refundStatus: 'NONE'
    });
    return await order.save();
  }

  async function getRestaurantAuthToken(restaurantId) {
    // 模拟JWT token生成
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { restaurantId: restaurantId.toString() },
      'test-secret',
      { expiresIn: '1h' }
    );
  }
});
