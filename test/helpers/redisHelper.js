/**
 * Redis helper functions for tests
 */

const Redis = require('ioredis');

let redisClient = null;

/**
 * Get a Redis client instance
 * @returns {Redis} Redis client
 */
function getRedisClient() {
  if (!redisClient) {
    redisClient = new Redis(global.__REDIS_URI__);
  }
  return redisClient;
}

/**
 * Close the Redis client connection
 * @returns {Promise<void>}
 */
async function closeRedisClient() {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
}

/**
 * Clear all keys in the Redis database
 * @returns {Promise<void>}
 */
async function clearRedis() {
  const client = getRedisClient();
  await client.flushdb();
}

/**
 * Get a value from Redis
 * @param {string} key - Redis key
 * @returns {Promise<string>} Value
 */
async function get(key) {
  const client = getRedisClient();
  return await client.get(key);
}

/**
 * Set a value in Redis
 * @param {string} key - Redis key
 * @param {string} value - Value to set
 * @param {number} [ttl] - Time to live in seconds (optional)
 * @returns {Promise<string>} "OK" if successful
 */
async function set(key, value, ttl = null) {
  const client = getRedisClient();
  if (ttl) {
    return await client.set(key, value, 'EX', ttl);
  }
  return await client.set(key, value);
}

/**
 * Delete a key from Redis
 * @param {string} key - Redis key
 * @returns {Promise<number>} Number of keys removed
 */
async function del(key) {
  const client = getRedisClient();
  return await client.del(key);
}

/**
 * Get a session from Redis
 * @param {string} sessionId - Session ID
 * @returns {Promise<Object>} Session data
 */
async function getSession(sessionId) {
  const client = getRedisClient();
  const sessionData = await client.get(`session:${sessionId}`);
  if (!sessionData) return null;
  return JSON.parse(sessionData);
}

/**
 * Set a session in Redis
 * @param {string} sessionId - Session ID
 * @param {Object} data - Session data
 * @param {number} [ttl=3600] - Time to live in seconds
 * @returns {Promise<string>} "OK" if successful
 */
async function setSession(sessionId, data, ttl = 3600) {
  const client = getRedisClient();
  return await client.set(
    `session:${sessionId}`,
    JSON.stringify(data),
    'EX',
    ttl
  );
}

/**
 * Delete a session from Redis
 * @param {string} sessionId - Session ID
 * @returns {Promise<number>} Number of keys removed
 */
async function deleteSession(sessionId) {
  const client = getRedisClient();
  return await client.del(`session:${sessionId}`);
}

/**
 * Get all keys matching a pattern
 * @param {string} pattern - Key pattern
 * @returns {Promise<Array<string>>} Matching keys
 */
async function keys(pattern) {
  const client = getRedisClient();
  return await client.keys(pattern);
}

/**
 * Check if a key exists
 * @param {string} key - Redis key
 * @returns {Promise<number>} 1 if key exists, 0 otherwise
 */
async function exists(key) {
  const client = getRedisClient();
  return await client.exists(key);
}

module.exports = {
  getRedisClient,
  closeRedisClient,
  clearRedis,
  get,
  set,
  del,
  getSession,
  setSession,
  deleteSession,
  keys,
  exists
};
