/**
 * GraphQL testing helper utilities
 */

const { createTestClient } = require('apollo-server-testing');
const { ApolloServer } = require('apollo-server-express');
const typeDefs = require('../../graphql/schema/index');
const resolvers = require('../../graphql/resolvers/index');

class GraphQLHelper {
  /**
   * Create a test Apollo Server with custom context
   * @param {Object} contextOverrides - Context overrides
   * @returns {Object} Apollo Server instance and test client
   */
  static createTestServer(contextOverrides = {}) {
    const server = new ApolloServer({
      typeDefs,
      resolvers,
      context: ({ req }) => {
        const defaultContext = {
          req: {
            isAuth: false,
            userId: null,
            userType: null,
            restaurantId: null
          }
        };

        return {
          ...defaultContext,
          ...contextOverrides,
          req: {
            ...defaultContext.req,
            ...(contextOverrides.req || {})
          }
        };
      }
    });

    const { query, mutate } = createTestClient(server);
    
    return { server, query, mutate };
  }

  /**
   * Create an authenticated test server
   * @param {string} userId - User ID
   * @param {string} userType - User type (customer, restaurant, admin, etc.)
   * @param {string} restaurantId - Restaurant ID (optional)
   * @returns {Object} Apollo Server instance and test client
   */
  static createAuthenticatedTestServer(userId, userType, restaurantId = null) {
    return this.createTestServer({
      req: {
        isAuth: true,
        userId: userId,
        userType: userType,
        restaurantId: restaurantId
      }
    });
  }

  /**
   * Create a WhatsApp authenticated test server
   * @param {Object} whatsappSession - WhatsApp session data
   * @returns {Object} Apollo Server instance and test client
   */
  static createWhatsAppTestServer(whatsappSession) {
    return this.createTestServer({
      req: {
        isAuth: false,
        whatsappSession: whatsappSession,
        isWhatsAppAuth: true
      }
    });
  }

  /**
   * Execute a GraphQL query and return formatted response
   * @param {Function} queryFn - Query function from test client
   * @param {string} query - GraphQL query string
   * @param {Object} variables - Query variables
   * @returns {Promise<Object>} Formatted response
   */
  static async executeQuery(queryFn, query, variables = {}) {
    const response = await queryFn({
      query,
      variables
    });

    return {
      data: response.data,
      errors: response.errors,
      hasErrors: !!response.errors,
      errorMessages: response.errors ? response.errors.map(e => e.message) : []
    };
  }

  /**
   * Execute a GraphQL mutation and return formatted response
   * @param {Function} mutateFn - Mutate function from test client
   * @param {string} mutation - GraphQL mutation string
   * @param {Object} variables - Mutation variables
   * @returns {Promise<Object>} Formatted response
   */
  static async executeMutation(mutateFn, mutation, variables = {}) {
    const response = await mutateFn({
      mutation,
      variables
    });

    return {
      data: response.data,
      errors: response.errors,
      hasErrors: !!response.errors,
      errorMessages: response.errors ? response.errors.map(e => e.message) : []
    };
  }

  /**
   * Assert that a GraphQL response is successful
   * @param {Object} response - GraphQL response
   * @param {string} dataKey - Key to check in response data
   */
  static assertSuccess(response, dataKey = null) {
    expect(response.hasErrors).toBe(false);
    expect(response.data).toBeDefined();
    
    if (dataKey) {
      expect(response.data[dataKey]).toBeDefined();
    }
  }

  /**
   * Assert that a GraphQL response has errors
   * @param {Object} response - GraphQL response
   * @param {string} expectedMessage - Expected error message (optional)
   */
  static assertError(response, expectedMessage = null) {
    expect(response.hasErrors).toBe(true);
    expect(response.errors).toBeDefined();
    expect(response.errors.length).toBeGreaterThan(0);
    
    if (expectedMessage) {
      expect(response.errorMessages).toContain(expectedMessage);
    }
  }

  /**
   * Assert authentication error
   * @param {Object} response - GraphQL response
   */
  static assertAuthError(response) {
    this.assertError(response, 'Unauthenticated');
  }

  /**
   * Assert authorization error
   * @param {Object} response - GraphQL response
   */
  static assertAuthzError(response) {
    this.assertError(response);
    expect(response.errorMessages.some(msg => 
      msg.includes('Unauthorized') || 
      msg.includes('Forbidden') || 
      msg.includes('Access denied')
    )).toBe(true);
  }

  /**
   * Common GraphQL query fragments
   */
  static get fragments() {
    return {
      userFields: `
        fragment UserFields on User {
          _id
          name
          email
          userType
          isActive
          phone
          emailIsVerified
          phoneIsVerified
        }
      `,
      
      restaurantFields: `
        fragment RestaurantFields on Restaurant {
          _id
          name
          description
          isActive
          location {
            coordinates
          }
          deliveryCostType
          deliveryCostMin
          minimumOrder
        }
      `,
      
      orderFields: `
        fragment OrderFields on Order {
          _id
          orderId
          orderStatus
          paymentStatus
          orderAmount
          deliveryCharges
          tipping
          taxationAmount
          isPickedUp
          createdAt
          acceptedAt
          deliveryTime
          completionTime
        }
      `,
      
      foodFields: `
        fragment FoodFields on Food {
          _id
          title
          description
          image
          price
          isActive
          isAvailable
          preparationTime
          variations {
            _id
            title
            price
            discounted
          }
        }
      `,
      
      addressFields: `
        fragment AddressFields on Address {
          _id
          label
          deliveryAddress
          details
          selected
          location {
            coordinates
          }
        }
      `
    };
  }

  /**
   * Common GraphQL queries
   */
  static get queries() {
    return {
      GET_PROFILE: `
        query GetProfile {
          profile {
            ...UserFields
            addresses {
              ...AddressFields
            }
          }
        }
        ${this.fragments.userFields}
        ${this.fragments.addressFields}
      `,
      
      GET_RESTAURANTS: `
        query GetRestaurants($location: LocationInput, $limit: Int, $offset: Int) {
          restaurants(location: $location, limit: $limit, offset: $offset) {
            ...RestaurantFields
            categories {
              _id
              title
              foods {
                ...FoodFields
              }
            }
          }
        }
        ${this.fragments.restaurantFields}
        ${this.fragments.foodFields}
      `,
      
      GET_ORDERS: `
        query GetOrders($userId: String, $status: String, $limit: Int) {
          orders(userId: $userId, status: $status, limit: $limit) {
            ...OrderFields
            restaurant {
              ...RestaurantFields
            }
            user {
              ...UserFields
            }
          }
        }
        ${this.fragments.orderFields}
        ${this.fragments.restaurantFields}
        ${this.fragments.userFields}
      `
    };
  }

  /**
   * Common GraphQL mutations
   */
  static get mutations() {
    return {
      PLACE_ORDER: `
        mutation PlaceOrder($orderInput: OrderInput!) {
          placeOrder(orderInput: $orderInput) {
            ...OrderFields
            restaurant {
              name
            }
          }
        }
        ${this.fragments.orderFields}
      `,
      
      UPDATE_ORDER_STATUS: `
        mutation UpdateOrderStatus($id: String!, $status: String!) {
          updateOrderStatus(id: $id, status: $status) {
            ...OrderFields
          }
        }
        ${this.fragments.orderFields}
      `,
      
      CREATE_USER: `
        mutation CreateUser($userInput: UserInput!) {
          createUser(userInput: $userInput) {
            ...UserFields
          }
        }
        ${this.fragments.userFields}
      `,
      
      ADD_CUSTOMER_ADDRESS: `
        mutation AddCustomerAddress($addressInput: AddressInput!) {
          addCustomerAddress(addressInput: $addressInput) {
            ...AddressFields
          }
        }
        ${this.fragments.addressFields}
      `
    };
  }

  /**
   * Generate test variables for common operations
   */
  static generateTestVariables() {
    return {
      orderInput: {
        restaurant: '507f1f77bcf86cd799439011',
        orderInput: [
          {
            food: '507f1f77bcf86cd799439012',
            variation: '507f1f77bcf86cd799439013',
            quantity: 2,
            addons: [],
            specialInstructions: 'No onions'
          }
        ],
        orderAmount: 25.99,
        deliveryCharges: 3.99,
        tipping: 5.00,
        taxationAmount: 2.08,
        paymentMethod: 'STRIPE',
        isPickedUp: false,
        deliveryAddress: {
          label: 'Home',
          deliveryAddress: '123 Test St, Test City',
          details: 'Apt 4B',
          location: {
            coordinates: [-73.935242, 40.730610]
          }
        }
      },
      
      userInput: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+1234567890',
        userType: 'customer'
      },
      
      addressInput: {
        label: 'Home',
        deliveryAddress: '123 Test St, Test City',
        details: 'Apt 4B',
        location: {
          coordinates: [-73.935242, 40.730610]
        }
      }
    };
  }
}

module.exports = GraphQLHelper;
