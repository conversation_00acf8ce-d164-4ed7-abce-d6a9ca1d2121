/**
 * API helper functions for tests
 */

const request = require('supertest');
const express = require('express');

// Create a simple mock app for testing
let app = null;

/**
 * Initialize a mock app for testing
 * @returns {Object} Express app instance
 */
function initializeApp() {
  if (!app) {
    app = express();

    // Add basic middleware
    app.use(express.json());

    // Mock WhatsApp webhook endpoint
    app.post('/whatsapp/webhook', (req, res) => {
      const signature = req.headers['x-mmc-signature'];

      // Simple signature validation mock
      if (!signature || signature === 'v1=invalid-signature') {
        return res.status(401).json({ error: 'Invalid signature' });
      }

      // Mock successful webhook processing
      res.status(202).json({ message: 'Webhook received' });
    });

    // Mock GraphQL endpoint
    app.post('/graphql', (req, res) => {
      const { query, variables } = req.body;
      const authHeader = req.headers.authorization;

      // Check for authentication
      const isAuthenticated = authHeader && authHeader.startsWith('Bearer ');

      // Parse the query to determine what to mock
      if (query && query.includes('placeOrder')) {
        // Check authentication for placeOrder
        if (!isAuthenticated) {
          return res.status(200).json({
            data: null,
            errors: [
              {
                message: 'Authentication required',
                path: ['placeOrder'],
                extensions: {
                  code: 'UNAUTHENTICATED'
                }
              }
            ]
          });
        }

        const input = variables?.input || {};

        // Validate restaurant ID (mock validation)
        if (input.restaurantId === 'invalid-restaurant-id') {
          return res.status(200).json({
            data: null,
            errors: [
              {
                message: 'Restaurant not found',
                path: ['placeOrder'],
                extensions: {
                  code: 'BAD_USER_INPUT'
                }
              }
            ]
          });
        }

        // Extract customer ID from auth token (mock)
        let customerId = 'mock-customer-id';
        try {
          const token = authHeader.replace('Bearer ', '');
          const jwt = require('jsonwebtoken');
          const decoded = jwt.decode(token);
          if (decoded && decoded.userId) {
            customerId = decoded.userId;
          }
        } catch (error) {
          // Use default if token parsing fails
        }

        res.status(200).json({
          data: {
            placeOrder: {
              _id: 'mock-order-id',
              status: 'PENDING',
              orderAmount: 25.99,
              restaurant: {
                _id: input.restaurantId || 'mock-restaurant-id',
                name: 'Mock Restaurant'
              },
              customer: {
                _id: customerId,
                name: 'Mock Customer'
              },
              isPickedUp: input.isPickedUp || false,
              instructions: input.instructions || ''
            }
          }
        });
      } else if (query && query.includes('updateOrderStatus')) {
        const { status } = variables || {};
        res.status(200).json({
          data: {
            updateOrderStatus: {
              _id: variables?.id || 'mock-order-id',
              status: status || 'ACCEPTED',
              updatedAt: new Date().toISOString()
            }
          }
        });
      } else {
        // Default mock response
        res.status(200).json({
          data: null,
          errors: [
            {
              message: 'Mock GraphQL endpoint - query not recognized',
              path: ['unknown']
            }
          ]
        });
      }
    });

    // Mock other endpoints as needed
    app.post('/stripe/create-checkout-session', (req, res) => {
      res.status(200).json({
        id: 'mock-checkout-session-id',
        url: 'https://mock-checkout-url.com'
      });
    });
  }
  return app;
}

/**
 * Clean up the app instance
 * @returns {Promise<void>}
 */
async function cleanupApp() {
  app = null;
}

/**
 * Get a supertest request object for the app
 * @returns {Object} Supertest request object
 */
function getRequest() {
  const appInstance = initializeApp();
  return request(appInstance);
}

/**
 * Send a GraphQL query
 * @param {string} query - GraphQL query or mutation
 * @param {Object} [variables={}] - Query variables
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function graphqlQuery(query, variables = {}, token = null) {
  const requestInstance = getRequest();
  const req = requestInstance.post('/graphql').send({
    query,
    variables
  });

  if (token) {
    req.set('Authorization', `Bearer ${token}`);
  }

  return await req;
}

/**
 * Send a GraphQL mutation
 * @param {string} mutation - GraphQL mutation
 * @param {Object} [variables={}] - Mutation variables
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function graphqlMutation(mutation, variables = {}, token = null) {
  return await graphqlQuery(mutation, variables, token);
}

/**
 * Send a POST request to a webhook endpoint
 * @param {string} endpoint - Webhook endpoint
 * @param {Object} payload - Request payload
 * @param {Object} [headers={}] - Request headers
 * @returns {Promise<Object>} Response
 */
async function postWebhook(endpoint, payload, headers = {}) {
  const requestInstance = getRequest();
  const req = requestInstance.post(endpoint).send(payload);

  // Set headers
  Object.entries(headers).forEach(([key, value]) => {
    req.set(key, value);
  });

  return await req;
}

/**
 * Send a WhatsApp webhook request
 * @param {Object} payload - Webhook payload
 * @param {string} signature - X-MMC-Signature header value
 * @returns {Promise<Object>} Response
 */
async function whatsappWebhook(payload, signature) {
  return await postWebhook('/whatsapp/webhook', payload, {
    'X-MMC-Signature': signature,
    'Content-Type': 'application/json'
  });
}

/**
 * Send a Stripe webhook request
 * @param {Object} payload - Webhook payload
 * @param {string} signature - Stripe-Signature header value
 * @returns {Promise<Object>} Response
 */
async function stripeWebhook(payload, signature) {
  return await postWebhook('/stripe/webhook', payload, {
    'Stripe-Signature': signature,
    'Content-Type': 'application/json'
  });
}

/**
 * Submit a cart via WhatsApp API
 * @param {Object} cartData - Cart data
 * @param {string} sessionToken - WhatsApp session token
 * @returns {Promise<Object>} Response
 */
async function submitCart(cartData, sessionToken) {
  const requestInstance = getRequest();
  return await requestInstance
    .post('/whatsapp/submit-cart')
    .set('X-WhatsApp-Token', sessionToken)
    .set('Content-Type', 'application/json')
    .send(cartData);
}

/**
 * Create a Stripe checkout session
 * @param {Object} checkoutData - Checkout data
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function createStripeCheckout(checkoutData, token = null) {
  const requestInstance = getRequest();
  const req = requestInstance
    .post('/stripe/create-checkout-session')
    .set('Content-Type', 'application/json')
    .send(checkoutData);

  if (token) {
    req.set('Authorization', `Bearer ${token}`);
  }

  return await req;
}

module.exports = {
  initializeApp,
  cleanupApp,
  getRequest,
  graphqlQuery,
  graphqlMutation,
  postWebhook,
  whatsappWebhook,
  stripeWebhook,
  submitCart,
  createStripeCheckout
};
