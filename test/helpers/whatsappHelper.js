/**
 * WhatsApp testing helper utilities
 */

const crypto = require('crypto');
const { faker } = require('@faker-js/faker');

class WhatsAppHelper {
  /**
   * Generate a valid WhatsApp webhook signature
   * @param {string} body - Request body
   * @param {string} secret - Webhook secret
   * @returns {string} Webhook signature
   */
  static generateWebhookSignature(body, secret = 'test-webhook-secret') {
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(body);
    return `sha256=${hmac.digest('hex')}`;
  }

  /**
   * Create a mock WhatsApp webhook payload
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Webhook payload
   */
  static createWebhookPayload(overrides = {}) {
    const defaultPayload = {
      object: 'whatsapp_business_account',
      entry: [
        {
          id: 'entry-123',
          changes: [
            {
              value: {
                messaging_product: 'whatsapp',
                metadata: {
                  display_phone_number: '+**********',
                  phone_number_id: 'phone-123'
                },
                messages: [
                  {
                    from: '+**********',
                    id: `msg-${Date.now()}`,
                    timestamp: Math.floor(Date.now() / 1000).toString(),
                    text: {
                      body: 'Hello'
                    },
                    type: 'text'
                  }
                ]
              },
              field: 'messages'
            }
          ]
        }
      ]
    };

    return this.deepMerge(defaultPayload, overrides);
  }

  /**
   * Create a text message webhook payload
   * @param {string} from - Sender phone number
   * @param {string} text - Message text
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Webhook payload
   */
  static createTextMessagePayload(from, text, overrides = {}) {
    return this.createWebhookPayload({
      entry: [
        {
          changes: [
            {
              value: {
                messages: [
                  {
                    from: from,
                    text: {
                      body: text
                    },
                    type: 'text'
                  }
                ]
              }
            }
          ]
        }
      ],
      ...overrides
    });
  }

  /**
   * Create an interactive message webhook payload
   * @param {string} from - Sender phone number
   * @param {Object} interactive - Interactive message data
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Webhook payload
   */
  static createInteractiveMessagePayload(from, interactive, overrides = {}) {
    return this.createWebhookPayload({
      entry: [
        {
          changes: [
            {
              value: {
                messages: [
                  {
                    from: from,
                    interactive: interactive,
                    type: 'interactive'
                  }
                ]
              }
            }
          ]
        }
      ],
      ...overrides
    });
  }

  /**
   * Create a button reply webhook payload
   * @param {string} from - Sender phone number
   * @param {string} buttonId - Button ID that was clicked
   * @param {string} buttonText - Button text
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Webhook payload
   */
  static createButtonReplyPayload(from, buttonId, buttonText, overrides = {}) {
    return this.createInteractiveMessagePayload(from, {
      type: 'button_reply',
      button_reply: {
        id: buttonId,
        title: buttonText
      }
    }, overrides);
  }

  /**
   * Create a list reply webhook payload
   * @param {string} from - Sender phone number
   * @param {string} listId - List item ID that was selected
   * @param {string} listTitle - List item title
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Webhook payload
   */
  static createListReplyPayload(from, listId, listTitle, overrides = {}) {
    return this.createInteractiveMessagePayload(from, {
      type: 'list_reply',
      list_reply: {
        id: listId,
        title: listTitle
      }
    }, overrides);
  }

  /**
   * Create a status update webhook payload
   * @param {string} messageId - Message ID
   * @param {string} status - Message status (sent, delivered, read, failed)
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Webhook payload
   */
  static createStatusUpdatePayload(messageId, status, overrides = {}) {
    return this.createWebhookPayload({
      entry: [
        {
          changes: [
            {
              value: {
                statuses: [
                  {
                    id: messageId,
                    status: status,
                    timestamp: Math.floor(Date.now() / 1000).toString(),
                    recipient_id: '+**********'
                  }
                ]
              },
              field: 'messages'
            }
          ]
        }
      ],
      ...overrides
    });
  }

  /**
   * Create a mock WhatsApp session
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Session object
   */
  static createMockSession(overrides = {}) {
    const defaultSession = {
      id: `session-${Date.now()}`,
      dialogueId: `dialogue-${Date.now()}`,
      customerPhone: '+**********',
      brandWhatsappId: 'brand-123',
      context: {
        customer: {
          phone: '+**********',
          customerId: null,
          addresses: []
        },
        brandRef: {
          id: 'brand-123',
          name: 'Test Brand'
        },
        selectedRestaurantRef: null,
        currentOrder: null,
        currentOrderState: {},
        isAddressSelected: false,
        orderPlaced: false,
        paymentDone: false,
        completedOrders: [],
        cartReceived: false,
        selectedAddressIndex: null
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return this.deepMerge(defaultSession, overrides);
  }

  /**
   * Create a mock session with restaurant selected
   * @param {string} restaurantId - Restaurant ID
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Session object
   */
  static createSessionWithRestaurant(restaurantId, overrides = {}) {
    return this.createMockSession({
      context: {
        selectedRestaurantRef: {
          id: restaurantId,
          name: 'Test Restaurant'
        }
      },
      ...overrides
    });
  }

  /**
   * Create a mock session with order in progress
   * @param {Object} orderData - Order data
   * @param {Object} overrides - Additional overrides
   * @returns {Object} Session object
   */
  static createSessionWithOrder(orderData, overrides = {}) {
    return this.createMockSession({
      context: {
        currentOrder: orderData,
        cartReceived: true,
        selectedRestaurantRef: {
          id: orderData.restaurantId || 'restaurant-123',
          name: 'Test Restaurant'
        }
      },
      ...overrides
    });
  }

  /**
   * Create mock cart data
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Cart data
   */
  static createMockCartData(overrides = {}) {
    const defaultCartData = {
      restaurantId: 'restaurant-123',
      isPickedUp: false,
      deliveryAddressId: 'address-123',
      orderInput: [
        {
          food: 'food-123',
          variation: 'variation-123',
          quantity: 2,
          addons: [],
          specialInstructions: 'No onions'
        }
      ],
      taxationAmount: 2.08,
      tipping: 3.00
    };

    return this.deepMerge(defaultCartData, overrides);
  }

  /**
   * Generate random phone number
   * @param {string} countryCode - Country code (default: +1)
   * @returns {string} Phone number
   */
  static generatePhoneNumber(countryCode = '+1') {
    const number = faker.phone.number('##########');
    return `${countryCode}${number}`;
  }

  /**
   * Generate random message ID
   * @returns {string} Message ID
   */
  static generateMessageId() {
    return `msg_${Date.now()}_${faker.string.alphanumeric(8)}`;
  }

  /**
   * Generate random dialogue ID
   * @returns {string} Dialogue ID
   */
  static generateDialogueId() {
    return `dlg_${Date.now()}_${faker.string.alphanumeric(8)}`;
  }

  /**
   * Create mock WhatsApp API response
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} API response
   */
  static createMockApiResponse(overrides = {}) {
    const defaultResponse = {
      messaging_product: 'whatsapp',
      contacts: [
        {
          input: '+**********',
          wa_id: '**********'
        }
      ],
      messages: [
        {
          id: this.generateMessageId(),
          message_status: 'accepted'
        }
      ]
    };

    return this.deepMerge(defaultResponse, overrides);
  }

  /**
   * Create mock error response
   * @param {string} errorCode - Error code
   * @param {string} errorMessage - Error message
   * @returns {Object} Error response
   */
  static createMockErrorResponse(errorCode = '400', errorMessage = 'Bad Request') {
    return {
      error: {
        message: errorMessage,
        type: 'OAuthException',
        code: parseInt(errorCode),
        fbtrace_id: faker.string.alphanumeric(16)
      }
    };
  }

  /**
   * Deep merge two objects
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} Merged object
   */
  static deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Validate webhook payload structure
   * @param {Object} payload - Webhook payload
   * @returns {boolean} Is valid
   */
  static isValidWebhookPayload(payload) {
    return (
      payload &&
      payload.object === 'whatsapp_business_account' &&
      Array.isArray(payload.entry) &&
      payload.entry.length > 0 &&
      Array.isArray(payload.entry[0].changes) &&
      payload.entry[0].changes.length > 0
    );
  }

  /**
   * Extract message from webhook payload
   * @param {Object} payload - Webhook payload
   * @returns {Object|null} Message object
   */
  static extractMessageFromPayload(payload) {
    if (!this.isValidWebhookPayload(payload)) {
      return null;
    }

    const change = payload.entry[0].changes[0];
    if (change.value.messages && change.value.messages.length > 0) {
      return change.value.messages[0];
    }

    return null;
  }

  /**
   * Extract status from webhook payload
   * @param {Object} payload - Webhook payload
   * @returns {Object|null} Status object
   */
  static extractStatusFromPayload(payload) {
    if (!this.isValidWebhookPayload(payload)) {
      return null;
    }

    const change = payload.entry[0].changes[0];
    if (change.value.statuses && change.value.statuses.length > 0) {
      return change.value.statuses[0];
    }

    return null;
  }
}

module.exports = WhatsAppHelper;
