/**
 * Test application helper
 * Creates a test instance of the Express app for integration tests
 */

const express = require('express');
const { ApolloServer } = require('apollo-server-express');
const mongoose = require('mongoose');
const typeDefs = require('../../graphql/schema/index');
const isAuthenticated = require('../../middleware/is-auth');
const { processWhatsAppAuth } = require('../../middleware/whatsapp-graphql-auth');
const graphqlTools = require('@graphql-tools/schema');
const logger = require('../../helpers/logger');
const http = require('http');

// Mock Redis queue for testing to avoid connection issues
jest.mock('../../queue/index', () => ({
  notificationsQueue: {
    add: jest.fn(),
    process: jest.fn(),
    close: jest.fn()
  }
}));

// Import resolvers after mocking
const resolvers = require('../../graphql/resolvers/index');

let testAppInstance = null;

/**
 * Create a test application instance
 * @returns {Promise<{app: Express, server: ApolloServer, httpServer: http.Server}>}
 */
async function createTestApp() {
  if (testAppInstance) {
    return testAppInstance;
  }

  const app = express();
  const httpServer = http.createServer(app);

  try {
    // 使用现有的数据库连接，不要重复连接
    if (mongoose.connection.readyState === 0) {
      // 如果没有连接，使用testDatabase helper来连接
      const { connectTestDB } = require('./testDatabase');
      await connectTestDB({ useRealDatabase: true });
      logger.info('Connected to test MongoDB via testDatabase helper');
    } else {
      logger.info('Using existing MongoDB connection');
    }

    // Basic middleware
    app.use(express.json({ type: ['application/json', 'application/vnd.api+json'] }));
    app.use(express.urlencoded({ extended: true }));

    // CORS middleware for tests
    app.use((req, res, next) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-WhatsApp-Token');
      
      if (req.method === 'OPTIONS') {
        return res.sendStatus(200);
      }
      next();
    });

    // Create GraphQL schema
    const schema = graphqlTools.makeExecutableSchema({
      typeDefs,
      resolvers
    });

    // Create Apollo Server
    const server = new ApolloServer({
      schema,
      introspection: true, // Enable introspection for tests
      context: async ({ req, res }) => {
        if (!req) return {};

        // 处理标准JWT认证
        const { isAuth, userId, userType, restaurantId } = isAuthenticated(req);
        req.isAuth = isAuth;
        req.userId = userId;
        req.userType = userType;
        req.restaurantId = restaurantId;

        // 创建基本上下文
        const context = { req, res };

        // 处理WhatsApp认证
        return await processWhatsAppAuth(context);
      },
      formatError: error => {
        logger.error('GraphQL test error:', error);
        return {
          message: error.message,
          path: error.path,
          extensions: error.extensions
        };
      }
    });

    await server.start();

    // Apply GraphQL middleware
    server.applyMiddleware({
      app,
      path: '/graphql',
      cors: {
        origin: '*',
        credentials: false
      }
    });

    // Add WhatsApp routes for integration tests
    try {
      const whatsappRoutes = require('../../whatsapp/routes/index');
      app.use('/whatsapp', whatsappRoutes);
      logger.info('WhatsApp routes loaded successfully');
    } catch (error) {
      logger.warn('WhatsApp routes not available in test environment:', error.message);
    }

    // Add basic API routes for testing
    app.use('/api/orders', (req, res) => {
      res.status(404).json({ success: false, error: 'Order API not implemented in test environment' });
    });

    // Add mock payment routes for testing
    app.use('/stripe', (req, res) => {
      res.status(404).json({ success: false, error: 'Stripe API not implemented in test environment' });
    });

    app.use('/paypal', (req, res) => {
      res.status(404).json({ success: false, error: 'PayPal API not implemented in test environment' });
    });

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });

    // Start server on a random port for tests
    const port = process.env.TEST_PORT || 0; // 0 means random available port
    await new Promise((resolve) => {
      httpServer.listen(port, resolve);
    });

    const actualPort = httpServer.address().port;
    logger.info(`Test server started on port ${actualPort}`);

    testAppInstance = {
      app,
      server,
      httpServer,
      port: actualPort
    };

    return testAppInstance;

  } catch (error) {
    logger.error('Failed to create test app:', error);
    throw error;
  }
}

/**
 * Clean up test application
 * @returns {Promise<void>}
 */
async function cleanupTestApp() {
  if (testAppInstance) {
    try {
      if (testAppInstance.server) {
        await testAppInstance.server.stop();
      }
      if (testAppInstance.httpServer) {
        await new Promise((resolve) => {
          testAppInstance.httpServer.close(resolve);
        });
      }
      // Don't disconnect mongoose here as it might be used by other tests
      // The global teardown will handle mongoose disconnection
      logger.info('Test app cleaned up successfully');
    } catch (error) {
      logger.error('Error cleaning up test app:', error);
    } finally {
      testAppInstance = null;
    }
  }
}

/**
 * Get the current test app instance
 * @returns {Object|null}
 */
function getTestApp() {
  return testAppInstance;
}

module.exports = {
  createTestApp,
  cleanupTestApp,
  getTestApp
};
