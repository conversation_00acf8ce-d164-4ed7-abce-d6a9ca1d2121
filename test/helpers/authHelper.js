/**
 * Authentication helper functions for tests
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../../config');

/**
 * Generate a JWT token for a user
 * @param {Object} user - User object
 * @param {string} [expiresIn='1h'] - Token expiration
 * @returns {string} JWT token
 */
function generateAuthToken(user, expiresIn = '1h') {
  const payload = {
    userId: user._id.toString(),
    email: user.email,
    userType: user.userType || user.role || 'customer'
  };

  return jwt.sign(
    payload,
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn }
  );
}

/**
 * Generate a WhatsApp session token
 * @param {Object} session - Session object
 * @returns {string} Session token
 */
function generateSessionToken(session) {
  const payload = {
    sessionId: session.id,
    dialogueId: session.dialogueId,
    customerPhone: session.customerPhone,
    brandWhatsappId: session.brandWhatsappId
  };
  
  return jwt.sign(
    payload,
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '24h' }
  );
}

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Object} Decoded token payload
 */
function verifyToken(token) {
  return jwt.verify(token, process.env.JWT_SECRET || 'test-secret');
}

/**
 * Generate an X-MMC-Signature for WhatsApp webhook
 * @param {string|Object} payload - Webhook payload
 * @param {string} secret - Webhook secret
 * @returns {string} X-MMC-Signature header value
 */
function generateXMMCSignature(payload, secret) {
  // Convert payload to string if it's an object
  const payloadString = typeof payload === 'string'
    ? payload
    : JSON.stringify(payload);
  
  // Create HMAC signature
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(payloadString);
  const digest = hmac.digest('hex');
  
  // Format signature
  return `v1=${digest}`;
}

/**
 * Generate a Stripe webhook signature
 * @param {string|Object} payload - Webhook payload
 * @param {string} secret - Webhook secret
 * @returns {string} Stripe-Signature header value
 */
function generateStripeSignature(payload, secret) {
  // Convert payload to string if it's an object
  const payloadString = typeof payload === 'string'
    ? payload
    : JSON.stringify(payload);
  
  const timestamp = Math.floor(Date.now() / 1000);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  // Create HMAC signature
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(signedPayload);
  const digest = hmac.digest('hex');
  
  // Format signature
  return `t=${timestamp},v1=${digest}`;
}

module.exports = {
  generateAuthToken,
  generateSessionToken,
  verifyToken,
  generateXMMCSignature,
  generateStripeSignature
};
