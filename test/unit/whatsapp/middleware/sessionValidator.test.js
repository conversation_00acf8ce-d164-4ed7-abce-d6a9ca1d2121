const validateSession = require('../../../../whatsapp/middleware/sessionValidator');
const SessionIdGenerator = require('../../../../whatsapp/utils/sessionIdGenerator');
const sessionService = require('../../../../whatsapp/services/sessionService');
const logger = require('../../../../helpers/logger');

// Mock dependencies
jest.mock('../../../../whatsapp/utils/sessionIdGenerator');
jest.mock('../../../../whatsapp/services/sessionService');
jest.mock('../../../../helpers/logger');

describe('Session Validator Middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request mock
    req = {
      headers: {}
    };

    // Setup response mock
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Setup next function mock
    next = jest.fn();
  });

  test('should pass validation with valid token and session', async () => {
    // Arrange
    const validToken = 'valid-token';
    const validSession = { dialogueId: 'dialogue-123' };
    req.headers['x-whatsappw-token'] = validToken;

    SessionIdGenerator.validateToken.mockReturnValue(true);
    sessionService.getSessionByToken.mockResolvedValue(validSession);

    // Act
    await validateSession(req, res, next);

    // Assert
    expect(SessionIdGenerator.validateToken).toHaveBeenCalledWith(validToken);
    expect(sessionService.getSessionByToken).toHaveBeenCalledWith(validToken);
    expect(req.session).toBe(validSession);
    expect(next).toHaveBeenCalled();
  });

  test('should return 401 when token is missing', async () => {
    // Act
    await validateSession(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Missing or invalid X-WhatsAppW-Token header'
    });
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 401 when token format is invalid', async () => {
    // Arrange
    const invalidToken = 'invalid-token';
    req.headers['x-whatsappw-token'] = invalidToken;
    SessionIdGenerator.validateToken.mockReturnValue(false);

    // Act
    await validateSession(req, res, next);

    // Assert
    expect(SessionIdGenerator.validateToken).toHaveBeenCalledWith(invalidToken);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ error: 'Invalid token format' });
    expect(logger.debug).toHaveBeenCalledWith(`Invalid WhatsApp token format: ${invalidToken}`);
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 404 when session does not exist', async () => {
    // Arrange
    const validToken = 'valid-token';
    req.headers['x-whatsappw-token'] = validToken;
    SessionIdGenerator.validateToken.mockReturnValue(true);
    sessionService.getSessionByToken.mockResolvedValue(null);

    // Act
    await validateSession(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'URL expired. Please send message in dialog to get new URL'
    });
    expect(logger.error).toHaveBeenCalledWith('Invalid session or missing dialogueId', {
      token: validToken
    });
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 404 when session exists but dialogueId is missing', async () => {
    // Arrange
    const validToken = 'valid-token';
    const invalidSession = { dialogueId: null };
    req.headers['x-whatsappw-token'] = validToken;
    SessionIdGenerator.validateToken.mockReturnValue(true);
    sessionService.getSessionByToken.mockResolvedValue(invalidSession);

    // Act
    await validateSession(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'URL expired. Please send message in dialog to get new URL'
    });
    expect(logger.error).toHaveBeenCalledWith('Invalid session or missing dialogueId', {
      token: validToken
    });
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 500 when internal error occurs', async () => {
    // Arrange
    const validToken = 'valid-token';
    const error = new Error('Database connection failed');
    req.headers['x-whatsappw-token'] = validToken;
    SessionIdGenerator.validateToken.mockReturnValue(true);
    sessionService.getSessionByToken.mockRejectedValue(error);

    // Act
    await validateSession(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({ error: 'Internal server error' });
    expect(logger.error).toHaveBeenCalledWith('Session validation error:', error);
    expect(next).not.toHaveBeenCalled();
  });
});
