/**
 * Link Generator Unit Tests
 * 测试链接生成器的核心功能
 */

const linkGenerator = require('../../../../whatsapp/utils/linkGenerator');

describe('LinkGenerator', () => {
  describe('generateMenuLink()', () => {
    test('should generate menu link with token and orderURL', () => {
      const token = 'session-123';
      const orderURL = 'app.firespoon.com';

      const link = linkGenerator.generateMenuLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/m/${token}/`);
    });

    test('should handle different domains', () => {
      const token = 'session-456';
      const orderURL = 'staging.firespoon.com';

      const link = linkGenerator.generateMenuLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/m/${token}/`);
    });

    test('should handle special characters in token', () => {
      const token = 'session-123+special/chars=';
      const orderURL = 'app.firespoon.com';

      const link = linkGenerator.generateMenuLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/m/${token}/`);
    });
  });

  describe('generateAddressLink()', () => {
    test('should generate address link with token and orderURL', () => {
      const token = 'session-123';
      const orderURL = 'app.firespoon.com';

      const link = linkGenerator.generateAddressLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/a/${token}/`);
    });

    test('should handle different domains', () => {
      const token = 'session-456';
      const orderURL = 'staging.firespoon.com';

      const link = linkGenerator.generateAddressLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/a/${token}/`);
    });

    test('should handle special characters in token', () => {
      const token = 'session-123+special/chars=';
      const orderURL = 'app.firespoon.com';

      const link = linkGenerator.generateAddressLink(token, orderURL);

      expect(link).toBe(`https://${orderURL}/a/${token}/`);
    });
  });

});
