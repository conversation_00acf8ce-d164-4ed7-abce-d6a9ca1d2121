/**
 * Session ID Generator Unit Tests
 * 测试会话ID生成器的核心功能
 */

const SessionIdGenerator = require('../../../../whatsapp/utils/sessionIdGenerator');

describe('SessionIdGenerator', () => {
  describe('generateOpaqueToken()', () => {
    test('should generate an opaque token', () => {
      const token = SessionIdGenerator.generateOpaqueToken();

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });

    test('should generate unique tokens', () => {
      const token1 = SessionIdGenerator.generateOpaqueToken();
      const token2 = SessionIdGenerator.generateOpaqueToken();

      expect(token1).not.toBe(token2);
    });

    test('should generate tokens with base64url format', () => {
      const token = SessionIdGenerator.generateOpaqueToken();

      // Base64url format: alphanumeric, hyphens, and underscores, no padding
      expect(token).toMatch(/^[A-Za-z0-9_-]+$/);
    });

    test('should generate tokens of consistent length', () => {
      const tokens = Array.from({ length: 10 }, () =>
        SessionIdGenerator.generateOpaqueToken()
      );

      const lengths = tokens.map(token => token.length);
      const uniqueLengths = [...new Set(lengths)];

      expect(uniqueLengths.length).toBe(1); // All should have same length
    });

    test('should handle errors gracefully', () => {
      // Mock crypto.randomBytes to throw an error
      const crypto = require('crypto');
      const originalRandomBytes = crypto.randomBytes;
      crypto.randomBytes = jest.fn().mockImplementation(() => {
        throw new Error('Crypto error');
      });

      expect(() => {
        SessionIdGenerator.generateOpaqueToken();
      }).toThrow('Failed to generate secure token');

      // Restore original function
      crypto.randomBytes = originalRandomBytes;
    });
  });

  describe('validateToken()', () => {
    test('should validate a valid token', () => {
      const token = SessionIdGenerator.generateOpaqueToken();
      const isValid = SessionIdGenerator.validateToken(token);

      expect(isValid).toBe(true);
    });

    test('should reject null token', () => {
      const isValid = SessionIdGenerator.validateToken(null);

      expect(isValid).toBe(false);
    });

    test('should reject undefined token', () => {
      const isValid = SessionIdGenerator.validateToken(undefined);

      expect(isValid).toBe(false);
    });

    test('should reject non-string token', () => {
      const isValid = SessionIdGenerator.validateToken(123);

      expect(isValid).toBe(false);
    });

    test('should reject empty string token', () => {
      const isValid = SessionIdGenerator.validateToken('');

      expect(isValid).toBe(false);
    });

    test('should reject invalid base64url token', () => {
      const isValid = SessionIdGenerator.validateToken('invalid-token-format!@#');

      expect(isValid).toBe(false);
    });

    test('should reject token with wrong length', () => {
      // Create a token with different length
      const shortToken = 'abc';
      const isValid = SessionIdGenerator.validateToken(shortToken);

      expect(isValid).toBe(false);
    });
  });

  describe('Token Security', () => {
    test('should generate cryptographically secure tokens', () => {
      const tokens = Array.from({ length: 1000 }, () =>
        SessionIdGenerator.generateOpaqueToken()
      );

      // Check that all tokens are unique
      const uniqueTokens = new Set(tokens);
      expect(uniqueTokens.size).toBe(1000);
    });

    test('should generate tokens that are URL-safe', () => {
      const token = SessionIdGenerator.generateOpaqueToken();
      const encoded = encodeURIComponent(token);

      expect(encoded).toBe(token); // Should not need encoding
    });

    test('should generate tokens without padding', () => {
      const token = SessionIdGenerator.generateOpaqueToken();

      // Base64url should not have padding characters
      expect(token).not.toContain('=');
    });
  });
});
