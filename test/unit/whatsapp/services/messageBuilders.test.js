/**
 * Message Builders Unit Tests
 * 测试消息构建器的核心功能
 */

const MessageBuilders = require('../../../../whatsapp/services/messageBuilders');

describe('MessageBuilders', () => {
  let messageBuilders;
  const mockConfig = {
    TEMPLATE_BASIC_TEXT: '2222',
    TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_HEADER_FOOTER: '2885',
    TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_IMAGE: '2893',
    TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_HEADER_FOOTER: '2882',
    TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_IMAGE: '2891',
    TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_HEADER_FOOTER: '2886',
    TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_IMAGE: '2889',
    TEMPLATE_WHATSAPP_0_VARIABLES_NO_IMAGE: '2863',
    TEMPLATE_WHATSAPP_0_VARIABLES_WITH_IMAGE: '2867',
    TEMPLATE_WHATSAPP_2_VARIABLES_NO_IMAGE: '2862',
    TEMPLATE_WHATSAPP_2_VARIABLES_WITH_IMAGE: '2866',
    TEMPLATE_WHATSAPP_3_VARIABLES_NO_IMAGE: '2861',
    TEMPLATE_WHATSAPP_3_VARIABLES_WITH_IMAGE: '2865',
    TEMPLATE_WHATSAPP_4_VARIABLES_NO_IMAGE: '2860',
    TEMPLATE_WHATSAPP_4_VARIABLES_WITH_IMAGE: '2864',
    TEMPLATE_WHATSAPP_FLOW_WITH_IMAGE: '2872',
    TEMPLATE_WHATSAPP_FLOW_FORM: '2873'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    messageBuilders = new MessageBuilders(mockConfig);
  });

  describe('buildBasicTextMessageData()', () => {
    test('should build basic text message for notification', () => {
      const recipientId = '+1234567890';
      const text = 'Hello, World!';
      const messageType = 'notification';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text, messageType);

      expect(result).toBeDefined();
      expect(result.type).toBe('notification-messages');
      expect(result.attributes.context.var_text).toBe(text);
      expect(result.relationships.recipient.data.id).toBe(recipientId);
    });

    test('should build basic text message for dialog', () => {
      const dialogueId = 'dialogue-123';
      const text = 'Hello from dialogue!';
      const messageType = 'dialog';

      const result = messageBuilders.buildBasicTextMessageData(dialogueId, text, messageType);

      expect(result).toBeDefined();
      expect(result.data.type).toBe('messages');
      expect(result.data.attributes.context.var_text).toBe(text);
      expect(result.data.relationships.dialogue.data.id).toBe(dialogueId);
    });

    test('should default to notification message type', () => {
      const recipientId = '+1234567890';
      const text = 'Hello, World!';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text);

      expect(result.type).toBe('notification-messages');
    });

    test('should handle empty text message', () => {
      const recipientId = '+1234567890';
      const text = '';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text);

      expect(result.attributes.context.var_text).toBe('');
    });

    test('should handle special characters in text', () => {
      const recipientId = '+1234567890';
      const text = 'Hello! 🎉 Special chars: @#$%^&*()';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text);

      expect(result.attributes.context.var_text).toBe(text);
    });
  });

  describe('buildQuickReplyMessageData()', () => {
    test('should build quick reply message with buttons', () => {
      const recipientId = 'dialogue-123';
      const options = {
        text: 'Choose an option:',
        buttons: [
          { text: 'Option 1', payload: 'opt1' },
          { text: 'Option 2', payload: 'opt2' }
        ]
      };

      const result = messageBuilders.buildQuickReplyMessageData(recipientId, options);

      expect(result).toBeDefined();
      expect(result.data.type).toBe('messages');
      expect(result.data.attributes.context.var_text).toBe(options.text);
      expect(result.data.attributes.context.qr1).toBe('Option 1');
      expect(result.data.attributes.context.qr1_payload).toBe('opt1');
      expect(result.data.attributes.context.qr2).toBe('Option 2');
      expect(result.data.attributes.context.qr2_payload).toBe('opt2');
    });

    test('should handle single button', () => {
      const recipientId = 'dialogue-123';
      const options = {
        text: 'Confirm action:',
        buttons: [
          { text: 'Confirm', payload: 'confirm' }
        ]
      };

      const result = messageBuilders.buildQuickReplyMessageData(recipientId, options);

      expect(result.data.attributes.context.qr1).toBe('Confirm');
      expect(result.data.attributes.context.qr1_payload).toBe('confirm');
    });

    test('should handle header and footer', () => {
      const recipientId = 'dialogue-123';
      const options = {
        text: 'Choose an option:',
        header: 'Important Decision',
        footer: 'Choose wisely',
        buttons: [
          { text: 'Yes', payload: 'yes' },
          { text: 'No', payload: 'no' }
        ]
      };

      const result = messageBuilders.buildQuickReplyMessageData(recipientId, options);

      expect(result.data.attributes.context.var_header).toBe('Important Decision');
      expect(result.data.attributes.context.var_footer).toBe('Choose wisely');
    });

    test('should throw error for invalid button count', () => {
      const recipientId = 'dialogue-123';
      const options = {
        text: 'Too many options:',
        buttons: [
          { text: 'Option 1', payload: 'opt1' },
          { text: 'Option 2', payload: 'opt2' },
          { text: 'Option 3', payload: 'opt3' },
          { text: 'Option 4', payload: 'opt4' }
        ]
      };

      expect(() => {
        messageBuilders.buildQuickReplyMessageData(recipientId, options);
      }).toThrow('Invalid number of quick replies. Must be 1, 2, or 3.');
    });
  });

  describe('buildWhatsAppTemplateMessageData()', () => {
    test('should build template message with variables', () => {
      const recipientId = '+1234567890';
      const templateName = 'welcome_template';
      const options = {
        variables: {
          var_name: 'John Doe',
          var_restaurant: 'Test Restaurant'
        }
      };

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId,
        templateName,
        options
      );

      expect(result).toBeDefined();
      expect(result.type).toBe('notification-messages');
      expect(result.attributes.context.var_template).toBe(templateName);
      expect(result.attributes.context.var_name).toBe('John Doe');
      expect(result.attributes.context.var_restaurant).toBe('Test Restaurant');
    });

    test('should handle template with image', () => {
      const recipientId = '+1234567890';
      const templateName = 'promo_template';
      const options = {
        image: 'https://example.com/image.jpg',
        variables: {
          var_discount: '20%'
        }
      };

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId,
        templateName,
        options
      );

      expect(result.attributes.context.image).toBe('https://example.com/image.jpg');
      expect(result.attributes.context.var_discount).toBe('20%');
    });

    test('should handle empty options', () => {
      const recipientId = '+1234567890';
      const templateName = 'simple_template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId,
        templateName
      );

      expect(result.attributes.context.var_template).toBe(templateName);
    });
  });

  describe('buildDialogueMessageData()', () => {
    test('should build dialogue text message', () => {
      const dialogueId = 'dialogue-123';
      const text = 'Hello from dialogue!';

      const result = messageBuilders.buildDialogueMessageData(dialogueId, text);

      expect(result).toBeDefined();
      expect(result.data.type).toBe('messages');
      expect(result.data.attributes.channelType).toBe('WHATSAPP');
      expect(result.data.attributes.content[0].attrs.text).toBe(text);
      expect(result.data.relationships.dialogue.data.id).toBe(dialogueId);
    });

    test('should handle empty text', () => {
      const dialogueId = 'dialogue-123';
      const text = '';

      const result = messageBuilders.buildDialogueMessageData(dialogueId, text);

      expect(result.data.attributes.content[0].attrs.text).toBe('');
    });
  });

  describe('Message Structure Validation', () => {
    test('should create valid notification message structure', () => {
      const result = messageBuilders.buildBasicTextMessageData('+1234567890', 'test');

      expect(result.type).toBe('notification-messages');
      expect(result.attributes).toBeDefined();
      expect(result.relationships).toBeDefined();
      expect(result.relationships.recipient).toBeDefined();
      expect(result.relationships.channelGroup).toBeDefined();
      expect(result.relationships.templates).toBeDefined();
    });

    test('should create valid dialogue message structure', () => {
      const result = messageBuilders.buildBasicTextMessageData('dialogue-123', 'test', 'dialog');

      expect(result.data.type).toBe('messages');
      expect(result.data.attributes).toBeDefined();
      expect(result.data.relationships).toBeDefined();
      expect(result.data.relationships.dialogue).toBeDefined();
      expect(result.data.relationships.templates).toBeDefined();
    });
  });
});
