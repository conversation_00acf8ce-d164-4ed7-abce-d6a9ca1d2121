/**
 * Restaurant Store Unit Tests
 * 测试餐厅数据存储服务的核心功能
 */

describe('RestaurantStore', () => {
  let restaurantStore;
  beforeEach(() => {
    // Clear module cache to get fresh instance
    jest.resetModules();

    // Re-require the service to get a fresh instance
    restaurantStore = require('../../../../whatsapp/services/restaurantStore');
  });

  describe('Service Initialization', () => {
    test('should export a service instance', () => {
      expect(restaurantStore).toBeDefined();
      expect(typeof restaurantStore).toBe('object');
    });

    test('should have required methods', () => {
      expect(typeof restaurantStore.initialize).toBe('function');
      expect(typeof restaurantStore.getBrandRef).toBe('function');
      expect(typeof restaurantStore.getBrandRefById).toBe('function');
      expect(typeof restaurantStore.getRestaurantRef).toBe('function');
      expect(typeof restaurantStore.getRestaurantsByBrand).toBe('function');
    });

    test('should have data storage maps', () => {
      expect(restaurantStore.brands).toBeDefined();
      expect(restaurantStore.restaurants).toBeDefined();
      expect(restaurantStore.brandsByWhatsapp).toBeDefined();
      expect(restaurantStore.restaurantsByBrand).toBeDefined();
      expect(restaurantStore.brands instanceof Map).toBe(true);
      expect(restaurantStore.restaurants instanceof Map).toBe(true);
    });
  });

  describe('Basic Functionality', () => {
    test('should be able to call initialize method', async () => {
      expect(typeof restaurantStore.initialize).toBe('function');

      // Test that method exists and can be called
      const mockDataFetcher = jest.fn().mockResolvedValue({
        restaurants: [],
        brands: []
      });

      try {
        await restaurantStore.initialize(mockDataFetcher);
        expect(mockDataFetcher).toHaveBeenCalled();
      } catch (error) {
        // It's okay if it fails due to file system operations, we just want to test the method exists
        expect(error).toBeDefined();
      }
    });

    test('should be able to call getBrandRef method', () => {
      expect(typeof restaurantStore.getBrandRef).toBe('function');

      // Test that method exists and can be called
      const result = restaurantStore.getBrandRef('test-whatsapp-id');
      expect(result).toBeUndefined(); // Should return undefined for non-existent brand
    });

    test('should be able to call getRestaurantRef method', () => {
      expect(typeof restaurantStore.getRestaurantRef).toBe('function');

      // Test that method exists and can be called
      const result = restaurantStore.getRestaurantRef('test-restaurant-id');
      expect(result).toBeUndefined(); // Should return undefined for non-existent restaurant
    });

    test('should be able to call getRestaurantsByBrand method', () => {
      expect(typeof restaurantStore.getRestaurantsByBrand).toBe('function');

      // Test that method exists and can be called
      const result = restaurantStore.getRestaurantsByBrand('test-brand-id');
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(0); // Should return empty array for non-existent brand
    });
  });

  describe('Data Storage', () => {
    test('should have proper data structure', () => {
      expect(restaurantStore.brands instanceof Map).toBe(true);
      expect(restaurantStore.restaurants instanceof Map).toBe(true);
      expect(restaurantStore.brandsByWhatsapp instanceof Map).toBe(true);
      expect(restaurantStore.restaurantsByBrand instanceof Map).toBe(true);
    });

    test('should handle empty data gracefully', () => {
      // Test that empty maps work correctly
      expect(restaurantStore.brands.size).toBe(0);
      expect(restaurantStore.restaurants.size).toBe(0);
      expect(restaurantStore.brandsByWhatsapp.size).toBe(0);
      expect(restaurantStore.restaurantsByBrand.size).toBe(0);
    });
  });
});
