const { sendWelcomeMessage, sendPaymentLink } = require('../../../whatsapp/machines/orderFsmActions');
const whatsappService = require('../../../whatsapp/services/whatsappService');
const logger = require('../../../helpers/logger');

// Mock dependencies
describe('Order FSM Actions', () => {
  let mockSession;
  let mockContext;
  let mockBrandRef;
  let mockCustomer;
  let mockRestaurant;
  let mockOrder;

  beforeEach(() => {
    // Mock logger
    jest.spyOn(logger, 'error').mockImplementation(() => {});
    jest.spyOn(logger, 'info').mockImplementation(() => {});
    jest.spyOn(logger, 'warn').mockImplementation(() => {});

    // Mock whatsappService
    whatsappService.sendDialogueText = jest.fn();
    whatsappService.sendQuickReply = jest.fn();

    // Mock data
    mockSession = {
      id: 'test-session-id',
      dialogueId: 'test-dialog-id',
      brandRef: {
        name: 'Test Brand',
        welcomeMessage: 'Welcome to our brand!',
        restaurants: [{
          _id: 'test-restaurant-id',
          name: 'Test Restaurant',
          address: '123 Test St',
          phone: '+1234567890',
          openingHours: '9 AM - 9 PM'
        }]
      }
    };

    mockContext = {
      customer: {
        orderHistory: [{
          orderId: '123',
          total: 50
        }]
      },
      isRestaurantSelected: true,
      selectedRestaurantRef: mockSession.brandRef.restaurants[0]
    };

    mockBrandRef = mockSession.brandRef;
    mockCustomer = mockContext.customer;
    mockRestaurant = mockSession.brandRef.restaurants[0];
    mockOrder = {
      food: 'test-food-id',
      variation: 'test-variation-id',
      addons: [{
        _id: 'test-addon-id',
        options: ['test-option-id']
      }]
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendWelcomeMessage', () => {
    it('should send welcome message with restaurant info and buttons', async () => {
      await sendWelcomeMessage(mockSession, mockContext);
      
      expect(whatsappService.sendQuickReply).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          text: expect.stringContaining(
            'Welcome to our brand!\n\n' +
            'Current restaurant: Test Restaurant\n' +
            '📍 *Address*: 123 Test St\n' +
            '📞 *Phone*: +1234567890\n' +
            '⏰ *Hours*: 9 AM - 9 PM\n' +
            'Follow the link below to view menu and order:\n' +
            '<https://undefined/m/test-session-id/>'
          ),
          header: 'Welcome to Test Brand',
          footer: '     ',
          buttons: expect.arrayContaining([
            expect.objectContaining({
              text: 'Order History',
              payload: 'order_history'
            })
          ])
        }),
        'dialog'
      );
    });

    it('should handle missing restaurant selection', async () => {
      mockContext.isRestaurantSelected = false;
      await sendWelcomeMessage(mockSession, mockContext);
      
      expect(whatsappService.sendQuickReply).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          text: expect.stringContaining('Please select a restaurant')
        }),
        'dialog'
      );
    });

    it('should throw error with invalid context', async () => {
      await expect(sendWelcomeMessage(mockSession, null)).rejects.toThrow('updatedContext is required for sendWelcomeMessage');
    });
  });

  describe('sendPaymentLink', () => {
    it('should successfully send payment link', async () => {
      // Mock successful response from whatsappService
      whatsappService.sendDialogueText.mockResolvedValue(true);

      const result = await sendPaymentLink(mockContext, 'https://test-payment-link.com', '123');
      
      expect(result).toBe(true);
      expect(whatsappService.sendDialogueText).toHaveBeenCalledWith(
        mockContext.dialogueId,
        expect.stringContaining('Your order #123 is ready for payment! Please use the link below to complete your payment:')
      );
      expect(logger.info).toHaveBeenCalledWith('Payment link sent successfully', {
        dialogueId: mockContext.dialogueId,
        orderId: '123'
      });
    });

    it('should handle missing parameters', async () => {
      const result = await sendPaymentLink({}, null, '123');
      
      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalled();
    });
  });


});
