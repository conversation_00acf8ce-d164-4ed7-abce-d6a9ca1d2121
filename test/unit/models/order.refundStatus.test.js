/**
 * Order Model refundStatus 单元测试
 * 
 * 验证refundStatus字段的默认值和枚举值
 */

const mongoose = require('mongoose');
const Order = require('../../../models/order');
const { ORDER_STATUS } = require('../../../helpers/enum');

describe('Order Model - refundStatus Field Tests', () => {
  beforeAll(async () => {
    // 使用全局测试数据库连接
    if (mongoose.connection.readyState === 0) {
      // 如果没有连接，使用全局测试URI
      const mongoUri = global.__MONGO_URI__ || 'mongodb://localhost:27017/test_order_refund_status';
      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      console.log('Connected to test database for refundStatus tests');
    } else {
      console.log('Using existing mongoose connection');
    }
  });

  afterAll(async () => {
    // 只清理数据，不断开连接（可能被其他测试使用）
    if (mongoose.connection.readyState === 1) {
      try {
        await Order.deleteMany({});
      } catch (error) {
        console.log('Cleanup warning:', error.message);
      }
    }
  });

  beforeEach(async () => {
    // 清理测试数据
    if (mongoose.connection.readyState === 1) {
      try {
        await Order.deleteMany({});
      } catch (error) {
        // 忽略清理错误
        console.log('Cleanup warning:', error.message);
      }
    }
  });

  test('should have refundStatus default value as NONE', async () => {
    console.log('📝 Testing refundStatus default value...');
    
    // 创建最简单的订单对象，不设置refundStatus
    const orderData = {
      orderId: `ORDER-UNIT-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: '507f1f77bcf86cd799439011', // 使用有效的ObjectId字符串
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012', // 使用有效的ObjectId字符串
      customerId: 'CUST-UNIT-001',
      customerPhone: '+1234567890',
      items: [{
        _id: '507f1f77bcf86cd799439013', // 使用有效的ObjectId字符串
        title: 'Test Item',
        food: 'test-food-id',
        description: 'Test food item',
        quantity: 1,
        variation: {
          _id: '507f1f77bcf86cd799439014', // 使用有效的ObjectId字符串
          title: 'Regular',
          price: 100.00,
          discounted: 0
        },
        addons: [],
        isActive: true
      }],
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'addr-unit-001',
      deliveryCoordinates: {
        type: 'Point',
        coordinates: [-74.006, 40.7128]
      },
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00,
      deliveryCharges: 5.00,
      tipping: 10.00,
      taxationAmount: 8.50,
      isPickedUp: false,
      paymentId: 'pi_unit_test_123'
      // 注意：不设置refundStatus, refunds, totalRefunded，让它们使用默认值
    };
    
    const order = new Order(orderData);
    
    // 验证默认值（保存前）
    expect(order.refundStatus).toBe('NONE');
    expect(order.totalRefunded).toBe(0);
    expect(order.refunds).toEqual([]);
    
    // 保存并验证
    const savedOrder = await order.save();
    expect(savedOrder.refundStatus).toBe('NONE');
    expect(savedOrder.totalRefunded).toBe(0);
    expect(savedOrder.refunds).toEqual([]);
    
    console.log('✅ refundStatus default value test passed');
    console.log('Order refundStatus:', savedOrder.refundStatus);
  });

  test('should allow valid refundStatus enum values', async () => {
    console.log('📝 Testing refundStatus enum values...');
    
    const orderData = {
      orderId: `ORDER-ENUM-${Date.now()}`,
      orderAmount: 50.00,
      restaurantId: '507f1f77bcf86cd799439015', // 使用有效的ObjectId字符串
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439016', // 使用有效的ObjectId字符串
      customerId: 'CUST-ENUM-001',
      customerPhone: '+1234567890',
      items: [{
        _id: '507f1f77bcf86cd799439017', // 使用有效的ObjectId字符串
        title: 'Test Item',
        food: 'test-food-id',
        description: 'Test food item',
        quantity: 1,
        variation: {
          _id: '507f1f77bcf86cd799439018', // 使用有效的ObjectId字符串
          title: 'Regular',
          price: 50.00,
          discounted: 0
        },
        addons: [],
        isActive: true
      }],
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'addr-enum-001',
      deliveryCoordinates: {
        type: 'Point',
        coordinates: [-74.006, 40.7128]
      },
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 50.00,
      deliveryCharges: 5.00,
      tipping: 5.00,
      taxationAmount: 4.25,
      isPickedUp: false,
      paymentId: 'pi_enum_test_456'
    };
    
    const order = new Order(orderData);
    const savedOrder = await order.save();
    
    // 测试默认值
    expect(savedOrder.refundStatus).toBe('NONE');
    
    // 测试可以设置的值
    savedOrder.refundStatus = 'PARTIAL';
    await savedOrder.save();
    expect(savedOrder.refundStatus).toBe('PARTIAL');
    
    savedOrder.refundStatus = 'FULL';
    await savedOrder.save();
    expect(savedOrder.refundStatus).toBe('FULL');
    
    // 重置为NONE
    savedOrder.refundStatus = 'NONE';
    await savedOrder.save();
    expect(savedOrder.refundStatus).toBe('NONE');
    
    console.log('✅ Refund status enum test passed');
  });

  test('should reject invalid refundStatus values', async () => {
    console.log('📝 Testing invalid refundStatus values...');
    
    const orderData = {
      orderId: `ORDER-INVALID-${Date.now()}`,
      orderAmount: 25.00,
      restaurantId: '507f1f77bcf86cd799439019', // 使用有效的ObjectId字符串
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439020', // 使用有效的ObjectId字符串
      customerId: 'CUST-INVALID-001',
      customerPhone: '+1234567890',
      items: [{
        _id: '507f1f77bcf86cd799439021', // 使用有效的ObjectId字符串
        title: 'Test Item',
        food: 'test-food-id',
        description: 'Test food item',
        quantity: 1,
        variation: {
          _id: '507f1f77bcf86cd799439022', // 使用有效的ObjectId字符串
          title: 'Regular',
          price: 25.00,
          discounted: 0
        },
        addons: [],
        isActive: true
      }],
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'addr-invalid-001',
      deliveryCoordinates: {
        type: 'Point',
        coordinates: [-74.006, 40.7128]
      },
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 25.00,
      deliveryCharges: 5.00,
      tipping: 2.50,
      taxationAmount: 2.13,
      isPickedUp: false,
      paymentId: 'pi_invalid_test_789',
      refundStatus: 'INVALID_STATUS' // 无效的状态
    };
    
    const order = new Order(orderData);
    
    // 应该抛出验证错误
    await expect(order.save()).rejects.toThrow();
    
    console.log('✅ Invalid refundStatus rejection test passed');
  });

  test('should verify refundStatus field definition in schema', () => {
    console.log('📝 Testing refundStatus schema definition...');
    
    const orderSchema = Order.schema;
    const refundStatusPath = orderSchema.paths.refundStatus;
    
    // 验证字段存在
    expect(refundStatusPath).toBeDefined();
    
    // 验证是枚举类型
    expect(refundStatusPath.enumValues).toContain('NONE');
    expect(refundStatusPath.enumValues).toContain('PARTIAL');
    expect(refundStatusPath.enumValues).toContain('FULL');
    
    // 验证默认值
    expect(refundStatusPath.defaultValue).toBe('NONE');
    
    console.log('✅ refundStatus schema definition test passed');
    console.log('Enum values:', refundStatusPath.enumValues);
    console.log('Default value:', refundStatusPath.defaultValue);
  });
});
