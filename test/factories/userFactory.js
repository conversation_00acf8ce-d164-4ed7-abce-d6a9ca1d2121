/**
 * User factory for generating test data
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import User model
const User = require('../../models/user');

class UserFactory {
  /**
   * Build a user object without saving to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} User object
   */
  static build(overrides = {}) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();

    return {
      name: `${firstName} ${lastName}`,
      email: faker.internet.email({ firstName, lastName }).toLowerCase(),
      password: bcrypt.hashSync('password123', 10),
      phone: faker.phone.number(),
      userType: 'customer',
      isActive: true,
      emailIsVerified: false,
      phoneIsVerified: false,
      isOrderNotification: false,
      isOfferNotification: false,
      notifications: [],
      addresses: [],
      favourite: [],
      _testData: true, // Mark as test data for easy cleanup
      ...overrides
    };
  }
  
  /**
   * Create a user and save to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created user
   */
  static async create(overrides = {}) {
    // Ensure mongoose is connected before creating
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 0) {
      // Try to connect if not already connected
      const { connectTestDB } = require('../helpers/dbHelper');
      await connectTestDB();
    }

    const userData = this.build(overrides);
    return await User.create(userData);
  }
  
  /**
   * Create multiple users
   * @param {number} count - Number of users to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created users
   */
  static async createMany(count, overrides = {}) {
    const users = [];
    
    for (let i = 0; i < count; i++) {
      users.push(this.build(overrides));
    }
    
    return await User.insertMany(users);
  }
  
  /**
   * Create a customer user
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created user
   */
  static async createCustomer(overrides = {}) {
    return await this.create({ userType: 'customer', ...overrides });
  }

  /**
   * Create a restaurant user
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created user
   */
  static async createRestaurant(overrides = {}) {
    return await this.create({ userType: 'restaurant', ...overrides });
  }

  /**
   * Create a rider user
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created user
   */
  static async createRider(overrides = {}) {
    return await this.create({ userType: 'rider', ...overrides });
  }

  /**
   * Create an admin user
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created user
   */
  static async createAdmin(overrides = {}) {
    return await this.create({ userType: 'admin', ...overrides });
  }
}

module.exports = UserFactory;
