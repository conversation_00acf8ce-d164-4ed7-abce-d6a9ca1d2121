import os
import json
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

# Load configuration from config.json
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logging.error(f"Error loading config file: {e}")
        return {}

# Set up logging
def setup_logging(log_level='INFO'):
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('import_restaurant.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# Load config and setup logging
CONFIG = load_config()
logger = setup_logging(CONFIG.get('log_level', 'INFO'))

def get_restaurant_id_from_file(file_path: str) -> Optional[str]:
    """Extract restaurant_id from the input JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return str(data.get('_id'))
    except Exception as e:
        logger.error(f"Error extracting restaurant_id from file: {e}")
        return None

def get_headers(admin_token: str) -> Dict[str, str]:
    """Generate headers with authentication."""
    return {
        'Content-Type': 'application/json',
        'Authorization': f"Bearer {admin_token}"
    }

@dataclass
class OptionInput:
    _id: str
    title: str
    description: Optional[str]
    price: float

@dataclass
class AddonInput:
    _id: str
    title: str
    description: Optional[str]
    options: List[str]
    quantityMinimum: int
    quantityMaximum: int

@dataclass
class VariationInput:
    _id: str
    title: str
    price: float
    discounted: Optional[float]
    addons: List[str]

@dataclass
class FoodInput:
    _id: str
    title: str
    description: str
    image: Optional[str]
    variations: List[VariationInput]
    is_active: bool = True

@dataclass
class CategoryInput:
    _id: str
    title: str
    foods: List[FoodInput]

class RestaurantImporter:
    def __init__(self, config: Dict, input_file: str = None):
        self.config = config
        self.graphql_endpoint = config.get('graphql_endpoint', 'http://localhost:4000/graphql')
        self.admin_token = config.get('admin_token', 'your_admin_token_here')
        self.headers = get_headers(self.admin_token)
        
        # Try to get restaurant_id from input file first, then from config
        self.restaurant_id = None
        if input_file:
            self.restaurant_id = get_restaurant_id_from_file(input_file)
        
        if not self.restaurant_id:
            self.restaurant_id = config.get('restaurant_id')
            if self.restaurant_id:
                logger.warning(f"Using restaurant_id from config: {self.restaurant_id}")
            else:
                raise ValueError("No restaurant_id found in input file or config")
        
        
        # Store the full restaurant data after fetching
        self.restaurant_data = None
        
        logger.info(f"Initialized importer for restaurant_id: {self.restaurant_id}")
    
    def fetch_restaurant_data(self) -> bool:
        """Fetch all restaurant data including options, addons, categories, and foods"""
        query = """
        query GetRestaurantData($restaurantId: String!) {
            restaurant(id: $restaurantId) {
                _id
                name
                options {
                    _id
                    title
                    description
                    price
                }
                addons {
                    _id
                    title
                    description
                    options
                    quantityMinimum
                    quantityMaximum
                }
                categories {
                    _id
                    title
                    foods {
                        _id
                        title
                        description
                        image
                        isActive
                        variations {
                            _id
                            title
                            price
                            discounted
                            addons
                        }
                    }
                }
            }
        }
        """
        
        result = self.execute_graphql(
            query,
            variables={'restaurantId': self.restaurant_id}
        )
        
        if not result or not result['restaurant']:
            logger.error("Failed to fetch restaurant data",result)
            return False
            
        self.restaurant_data = result['restaurant']
        
        
        logger.info(f"Fetched restaurant data - Options: {len(result['restaurant']['options'])}, "
                    f"Addons: {len(result['restaurant']['addons'])}, Foods: {len(result['restaurant']['categories'])}")
        return True

    def execute_graphql(self, query: str, variables: Dict = None) -> Dict:
        """Execute a GraphQL query/mutation"""
        payload = {'query': query}
        if variables:
            payload['variables'] = variables

        try:
            response = requests.post(
                self.graphql_endpoint,
                json=payload,
                headers=self.headers
            )
            response.raise_for_status()
            result = response.json()
            
            if 'errors' in result:
                logger.error(f"GraphQL Error: {result['errors']}")
                return None
                
            return result.get('data')
            
        except Exception as e:
            logger.error(f"Error executing GraphQL: {str(e)}")
            return None

    def create_options(self, options: List[Dict]) -> bool:
        """Create options in bulk"""
        if not options:
            return True

        # 记录原始options的数量
        original_count = len(options)
        
        # Prepare options data without IDs
        option_inputs = []
        for opt in options:
            option_inputs.append({
                'title': opt['title'],
                'description': opt.get('description', ''),
                'price': float(opt['price']) if opt['price'] is not None else 0.0
            })

        mutation = """
        mutation CreateOptions($restaurant: String!, $options: [OptionInput!]!) {
            createOptions(optionInput: { restaurant: $restaurant, options: $options }) {
                options {
                    _id
                    title
                    description
                    price
                }
            }
        }
        """
        
        result = self.execute_graphql(
            mutation,
            variables={
                'restaurant': self.restaurant_id,
                'options': option_inputs
            }
        )
        
        if not result or 'createOptions' not in result:
            logger.error("Failed to create options: No result returned")
            return False
            
        created_options = result['createOptions'].get('options', [])
        if len(created_options) != original_count:
            logger.error(f"Failed to create all options: Expected {original_count}, but got {len(created_options)}")
            return False
            
        # 验证所有选项都被正确创建
        for opt, created_opt in zip(options, created_options):
            if (opt['title'].lower().strip() != created_opt['title'].lower().strip() or
                abs(float(opt.get('price', 0)) - float(created_opt.get('price', 0))) > 0.01):
                logger.error(f"Option verification failed: {opt['title']} != {created_opt['title']}")
                return False
        
        # 更新 restaurant_data 中的 options
        if not hasattr(self, 'restaurant_data'):
            self.restaurant_data = {}
        self.restaurant_data['options'] = created_options
                
        logger.info(f"Successfully created and verified {len(created_options)} options")
        return True

    def create_addons(self, addons: List[Dict], all_options: List[Dict]) -> bool:
        """
        创建addons
        :param addons: 要创建的addon列表
        :param all_options: 所有options的原始数据，用于ID映射
        :return: 是否成功
        """
        # 1. 获取服务器上的所有options
        if not self.fetch_restaurant_data():
            logger.error("Failed to fetch restaurant data for addon creation")
            return False
            
        server_options = self.restaurant_data.get('options', [])
        if not server_options:
            logger.error("No options found on server. Create options first.")
            return False
            
        # 2. 创建option ID映射 (old_id -> new_id)
        option_map = {}
        unmapped_options = []
        
        for opt in all_options:
            mapped = False
            for server_opt in server_options:
                if (opt.get('title', '').lower().strip() == server_opt['title'].lower().strip() and
                    (opt.get('description') or '').lower().strip() == (server_opt.get('description') or '').lower().strip() and
                    abs(float(opt.get('price', 0)) - float(server_opt.get('price', 0))) < 0.01):
                    option_map[opt['_id']] = server_opt['_id']
                    mapped = True
                    logger.debug(f"Mapped option: {opt['title']} ({opt['_id']}) -> {server_opt['_id']}")
                    break
                    
            if not mapped:
                unmapped_options.append(f"{opt['_id']} - {opt.get('title')}")
                
        if unmapped_options:
            logger.error(f"Failed to map {len(unmapped_options)} options: {', '.join(unmapped_options)}")
            return False
        
        # 3. 准备要创建的addons
        addon_inputs = []
        self.addon_mapping = {}  # 重置映射关系
        
        for addon in addons:
            # 映射option IDs
            mapped_option_ids = []
            missing_options = []
            
            for opt_id in addon.get('options', []):
                if opt_id in option_map:
                    mapped_option_ids.append(option_map[opt_id])
                else:
                    missing_options.append(opt_id)
            
            if missing_options:
                logger.error(f"Missing options for addon {addon.get('title')}: {missing_options}")
                return False
            
            # 创建addon的唯一标识符
            addon_key = (
                addon['title'].lower().strip(),
                (addon.get('description') or '').lower().strip(),
                tuple(sorted(mapped_option_ids)),  # 使用映射后的 option IDs
                addon.get('quantity_minimum'),  # 添加数量最小值
                addon.get('quantity_maximum')  # 添加数量最大值
            )
            
            addon_inputs.append({
                'title': addon['title'],
                'description': addon.get('description', ''),
                'options': mapped_option_ids,
                'quantityMinimum': addon.get('quantity_minimum'),
                'quantityMaximum': addon.get('quantity_maximum')
            })
            
            # 存储映射关系，等创建完成后填充new_addon_id
            self.addon_mapping[str(addon['_id'])] = {
                'key': addon_key,
                'new_id': None
            }
            
            logger.debug(f"Prepared addon: {addon['title']} with {len(mapped_option_ids)} options")
        
        if not addon_inputs:
            logger.error("No valid addons to create after option mapping")
            return False

        # 4. 创建addons
        mutation = """
        mutation CreateAddons($restaurant: String!, $addons: [createAddonInput!]!) {
            createAddons(addonInput: { restaurant: $restaurant, addons: $addons }) {
                addons {
                    _id
                    title
                    description
                    options
                    quantityMinimum
                    quantityMaximum
                }
            }
        }
        """
        
        result = self.execute_graphql(
            mutation,
            variables={
                'restaurant': self.restaurant_id,
                'addons': addon_inputs
            }
        )
        
        if result and 'createAddons' in result:
            # 获取新创建的addons
            created_addons = result['createAddons'].get('addons', [])
            logger.info(f"Created/Updated {len(created_addons)} addons")
            
            # 将新创建的addons保存到restaurant_data中
            if not hasattr(self, 'restaurant_data'):
                self.restaurant_data = {}
            self.restaurant_data['addons'] = created_addons
            
            # 更新addon映射关系
            for addon in created_addons:
                # Debug the addon结构
                logger.debug(f"Processing server addon: {addon.get('title')}")
                
                # Get option IDs from the addon
                option_ids = tuple(sorted(addon.get('options', [])))  # options 字段直接包含 option IDs
                
                # Create a key for matching
                addon_key = (
                    addon.get('title', '').lower().strip(),
                    (addon.get('description') or '').lower().strip(),
                    option_ids,
                    addon.get('quantityMinimum'),  # 添加数量最小值
                    addon.get('quantityMaximum')  # 添加数量最大值
                )
                
                # 找到对应的原始addon ID并更新映射
                for orig_id, mapping in self.addon_mapping.items():
                    if mapping['key'] == addon_key:
                        mapping['new_id'] = addon.get('_id')
                        logger.debug(f"Mapped addon {orig_id} -> {addon.get('_id')}: {addon.get('title', '')}")
                        break
            
            return True
            
        logger.error(f"Failed to create/update addons: {result}")
        return False

    def create_category(self, category: Dict) -> str:
        """Create a single category"""
        mutation = """
        mutation CreateCategory($category: CategoryInput!) {
            createCategory(category: $category) {
                _id
                name
            }
        }
        """
        
        category_input = {
            'title': category['title'],
            'restaurant': self.restaurant_id
        }
        
        result = self.execute_graphql(
            mutation,
            variables={'category': category_input}
        )
        
        if result and 'createCategory' in result:
            logger.info(f"Created/Updated category: {category['title']}")
            return result['createCategory']['_id']
        logger.error(f"Failed to create/update category: {result}")
        return None

    def create_food(self, food: Dict, category_id: str, all_addons: List[Dict] = None) -> str:
        """
        Create a single food item
        :param food: Food data to create
        :param category_id: ID of the category this food belongs to
        :param all_addons: List of all addons data for ID mapping
        :return: ID of the created food or None if failed
        """
        if not hasattr(self, 'restaurant_data') or 'addons' not in self.restaurant_data:
            logger.error("No restaurant data available for food creation")
            return None

        # 创建 addon 映射表 (old_addon_id -> new_addon_id)
        addon_map = {}
        missing_mappings = []

        if all_addons:
            if not hasattr(self, 'addon_mapping'):
                logger.error("Addon mapping not found. Make sure to run create_addons first.")
                return None

            # 使用 create_addons 中创建的映射关系
            for addon in all_addons:
                addon_id = str(addon['_id'])
                if addon_id in self.addon_mapping and self.addon_mapping[addon_id]['new_id']:
                    addon_map[addon_id] = self.addon_mapping[addon_id]['new_id']
                    logger.debug(f"Using mapped addon {addon_id} -> {addon_map[addon_id]}")
                else:
                    missing_mappings.append(f"{addon_id} - {addon.get('title')}")

            # 如果有未映射的 addon，直接报错并终止
            if missing_mappings:
                logger.error(f"Missing addon mappings for: {', '.join(missing_mappings)}")
                logger.error("Some addons failed to map. Please check if all addons were properly created.")
                return None

        # 准备 variations 数据，映射 addon IDs
        variations = []
        for var in food.get('variations', []):
            mapped_addons = []
            for addon_id in var.get('addons', []):
                if str(addon_id) in addon_map:
                    mapped_addons.append(addon_map[str(addon_id)])
                else:
                    logger.error(f"Missing addon mapping for ID {addon_id} in variation {var.get('title')}")
                    return None

            variations.append({
                'title': var['title'],
                'price': float(var['price']),
                'discounted': float(var['discounted']) if var.get('discounted') is not None else None,
                'addons': mapped_addons
            })

        # 准备 food 输入数据
        food_input = {
            'restaurant': self.restaurant_id,
            'category': category_id,
            'title': food['title'],
            'description': food.get('description', ''),
            'image': food.get('image'),
            'variations': variations
        }

        # 定义 GraphQL mutation
        food_mutation = """
        mutation CreateFood($foodInput: FoodInput!) {
            createFood(foodInput: $foodInput) {
                _id
                name
            }
        }
        """

        result = self.execute_graphql(
            food_mutation,
            variables={'foodInput': food_input}
        )

        if result and 'data' in result and 'createFood' in result['data']:
            logger.info(f"Created/Updated food: {food['title']}")
            return result['data']['createFood']['_id']
        return None

    def import_restaurant_data(self, data: Dict):
        """Main method to import restaurant data"""
        logger.info("Starting restaurant data import...")
        
        # 1. Import options
        if 'options' in data and data['options']:
            self.create_options(data['options'])
        
        # 2. Import addons
        if 'addons' in data and data['addons']:
            # Pass both addons and options to create_addons
            all_options = data.get('options', [])
            self.create_addons(data['addons'], all_options)
        
        # 3. Import categories and foods
        if 'categories' in data and data['categories']:
            # 确保有addons数据
            if 'addons' not in data:
                logger.error("No addons data provided in input")
                return
                
            # 获取所有addon数据
            all_addons = data['addons']
            
            for category_data in data['categories']:
                # 创建category
                restaurant_idre = self.create_category(category_data)
                if not restaurant_idre:
                    logger.error(f"Failed to create category: {category_data['title']}")
                    continue
                
                # 获取最新的服务器数据（每个category获取一次）
                if not self.fetch_restaurant_data():
                    logger.error("Failed to fetch updated restaurant data after creating category")
                    continue
                for server_category in self.restaurant_data['categories']:
                    if server_category.get('title', '').lower().strip() == category_data['title'].lower().strip():
                        category_id = server_category['_id']
                        break # Found it, no need to continue looping

                if category_id:
                    logger.info(f"Found existing category '{category_data['title']}' with ID: {category_id}")
                else:
                    logger.info(f"Category '{category_data['title']}' not found in existing restaurant data. Will create a new one.")                     
                # 导入该category下的foods
                for food_data in category_data.get('foods', []):
                    self.create_food(food_data, category_id, all_addons)
        
        logger.info("Restaurant data import completed!")

def load_restaurant_data(file_path: str) -> Dict:
    """Load restaurant data from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading restaurant data: {str(e)}")
        raise

def main():
    import argparse
    
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Import restaurant data to GraphQL API')
    parser.add_argument('input_file', help='Path to the input JSON file')
    parser.add_argument('--restaurant-id', help='Override restaurant ID (optional)')
    args = parser.parse_args()
    
    # Load configuration from config file
    config = CONFIG.copy()
    
    # Override config with command line arguments
    if args.restaurant_id:
        config['restaurant_id'] = args.restaurant_id
    
    try:
        # Load restaurant data
        restaurant_data = load_restaurant_data(args.input_file)
        
        # Initialize importer with config and input file
        importer = RestaurantImporter(config, input_file=args.input_file)
        
        # Start import process
        importer.import_restaurant_data(restaurant_data)
        
        # 导入完成后获取最新数据并保存
        if importer.fetch_restaurant_data():
            # 生成输出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_filename = f"restaurant_{importer.restaurant_id}_{timestamp}.json"
            output_path = os.path.join("output", output_filename)
            
            # 确保输出目录存在
            os.makedirs("output", exist_ok=True)
            
            # 保存最新数据到文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(importer.restaurant_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Latest restaurant data saved to: {output_path}")
        
        logger.info("Import process completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Error during import: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
