/**
 * Food factory for generating test data
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

// Import Food model
const Food = require('../../models/food');

class FoodFactory {
  /**
   * Build a food object without saving to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Food object
   */
  static build(overrides = {}) {
    const basePrice = faker.number.float({ min: 5, max: 50, precision: 0.01 });
    
    return {
      title: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      image: faker.image.url({ width: 400, height: 300, category: 'food' }),
      price: basePrice,
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Regular',
          price: basePrice,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Large',
          price: basePrice + 3.00,
          discounted: 0
        }
      ],
      addons: [],
      options: [],
      restaurant: new mongoose.Types.ObjectId(),
      category: new mongoose.Types.ObjectId(),
      isActive: true,
      isAvailable: true,
      preparationTime: faker.number.int({ min: 10, max: 30 }),
      tags: faker.helpers.arrayElements(['spicy', 'vegetarian', 'gluten-free', 'popular'], 2),
      nutritionInfo: {
        calories: faker.number.int({ min: 200, max: 800 }),
        protein: faker.number.int({ min: 5, max: 30 }),
        carbs: faker.number.int({ min: 10, max: 60 }),
        fat: faker.number.int({ min: 5, max: 25 })
      },
      _testData: true, // Mark as test data for easy cleanup
      ...overrides
    };
  }

  /**
   * Create a food item and save to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async create(overrides = {}) {
    // Ensure mongoose is connected before creating
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 0) {
      const { connectTestDB } = require('../helpers/dbHelper');
      await connectTestDB();
    }

    const foodData = this.build(overrides);
    return await Food.create(foodData);
  }

  /**
   * Create multiple food items
   * @param {number} count - Number of food items to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created food items
   */
  static async createMany(count, overrides = {}) {
    const foods = [];
    
    for (let i = 0; i < count; i++) {
      foods.push(this.build(overrides));
    }
    
    return await Food.insertMany(foods);
  }

  /**
   * Create a pizza
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createPizza(overrides = {}) {
    return await this.create({
      title: `${faker.helpers.arrayElement(['Margherita', 'Pepperoni', 'Hawaiian', 'Veggie'])} Pizza`,
      description: 'Delicious pizza with fresh ingredients',
      tags: ['popular', 'italian'],
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Small (10")',
          price: 12.99,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Medium (12")',
          price: 16.99,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Large (14")',
          price: 20.99,
          discounted: 0
        }
      ],
      preparationTime: 20,
      ...overrides
    });
  }

  /**
   * Create a burger
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createBurger(overrides = {}) {
    return await this.create({
      title: `${faker.helpers.arrayElement(['Classic', 'Cheese', 'BBQ', 'Chicken'])} Burger`,
      description: 'Juicy burger with fresh toppings',
      tags: ['popular', 'american'],
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Regular',
          price: 9.99,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Double',
          price: 13.99,
          discounted: 0
        }
      ],
      preparationTime: 15,
      ...overrides
    });
  }

  /**
   * Create a salad
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createSalad(overrides = {}) {
    return await this.create({
      title: `${faker.helpers.arrayElement(['Caesar', 'Greek', 'Garden', 'Cobb'])} Salad`,
      description: 'Fresh and healthy salad',
      tags: ['healthy', 'vegetarian'],
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Regular',
          price: 8.99,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Large',
          price: 11.99,
          discounted: 0
        }
      ],
      preparationTime: 10,
      nutritionInfo: {
        calories: 250,
        protein: 15,
        carbs: 20,
        fat: 8
      },
      ...overrides
    });
  }

  /**
   * Create a beverage
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createBeverage(overrides = {}) {
    return await this.create({
      title: faker.helpers.arrayElement(['Coca Cola', 'Pepsi', 'Orange Juice', 'Water', 'Coffee']),
      description: 'Refreshing beverage',
      tags: ['beverage'],
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Small',
          price: 1.99,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Medium',
          price: 2.49,
          discounted: 0
        },
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Large',
          price: 2.99,
          discounted: 0
        }
      ],
      preparationTime: 2,
      ...overrides
    });
  }

  /**
   * Create a food item with addons
   * @param {Array} addons - Array of addon IDs
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createWithAddons(addons, overrides = {}) {
    return await this.create({
      addons: addons,
      ...overrides
    });
  }

  /**
   * Create a food item with options
   * @param {Array} options - Array of option IDs
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createWithOptions(options, overrides = {}) {
    return await this.create({
      options: options,
      ...overrides
    });
  }

  /**
   * Create a discounted food item
   * @param {number} discountPercent - Discount percentage (0-100)
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createDiscounted(discountPercent, overrides = {}) {
    const basePrice = faker.number.float({ min: 10, max: 30, precision: 0.01 });
    const discountAmount = (basePrice * discountPercent) / 100;

    return await this.create({
      price: basePrice,
      variations: [
        {
          _id: new mongoose.Types.ObjectId(),
          title: 'Regular',
          price: basePrice,
          discounted: discountAmount
        }
      ],
      tags: ['on-sale', 'popular'],
      ...overrides
    });
  }

  /**
   * Create an unavailable food item
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createUnavailable(overrides = {}) {
    return await this.create({
      isAvailable: false,
      ...overrides
    });
  }

  /**
   * Create an inactive food item
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created food item
   */
  static async createInactive(overrides = {}) {
    return await this.create({
      isActive: false,
      ...overrides
    });
  }

  /**
   * Create food items for a specific restaurant
   * @param {string} restaurantId - Restaurant ID
   * @param {number} count - Number of food items to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created food items
   */
  static async createForRestaurant(restaurantId, count = 5, overrides = {}) {
    const foods = [];
    
    for (let i = 0; i < count; i++) {
      foods.push(this.build({
        restaurant: restaurantId,
        ...overrides
      }));
    }
    
    return await Food.insertMany(foods);
  }

  /**
   * Create food items for a specific category
   * @param {string} categoryId - Category ID
   * @param {number} count - Number of food items to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created food items
   */
  static async createForCategory(categoryId, count = 3, overrides = {}) {
    const foods = [];
    
    for (let i = 0; i < count; i++) {
      foods.push(this.build({
        category: categoryId,
        ...overrides
      }));
    }
    
    return await Food.insertMany(foods);
  }
}

module.exports = FoodFactory;
