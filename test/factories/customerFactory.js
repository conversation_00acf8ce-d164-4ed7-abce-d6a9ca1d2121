/**
 * Customer Factory
 * 为测试创建客户相关的测试数据
 */

const mongoose = require('mongoose');
const Customer = require('../../models/customer');
const Address = require('../../models/address');

/**
 * 创建测试客户
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的客户对象
 */
const createCustomer = async (overrides = {}) => {
  const customerId = new mongoose.Types.ObjectId();
  const defaultCustomer = {
    customerId: customerId,
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+1234567890',
    isActive: true,
    addresses: [],
    totalOrders: 0,
    totalAmount: 0,
    orderHistory: [],
    brandOrderCount: 0,
    hasMore: false,
    customerTag: []
  };

  const customerData = { ...defaultCustomer, ...overrides };

  try {
    const customer = new Customer(customerData);
    await customer.save();
    return customer;
  } catch (error) {
    console.error('Error creating test customer:', error);
    throw error;
  }
};

/**
 * 创建测试客户地址
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的地址对象
 */
const createCustomerAddress = async (overrides = {}) => {
  const defaultAddress = {
    label: 'Home',
    deliveryAddress: '123 Test Street, Test City, TC 12345',
    details: 'Apartment 4B, Ring doorbell',
    location: {
      type: 'Point',
      coordinates: [-73.935242, 40.730610] // NYC coordinates (longitude, latitude)
    },
    selected: false,
    isActive: true
  };

  const addressData = { ...defaultAddress, ...overrides };

  try {
    const address = new Address(addressData);
    await address.save();
    return address;
  } catch (error) {
    console.error('Error creating test customer address:', error);
    throw error;
  }
};

/**
 * 创建带有地址的完整客户
 * @param {Object} customerOverrides - 客户属性覆盖
 * @param {Array} addressesData - 地址数据数组
 * @returns {Promise<Object>} 包含客户和地址的对象
 */
const createCustomerWithAddresses = async (customerOverrides = {}, addressesData = []) => {
  try {
    // 如果没有提供地址数据，创建默认地址
    if (addressesData.length === 0) {
      addressesData = [
        {
          addressId: new mongoose.Types.ObjectId(),
          placeId: 'test_place_1',
          formattedAddress: '123 Test Street, Test City, TC 12345',
          streetAddress: '123 Test Street',
          city: 'Test City',
          state: 'TC',
          postcode: '12345',
          country: 'Test Country',
          coordinates: {
            longitude: -73.935242,
            latitude: 40.730610
          },
          recipientName: 'Test Customer',
          phone: '+1234567890',
          isDefault: true,
          tag: 'Home'
        }
      ];
    }

    // 确保每个地址都有必需的字段
    const processedAddresses = addressesData.map(addr => ({
      addressId: addr.addressId || new mongoose.Types.ObjectId(),
      placeId: addr.placeId || 'test_place',
      formattedAddress: addr.formattedAddress || addr.deliveryAddress || '123 Test Street',
      streetAddress: addr.streetAddress || addr.deliveryAddress || '123 Test Street',
      city: addr.city || 'Test City',
      state: addr.state || 'TC',
      postcode: addr.postcode || '12345',
      country: addr.country || 'Test Country',
      coordinates: addr.coordinates || {
        longitude: -73.935242,
        latitude: 40.730610
      },
      recipientName: addr.recipientName || customerOverrides.name || 'Test Customer',
      phone: addr.phone || customerOverrides.phone || '+1234567890',
      isDefault: addr.isDefault || false,
      tag: addr.tag || addr.label || 'Home'
    }));

    // 创建客户时包含地址
    const customerData = {
      ...customerOverrides,
      addresses: processedAddresses
    };

    const customer = await createCustomer(customerData);

    return {
      customer,
      addresses: customer.addresses
    };
  } catch (error) {
    console.error('Error creating customer with addresses:', error);
    throw error;
  }
};

/**
 * 创建已验证的客户
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的已验证客户对象
 */
const createVerifiedCustomer = async (overrides = {}) => {
  const verifiedOverrides = {
    phoneIsVerified: true,
    emailIsVerified: true,
    ...overrides
  };

  return await createCustomer(verifiedOverrides);
};

/**
 * 创建多个测试客户
 * @param {number} count - 要创建的客户数量
 * @param {Object} baseOverrides - 基础覆盖属性
 * @returns {Promise<Array>} 创建的客户数组
 */
const createMultipleCustomers = async (count = 3, baseOverrides = {}) => {
  const customers = [];
  
  for (let i = 0; i < count; i++) {
    const customer = await createCustomer({
      name: `Test Customer ${i + 1}`,
      email: `test.customer${i + 1}@example.com`,
      phone: `+123456789${i}`,
      ...baseOverrides
    });
    customers.push(customer);
  }
  
  return customers;
};

/**
 * 创建客户订单历史数据
 * @param {string} customerId - 客户ID
 * @param {number} orderCount - 订单数量
 * @returns {Promise<Array>} 创建的订单数组
 */
const createCustomerOrderHistory = async (customerId, orderCount = 5) => {
  const { createOrder } = require('./orderFactory');
  const { createRestaurant } = require('./restaurantFactory');
  
  const orders = [];
  const restaurant = await createRestaurant();
  
  for (let i = 0; i < orderCount; i++) {
    const order = await createOrder({
      user: customerId,
      restaurant: restaurant._id,
      orderStatus: i % 2 === 0 ? 'DELIVERED' : 'PENDING',
      orderAmount: Math.floor(Math.random() * 50) + 10,
      createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)) // i days ago
    });
    orders.push(order);
  }
  
  return orders;
};

/**
 * 创建客户偏好设置
 * @param {string} customerId - 客户ID
 * @param {Object} preferences - 偏好设置
 * @returns {Promise<Object>} 创建的偏好设置对象
 */
const createCustomerPreferences = async (customerId, preferences = {}) => {
  const defaultPreferences = {
    customer: customerId,
    notifications: {
      email: true,
      sms: true,
      push: true
    },
    dietary: {
      vegetarian: false,
      vegan: false,
      glutenFree: false,
      halal: false
    },
    delivery: {
      defaultTip: 15,
      preferredTime: 'ASAP',
      specialInstructions: ''
    },
    ...preferences
  };

  // Note: This assumes a CustomerPreferences model exists
  // If not, this would need to be adjusted based on actual schema
  return defaultPreferences;
};

/**
 * 创建客户支付方式
 * @param {string} customerId - 客户ID
 * @param {Object} paymentData - 支付方式数据
 * @returns {Promise<Object>} 创建的支付方式对象
 */
const createCustomerPaymentMethod = async (customerId, paymentData = {}) => {
  const defaultPaymentMethod = {
    customer: customerId,
    type: 'CARD',
    cardLast4: '1234',
    cardBrand: 'VISA',
    isDefault: true,
    isActive: true,
    ...paymentData
  };

  // Note: This assumes a CustomerPaymentMethod model exists
  // If not, this would need to be adjusted based on actual schema
  return defaultPaymentMethod;
};

/**
 * 清理测试客户数据
 * @param {Array} customerIds - 要清理的客户ID数组
 */
const cleanupCustomers = async (customerIds = []) => {
  try {
    if (customerIds.length > 0) {
      await Customer.deleteMany({ _id: { $in: customerIds } });
      await Address.deleteMany({ customer: { $in: customerIds } });
    } else {
      // 清理所有测试客户（基于邮箱模式）
      await Customer.deleteMany({ email: /test\.customer.*@example\.com/ });
      await Address.deleteMany({});
    }
  } catch (error) {
    console.error('Error cleaning up test customers:', error);
    throw error;
  }
};

/**
 * 生成随机客户数据
 * @returns {Object} 随机客户数据
 */
const generateRandomCustomerData = () => {
  const randomId = Math.floor(Math.random() * 10000);
  return {
    name: `Random Customer ${randomId}`,
    email: `random.customer${randomId}@example.com`,
    phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    picture: `https://example.com/avatar${randomId}.jpg`
  };
};

module.exports = {
  createCustomer,
  createCustomerAddress,
  createCustomerWithAddresses,
  createVerifiedCustomer,
  createMultipleCustomers,
  createCustomerOrderHistory,
  createCustomerPreferences,
  createCustomerPaymentMethod,
  cleanupCustomers,
  generateRandomCustomerData
};
