/**
 * Order factory for generating test data
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

// Import Order model
const Order = require('../../models/order');

class OrderFactory {
  /**
   * Build an order object without saving to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Order object
   */
  static build(overrides = {}) {
    const orderAmount = faker.number.float({ min: 10, max: 100, precision: 0.01 });
    const deliveryCharges = faker.number.float({ min: 2, max: 8, precision: 0.01 });
    const tipping = faker.number.float({ min: 0, max: 10, precision: 0.01 });
    const taxationAmount = orderAmount * 0.08; // 8% tax

    return {
      orderId: faker.string.alphanumeric(10).toUpperCase(),
      restaurant: new mongoose.Types.ObjectId(),
      user: new mongoose.Types.ObjectId(),
      deliveryAddress: {
        label: faker.location.streetAddress(),
        deliveryAddress: faker.location.streetAddress(),
        details: faker.location.secondaryAddress(),
        location: {
          type: 'Point',
          coordinates: [
            faker.location.longitude(),
            faker.location.latitude()
          ]
        }
      },
      orderInput: [
        {
          food: new mongoose.Types.ObjectId(),
          variation: new mongoose.Types.ObjectId(),
          quantity: faker.number.int({ min: 1, max: 3 }),
          addons: [],
          specialInstructions: faker.lorem.sentence()
        }
      ],
      orderAmount: orderAmount,
      deliveryCharges: deliveryCharges,
      tipping: tipping,
      taxationAmount: taxationAmount,
      orderStatus: 'PENDING',
      paymentStatus: 'PENDING',
      paymentMethod: faker.helpers.arrayElement(['STRIPE', 'PAYPAL', 'CASH']),
      isPickedUp: faker.datatype.boolean(),
      createdAt: faker.date.recent(),
      acceptedAt: null,
      preparationTime: faker.number.int({ min: 15, max: 45 }),
      rider: null,
      deliveryTime: null,
      completionTime: null,
      reason: '',
      _testData: true, // Mark as test data for easy cleanup
      ...overrides
    };
  }

  /**
   * Create an order and save to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async create(overrides = {}) {
    // Ensure mongoose is connected before creating
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 0) {
      const { connectTestDB } = require('../helpers/dbHelper');
      await connectTestDB();
    }

    const orderData = this.build(overrides);
    return await Order.create(orderData);
  }

  /**
   * Create multiple orders
   * @param {number} count - Number of orders to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created orders
   */
  static async createMany(count, overrides = {}) {
    const orders = [];
    
    for (let i = 0; i < count; i++) {
      orders.push(this.build(overrides));
    }
    
    return await Order.insertMany(orders);
  }

  /**
   * Create a pending order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createPending(overrides = {}) {
    return await this.create({ 
      orderStatus: 'PENDING',
      paymentStatus: 'PENDING',
      ...overrides 
    });
  }

  /**
   * Create an accepted order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createAccepted(overrides = {}) {
    return await this.create({ 
      orderStatus: 'ACCEPTED',
      paymentStatus: 'PAID',
      acceptedAt: new Date(),
      ...overrides 
    });
  }

  /**
   * Create a preparing order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createPreparing(overrides = {}) {
    return await this.create({ 
      orderStatus: 'PREPARING',
      paymentStatus: 'PAID',
      acceptedAt: new Date(),
      ...overrides 
    });
  }

  /**
   * Create a ready order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createReady(overrides = {}) {
    return await this.create({ 
      orderStatus: 'READY',
      paymentStatus: 'PAID',
      acceptedAt: new Date(),
      ...overrides 
    });
  }

  /**
   * Create a delivered order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createDelivered(overrides = {}) {
    return await this.create({ 
      orderStatus: 'DELIVERED',
      paymentStatus: 'PAID',
      acceptedAt: new Date(),
      deliveryTime: new Date(),
      completionTime: new Date(),
      ...overrides 
    });
  }

  /**
   * Create a cancelled order
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createCancelled(overrides = {}) {
    return await this.create({ 
      orderStatus: 'CANCELLED',
      paymentStatus: 'REFUNDED',
      reason: 'Customer requested cancellation',
      ...overrides 
    });
  }

  /**
   * Create an order with specific restaurant and user
   * @param {string} restaurantId - Restaurant ID
   * @param {string} userId - User ID
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createForRestaurantAndUser(restaurantId, userId, overrides = {}) {
    return await this.create({
      restaurant: restaurantId,
      user: userId,
      ...overrides
    });
  }

  /**
   * Create an order with multiple items
   * @param {Array} items - Array of order items
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createWithItems(items, overrides = {}) {
    const orderAmount = items.reduce((total, item) => {
      return total + (item.price || faker.number.float({ min: 5, max: 25, precision: 0.01 })) * item.quantity;
    }, 0);

    return await this.create({
      orderInput: items.map(item => ({
        food: item.food || new mongoose.Types.ObjectId(),
        variation: item.variation || new mongoose.Types.ObjectId(),
        quantity: item.quantity || 1,
        addons: item.addons || [],
        specialInstructions: item.specialInstructions || ''
      })),
      orderAmount: orderAmount,
      taxationAmount: orderAmount * 0.08,
      ...overrides
    });
  }

  /**
   * Create an order with delivery address
   * @param {Object} address - Delivery address object
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createWithAddress(address, overrides = {}) {
    return await this.create({
      deliveryAddress: {
        label: address.label || faker.location.streetAddress(),
        deliveryAddress: address.deliveryAddress || faker.location.streetAddress(),
        details: address.details || faker.location.secondaryAddress(),
        location: address.location || {
          type: 'Point',
          coordinates: [
            faker.location.longitude(),
            faker.location.latitude()
          ]
        }
      },
      ...overrides
    });
  }

  /**
   * Create an order with specific payment method
   * @param {string} paymentMethod - Payment method (STRIPE, PAYPAL, CASH)
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createWithPaymentMethod(paymentMethod, overrides = {}) {
    return await this.create({
      paymentMethod: paymentMethod,
      paymentStatus: paymentMethod === 'CASH' ? 'PENDING' : 'PAID',
      ...overrides
    });
  }

  /**
   * Create an order for pickup
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createForPickup(overrides = {}) {
    return await this.create({
      isPickedUp: true,
      deliveryCharges: 0,
      deliveryAddress: null,
      ...overrides
    });
  }

  /**
   * Create an order with rider assigned
   * @param {string} riderId - Rider ID
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created order
   */
  static async createWithRider(riderId, overrides = {}) {
    return await this.create({
      rider: riderId,
      orderStatus: 'ON_THE_WAY',
      paymentStatus: 'PAID',
      acceptedAt: new Date(),
      ...overrides
    });
  }
}

module.exports = OrderFactory;
