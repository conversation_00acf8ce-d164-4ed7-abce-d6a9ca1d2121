{"data": {"type": "webhook_event", "id": "event-987654321", "attributes": {"type": "messages.received"}}, "included": [{"type": "messages", "id": "message-987654321", "attributes": {"content": [{"displayType": "interactive", "attrs": {"type": "button_reply", "title": "Restaurant Selection", "payload": "restaurant_1"}, "payload": {"externalID": "ext-987654321"}}]}}, {"type": "dialogues", "id": "dialogue-123456789", "attributes": {"status": "ACTIVE"}, "relationships": {"recipient": {"data": {"id": "+1234567890", "type": "customers"}}, "agent": {"data": {"id": "brand-123456789", "type": "agents"}}}}]}