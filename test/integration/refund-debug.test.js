/**
 * 简化的退款测试 - 用于调试
 */

const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const Order = require('../../models/order');
const Refund = require('../../models/refund');
const Restaurant = require('../../models/restaurant');
const Customer = require('../../models/customer');
const Brand = require('../../models/brand');
const Owner = require('../../models/owner');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../helpers/enum');

describe('Refund Debug Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Starting debug test setup...');
    await connectTestDB();
    console.log('✅ Database connected');
  });

  afterAll(async () => {
    await disconnectTestDB();
    console.log('🧹 Debug test cleanup completed');
  });

  beforeEach(async () => {
    await clearTestDB();
    console.log('🧹 Database cleared');
  });

  test('should create basic test data', async () => {
    console.log('📝 Creating test data...');
    
    // 创建Owner
    const owner = new Owner({
      email: '<EMAIL>',
      password: 'testpassword123',
      userType: 'ADMIN',
      restaurants: []
    });
    await owner.save();
    console.log('✅ Owner created:', owner._id);

    // 创建Brand
    const brand = new Brand({
      name: 'Test Brand',
      email: '<EMAIL>',
      phone: '+1234567890',
      orderURL: 'https://test.com/order',
      paymentURL: 'https://test.com/payment',
      ownerId: owner._id,
      restaurants: []
    });
    await brand.save();
    console.log('✅ Brand created:', brand._id);

    // 创建Restaurant
    const restaurant = new Restaurant({
      name: 'Test Restaurant',
      address: 'Test Address',
      phone: '+1234567890',
      commissionRate: 15,
      deliveryTime: 30,
      isAvailable: true,
      restaurantBrand: brand.name,
      restaurantBrandId: brand._id
    });
    await restaurant.save();
    console.log('✅ Restaurant created:', restaurant._id);

    // 创建Customer
    const customer = new Customer({
      customerId: new require('mongoose').Types.ObjectId(),
      phone: '+1987654321',
      email: '<EMAIL>',
      name: 'Test Customer',
      orderHistory: []
    });
    await customer.save();
    console.log('✅ Customer created:', customer._id);

    // 创建Order
    const order = new Order({
      orderId: `ORDER-DEBUG-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: restaurant._id.toString(),
      restaurantName: restaurant.name,
      restaurantBrand: brand.name,
      restaurantBrandId: brand._id,
      customerId: customer.customerId.toString(),
      customerPhone: customer.phone,
      items: [{
        _id: new require('mongoose').Types.ObjectId(),
        title: 'Test Item',
        quantity: 2,
        variation: { price: 50.00 }
      }],
      deliveryAddress: 'Test Delivery Address',
      deliveryAddressId: `ADDR-DEBUG-${Date.now()}`,
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00,
      paymentId: `pi_test_debug_${Date.now()}`,
      refunds: [],
      totalRefunded: 0,
      refundStatus: 'NONE'
    });
    await order.save();
    console.log('✅ Order created:', order._id);

    // 验证数据
    expect(owner._id).toBeTruthy();
    expect(brand._id).toBeTruthy();
    expect(restaurant._id).toBeTruthy();
    expect(customer._id).toBeTruthy();
    expect(order._id).toBeTruthy();
    expect(order.refundStatus).toBe('NONE');

    console.log('🎉 All test data created successfully!');
  });

  test('should verify refundStatus default value', async () => {
    console.log('📝 Testing refundStatus default value...');
    
    // 创建最小化的测试数据
    const owner = new Owner({
      email: '<EMAIL>',
      password: 'testpassword123',
      userType: 'ADMIN'
    });
    await owner.save();

    const brand = new Brand({
      name: 'Test Brand 2',
      email: '<EMAIL>',
      phone: '+1234567891',
      orderURL: 'https://test2.com/order',
      paymentURL: 'https://test2.com/payment',
      ownerId: owner._id
    });
    await brand.save();

    const restaurant = new Restaurant({
      name: 'Test Restaurant 2',
      restaurantBrand: brand.name,
      restaurantBrandId: brand._id
    });
    await restaurant.save();

    const customer = new Customer({
      customerId: new require('mongoose').Types.ObjectId(),
      phone: '+1987654322'
    });
    await customer.save();

    const order = new Order({
      orderId: `ORDER-REFUND-${Date.now()}`,
      orderAmount: 50.00,
      restaurantId: restaurant._id.toString(),
      restaurantName: restaurant.name,
      restaurantBrand: brand.name,
      restaurantBrandId: brand._id,
      customerId: customer.customerId.toString(),
      deliveryAddress: 'Test Address',
      deliveryAddressId: `ADDR-${Date.now()}`,
      orderStatus: ORDER_STATUS.PENDING
    });
    await order.save();

    console.log('Order refundStatus:', order.refundStatus);
    expect(order.refundStatus).toBe('NONE');
    
    console.log('✅ refundStatus default value test passed!');
  });
});
