/**
 * 最简单的调试测试 - 不使用testcontainers
 */

const mongoose = require('mongoose');
const Order = require('../../models/order');
const { ORDER_STATUS } = require('../../helpers/enum');

describe('Simple Debug Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Starting simple debug test...');
    
    // 直接连接到本地MongoDB，不使用testcontainers
    try {
      await mongoose.connect('mongodb://localhost:27017/firespoon_test_simple', {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      console.log('✅ Connected to local MongoDB');
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  });

  afterAll(async () => {
    try {
      await mongoose.disconnect();
      console.log('🔌 Disconnected from MongoDB');
    } catch (error) {
      console.error('❌ Disconnect error:', error);
    }
  });

  beforeEach(async () => {
    try {
      // 清理测试数据
      await Order.deleteMany({});
      console.log('🧹 Cleared test data');
    } catch (error) {
      console.error('❌ Clear data error:', error);
    }
  });

  test('should verify refundStatus default value', async () => {
    console.log('📝 Testing refundStatus default value...');
    
    try {
      const order = new Order({
        orderId: `SIMPLE-TEST-${Date.now()}`,
        orderAmount: 100.00,
        restaurantId: '507f1f77bcf86cd799439011',
        restaurantName: 'Test Restaurant',
        restaurantBrand: 'Test Brand',
        restaurantBrandId: '507f1f77bcf86cd799439012',
        customerId: '507f1f77bcf86cd799439013',
        deliveryAddress: 'Test Address',
        deliveryAddressId: `ADDR-${Date.now()}`,
        orderStatus: ORDER_STATUS.PENDING,
        paymentMethod: 'STRIPE',
        paymentStatus: 'PAID'
      });
      
      console.log('Order before save - refundStatus:', order.refundStatus);
      
      await order.save();
      
      console.log('✅ Order saved successfully');
      console.log('Order after save - refundStatus:', order.refundStatus);
      
      expect(order.refundStatus).toBe('NONE');
      expect(order.totalRefunded).toBe(0);
      
      console.log('✅ Simple debug test passed!');
    } catch (error) {
      console.error('❌ Test error:', error);
      throw error;
    }
  });

  test('should test refundStatus enum values', async () => {
    console.log('📝 Testing refundStatus enum values...');
    
    try {
      const order = new Order({
        orderId: `ENUM-TEST-${Date.now()}`,
        orderAmount: 50.00,
        restaurantId: '507f1f77bcf86cd799439011',
        restaurantName: 'Test Restaurant',
        restaurantBrand: 'Test Brand',
        restaurantBrandId: '507f1f77bcf86cd799439012',
        customerId: '507f1f77bcf86cd799439013',
        deliveryAddress: 'Test Address',
        deliveryAddressId: `ADDR-${Date.now()}`,
        orderStatus: ORDER_STATUS.PENDING
      });
      
      await order.save();
      
      // 测试NONE
      expect(order.refundStatus).toBe('NONE');
      
      // 测试PARTIAL
      order.refundStatus = 'PARTIAL';
      await order.save();
      expect(order.refundStatus).toBe('PARTIAL');
      
      // 测试FULL
      order.refundStatus = 'FULL';
      await order.save();
      expect(order.refundStatus).toBe('FULL');
      
      console.log('✅ Enum values test passed!');
    } catch (error) {
      console.error('❌ Enum test error:', error);
      throw error;
    }
  });
});
