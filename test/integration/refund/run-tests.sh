#!/bin/bash

# 退款系统集成测试运行脚本
# 支持运行Mock测试和真实Stripe测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_message $RED "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
    print_message $GREEN "✅ Docker is running"
}

# 检查Node.js依赖
check_dependencies() {
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "📦 Installing dependencies..."
        npm install
    fi
    print_message $GREEN "✅ Dependencies are ready"
}

# 设置测试环境变量
setup_test_env() {
    export NODE_ENV=test
    export MONGODB_URI=mongodb://localhost:27017/test_refund
    export REDIS_URL=redis://localhost:6379
    
    # 如果没有设置Stripe密钥，使用默认测试密钥
    if [ -z "$STRIPE_SECRET_KEY" ]; then
        export STRIPE_SECRET_KEY=sk_test_51RC9DgRw6scKj0ZOTdVY2bJAekzcYb3173ewXVnOwfaxceofKBRIK6cpDKNTquKMgM3xEjwg87AgZ6JXS0HEGuSU00BuOEDr1z
        print_message $YELLOW "⚠️  Using default Stripe test key"
    else
        print_message $GREEN "✅ Using custom Stripe test key"
    fi
}

# 运行Mock测试
run_mock_tests() {
    print_message $BLUE "🧪 Running Mock Stripe Tests..."
    
    npx jest \
        --config test/integration/refund/jest.config.js \
        --testPathIgnorePatterns="stripe.real.integration.test.js" \
        --verbose \
        --detectOpenHandles \
        --forceExit
}

# 运行真实Stripe测试
run_real_stripe_tests() {
    print_message $BLUE "💳 Running Real Stripe Tests..."
    
    if [[ ! $STRIPE_SECRET_KEY == sk_test_* ]]; then
        print_message $RED "❌ Real Stripe tests require a test API key (sk_test_...)"
        print_message $YELLOW "💡 Set STRIPE_SECRET_KEY environment variable"
        return 1
    fi
    
    npx jest \
        --config test/integration/refund/jest.config.js \
        --testPathPattern="stripe.real.integration.test.js" \
        --verbose \
        --detectOpenHandles \
        --forceExit
}

# 运行所有测试
run_all_tests() {
    print_message $BLUE "🚀 Running All Refund Integration Tests..."
    
    npx jest \
        --config test/integration/refund/jest.config.js \
        --verbose \
        --coverage \
        --detectOpenHandles \
        --forceExit
}

# 生成覆盖率报告
generate_coverage() {
    print_message $BLUE "📊 Generating Coverage Report..."
    
    npx jest \
        --config test/integration/refund/jest.config.js \
        --coverage \
        --coverageReporters=text \
        --coverageReporters=lcov \
        --coverageReporters=html \
        --silent
    
    if [ -f "coverage/refund-integration/lcov-report/index.html" ]; then
        print_message $GREEN "📈 Coverage report generated: coverage/refund-integration/lcov-report/index.html"
    fi
}

# 清理测试环境
cleanup() {
    print_message $YELLOW "🧹 Cleaning up test environment..."
    
    # 停止可能残留的容器
    docker ps -q --filter "ancestor=mongo:5.0" | xargs -r docker stop
    docker ps -q --filter "ancestor=redis:7-alpine" | xargs -r docker stop
    
    # 清理测试数据库
    if command -v mongosh &> /dev/null; then
        mongosh --eval "db.dropDatabase()" mongodb://localhost:27017/test_refund 2>/dev/null || true
    fi
    
    print_message $GREEN "✅ Cleanup completed"
}

# 显示帮助信息
show_help() {
    echo "退款系统集成测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  mock          运行Mock Stripe测试"
    echo "  real          运行真实Stripe测试"
    echo "  all           运行所有测试"
    echo "  coverage      生成覆盖率报告"
    echo "  cleanup       清理测试环境"
    echo "  help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  STRIPE_SECRET_KEY    Stripe测试密钥 (sk_test_...)"
    echo "  VERBOSE_TESTS        启用详细日志 (true/false)"
    echo ""
    echo "示例:"
    echo "  $0 mock              # 运行Mock测试"
    echo "  $0 real              # 运行真实Stripe测试"
    echo "  $0 all               # 运行所有测试"
    echo "  VERBOSE_TESTS=true $0 all  # 运行所有测试并显示详细日志"
}

# 主函数
main() {
    local command=${1:-all}
    
    case $command in
        mock)
            check_docker
            check_dependencies
            setup_test_env
            run_mock_tests
            ;;
        real)
            check_docker
            check_dependencies
            setup_test_env
            run_real_stripe_tests
            ;;
        all)
            check_docker
            check_dependencies
            setup_test_env
            run_all_tests
            ;;
        coverage)
            check_docker
            check_dependencies
            setup_test_env
            generate_coverage
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 运行主函数
main "$@"
