const RefundTestHelpers = require('./refund.test.helpers');
const stripeRefundService = require('../../../services/stripeRefundService');
const refundService = require('../../../services/refundService');
const { REFUND_STATUS, REFUND_REASON } = require('../../../helpers/enum');

describe('Stripe Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
  });

  afterAll(async () => {
    await testHelpers.stopContainers();
  });

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('R-STRIPE-001: Stripe退款API调用成功', () => {
    test('should successfully call Stripe refund API', async () => {
      // Precondition: 配置Stripe测试环境
      const mockStripeRefund = {
        id: 're_test_stripe_success',
        amount: 3000, // $30.00 in cents
        status: 'pending',
        payment_intent: 'pi_test_payment_intent_123'
      };

      // Mock Stripe API
      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue(mockStripeRefund);

      // Steps: 发起部分退款请求
      const paymentIntentId = 'pi_test_payment_intent_123';
      const refundAmount = 30.00;

      const result = await stripeRefundService.createRefund(
        paymentIntentId,
        refundAmount,
        'requested_by_customer'
      );

      // Expected Results
      expect(result).toBeTruthy();
      expect(result.id).toBe('re_test_stripe_success');
      expect(result.amount).toBe(3000);
      expect(result.status).toMatch(/pending|succeeded/);
      expect(result.payment_intent).toBe(paymentIntentId);

      // 验证API调用参数
      expect(stripeRefundService.createRefund).toHaveBeenCalledWith(
        paymentIntentId,
        refundAmount,
        'requested_by_customer'
      );
    });

    test('should handle different refund amounts correctly', async () => {
      // Test various amounts
      const testCases = [
        { amount: 10.50, expectedCents: 1050 },
        { amount: 100.00, expectedCents: 10000 },
        { amount: 0.99, expectedCents: 99 }
      ];

      for (const testCase of testCases) {
        jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
          id: 're_test_amount',
          amount: testCase.expectedCents,
          status: 'succeeded'
        });

        const result = await stripeRefundService.createRefund(
          'pi_test',
          testCase.amount
        );

        expect(result.amount).toBe(testCase.expectedCents);
      }
    });
  });

  describe('R-STRIPE-002: Stripe退款API调用失败', () => {
    test('should handle Stripe API errors gracefully', async () => {
      // Precondition: Mock Stripe API返回错误
      const stripeError = new Error('Your card was declined.');
      stripeError.type = 'StripeCardError';
      stripeError.code = 'card_declined';

      jest.spyOn(stripeRefundService, 'createRefund').mockRejectedValue(stripeError);

      // Steps: 发起退款请求
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');

      try {
        await refundService.initiateRefund(
          order._id,
          30.00,
          REFUND_REASON.MERCHANT_OTHER,
          null,
          'PARTIAL',
          order.restaurantId
        );
        
        // 不应该到达这里
        expect(true).toBe(false);
      } catch (error) {
        // Expected Results: 验证错误处理
        expect(error.message).toContain('Stripe refund failed');
      }

      // 验证退款记录状态
      const refunds = await testHelpers.testData.refund?.find() || [];
      if (refunds.length > 0) {
        expect(refunds[0].status).toBe(REFUND_STATUS.FAILED);
        expect(refunds[0].errorMessage).toContain('card_declined');
      }
    });

    test('should handle different Stripe error types', async () => {
      const errorTypes = [
        {
          type: 'StripeInvalidRequestError',
          message: 'Invalid request',
          expectedMessage: 'Invalid request'
        },
        {
          type: 'StripeAPIError',
          message: 'API error',
          expectedMessage: 'Stripe API error'
        },
        {
          type: 'StripeConnectionError',
          message: 'Network error',
          expectedMessage: 'Network error'
        }
      ];

      for (const errorType of errorTypes) {
        const stripeError = new Error(errorType.message);
        stripeError.type = errorType.type;

        jest.spyOn(stripeRefundService, 'createRefund').mockRejectedValue(stripeError);

        await expect(
          stripeRefundService.createRefund('pi_test', 30.00)
        ).rejects.toThrow(errorType.expectedMessage);
      }
    });
  });

  describe('R-STRIPE-003: Webhook事件处理', () => {
    test('should process refund.updated webhook successfully', async () => {
      // Precondition: 创建处理中的退款记录
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      
      // 创建退款记录
      const refundResult = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        null,
        'PARTIAL',
        order.restaurantId
      );

      const stripeRefundId = 're_test_webhook_success';
      
      // 更新退款记录的Stripe ID
      if (refundResult.refund) {
        refundResult.refund.stripeRefundId = stripeRefundId;
        await refundResult.refund.save();
      }

      // Steps: 发送refund.updated webhook
      const webhookData = testHelpers.generateWebhookEvent(
        'refund.updated',
        stripeRefundId,
        'succeeded'
      );

      await refundService.processRefundWebhook(
        stripeRefundId,
        'succeeded',
        webhookData.data.object
      );

      // Expected Results: 验证退款记录状态更新
      if (refundResult.refund) {
        const updatedRefund = await testHelpers.verifyRefundRecord(
          refundResult.refund.refundId,
          REFUND_STATUS.SUCCEEDED
        );

        expect(updatedRefund.completedAt).toBeTruthy();
      }

      // 验证订单状态更新
      await testHelpers.verifyOrderStatus(
        order._id,
        'PARTIALLY_REFUNDED'
      );
    });

    test('should handle refund failure webhook', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      
      const refundResult = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        null,
        'PARTIAL',
        order.restaurantId
      );

      const stripeRefundId = 're_test_webhook_failure';
      
      if (refundResult.refund) {
        refundResult.refund.stripeRefundId = stripeRefundId;
        await refundResult.refund.save();
      }

      // Steps: 发送失败webhook
      const webhookData = testHelpers.generateWebhookEvent(
        'refund.updated',
        stripeRefundId,
        'failed'
      );

      await refundService.processRefundWebhook(
        stripeRefundId,
        'failed',
        webhookData.data.object
      );

      // Expected Results
      if (refundResult.refund) {
        const updatedRefund = await testHelpers.verifyRefundRecord(
          refundResult.refund.refundId,
          REFUND_STATUS.FAILED
        );

        expect(updatedRefund.errorMessage).toBe('insufficient_funds');
        expect(updatedRefund.completedAt).toBeTruthy();
      }
    });

    test('should handle webhook for non-existent refund', async () => {
      // Steps: 发送不存在退款ID的webhook
      const nonExistentRefundId = 're_nonexistent_refund';

      // 应该不抛出错误，只是记录警告
      await expect(
        refundService.processRefundWebhook(
          nonExistentRefundId,
          'succeeded',
          { id: nonExistentRefundId }
        )
      ).resolves.not.toThrow();
    });
  });

  describe('Stripe Service Validation', () => {
    test('should validate refund amount against payment intent', async () => {
      // Mock Stripe payment intent retrieval
      const mockPaymentIntent = {
        amount: 10000, // $100.00 in cents
        status: 'succeeded'
      };

      const mockExistingRefunds = [
        { amount: 3000, status: 'succeeded' } // $30.00 already refunded
      ];

      jest.spyOn(stripeRefundService, 'validateRefundAmount').mockResolvedValue({
        isValid: true,
        availableAmount: 70.00,
        requestedAmount: 30.00,
        totalRefunded: 30.00,
        originalAmount: 100.00
      });

      // Steps
      const validation = await stripeRefundService.validateRefundAmount(
        'pi_test_payment_intent',
        30.00
      );

      // Expected Results
      expect(validation.isValid).toBe(true);
      expect(validation.availableAmount).toBe(70.00);
      expect(validation.requestedAmount).toBe(30.00);
    });

    test('should reject refund exceeding available amount', async () => {
      jest.spyOn(stripeRefundService, 'validateRefundAmount').mockResolvedValue({
        isValid: false,
        availableAmount: 20.00,
        requestedAmount: 30.00,
        totalRefunded: 80.00,
        originalAmount: 100.00
      });

      const validation = await stripeRefundService.validateRefundAmount(
        'pi_test_payment_intent',
        30.00
      );

      expect(validation.isValid).toBe(false);
      expect(validation.availableAmount).toBe(20.00);
    });
  });
});
