const mongoose = require('mongoose');
const { GenericContainer } = require('testcontainers');
const Order = require('../../../models/order');
const Refund = require('../../../models/refund');
const Restaurant = require('../../../models/restaurant');
const Customer = require('../../../models/customer');
const Stripe = require('../../../models/stripe');
const { REFUND_REASON, REFUND_STATUS, ORDER_STATUS } = require('../../../helpers/enum');

class RefundTestHelpers {
  constructor() {
    this.mongoContainer = null;
    this.redisContainer = null;
    this.testData = {};
  }

  /**
   * 启动测试容器
   */
  async startContainers() {
    // 启动MongoDB容器
    this.mongoContainer = await new GenericContainer('mongo:5.0')
      .withExposedPorts(27017)
      .withEnvironment({
        MONGO_INITDB_ROOT_USERNAME: 'admin',
        MONGO_INITDB_ROOT_PASSWORD: 'password'
      })
      .start();

    // 启动Redis容器
    this.redisContainer = await new GenericContainer('redis:7-alpine')
      .withExposedPorts(6379)
      .start();

    // 连接到测试数据库
    const mongoUri = `mongodb://admin:password@${this.mongoContainer.getHost()}:${this.mongoContainer.getMappedPort(27017)}/test_refund?authSource=admin`;
    await mongoose.connect(mongoUri);

    console.log('Test containers started successfully');
  }

  /**
   * 停止测试容器
   */
  async stopContainers() {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
    
    if (this.mongoContainer) {
      await this.mongoContainer.stop();
    }
    
    if (this.redisContainer) {
      await this.redisContainer.stop();
    }
    
    console.log('Test containers stopped');
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    await Promise.all([
      Order.deleteMany({}),
      Refund.deleteMany({}),
      Restaurant.deleteMany({}),
      Customer.deleteMany({}),
      Stripe.deleteMany({})
    ]);
  }

  /**
   * 创建测试餐厅
   */
  async createTestRestaurant() {
    const restaurant = new Restaurant({
      name: 'Test Restaurant',
      address: 'Test Address',
      phone: '+**********',
      email: '<EMAIL>',
      commissionRate: 15,
      deliveryTime: 30,
      isAvailable: true,
      stripeAccountId: 'acct_test123',
      stripeDetailsSubmitted: true
    });

    const savedRestaurant = await restaurant.save();
    this.testData.restaurant = savedRestaurant;
    return savedRestaurant;
  }

  /**
   * 创建测试客户
   */
  async createTestCustomer() {
    const customer = new Customer({
      customerId: 'CUST-TEST-001',
      phone: '+**********',
      email: '<EMAIL>',
      name: 'Test Customer',
      orderHistory: []
    });

    const savedCustomer = await customer.save();
    this.testData.customer = savedCustomer;
    return savedCustomer;
  }

  /**
   * 创建已支付的测试订单
   */
  async createPaidOrder(orderAmount = 100.00, paymentMethod = 'STRIPE') {
    const restaurant = this.testData.restaurant || await this.createTestRestaurant();
    const customer = this.testData.customer || await this.createTestCustomer();

    const order = new Order({
      orderId: `ORDER-TEST-${Date.now()}`,
      orderAmount,
      restaurantId: restaurant._id.toString(),
      restaurantName: restaurant.name,
      restaurantBrand: 'Test Brand',
      restaurantBrandId: restaurant._id,
      customerId: customer.customerId,
      customerPhone: customer.phone,
      items: [{
        _id: new mongoose.Types.ObjectId(),
        title: 'Test Item',
        food: 'test-food-id',
        description: 'Test food item',
        quantity: 2,
        variation: {
          _id: new mongoose.Types.ObjectId(),
          title: 'Regular',
          price: 25.00,
          discounted: 0
        },
        addons: [],
        isActive: true
      }],
      deliveryAddress: 'Test Delivery Address',
      deliveryAddressId: 'addr-test-001',
      deliveryCoordinates: {
        type: 'Point',
        coordinates: [-74.006, 40.7128]
      },
      orderStatus: ORDER_STATUS.PENDING,
      paymentMethod,
      paymentStatus: 'PAID',
      paidAmount: orderAmount,
      deliveryCharges: 5.00,
      tipping: 10.00,
      taxationAmount: 8.50,
      isPickedUp: false,
      paymentId: 'pi_test_payment_intent_123',
      refunds: [],
      totalRefunded: 0,
      refundStatus: 'NONE'
    });

    const savedOrder = await order.save();
    this.testData.order = savedOrder;
    return savedOrder;
  }



  /**
   * 创建部分退款的订单
   */
  async createPartiallyRefundedOrder(orderAmount = 100.00, refundedAmount = 30.00) {
    const order = await this.createPaidOrder(orderAmount);
    
    // 创建退款记录
    const refund = new Refund({
      orderId: order.orderId,
      originalOrderId: order._id,
      refundType: 'PARTIAL',
      requestAmount: refundedAmount,
      finalRefundAmount: refundedAmount,
      reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK,
      feeBearer: 'MERCHANT',
      transactionFee: 3.20,
      status: REFUND_STATUS.SUCCEEDED,
      stripeRefundId: 're_test_refund_123',
      requestedBy: this.testData.restaurant._id,
      completedAt: new Date()
    });

    const savedRefund = await refund.save();

    // 更新订单
    order.refunds.push(savedRefund._id);
    order.totalRefunded = refundedAmount;
    order.refundStatus = 'PARTIAL';
    order.orderStatus = ORDER_STATUS.PARTIALLY_REFUNDED;

    await order.save();
    this.testData.refund = savedRefund;
    return { order, refund: savedRefund };
  }

  /**
   * Mock Stripe服务
   */
  mockStripeService() {
    const mockStripe = {
      refunds: {
        create: jest.fn().mockResolvedValue({
          id: 're_test_mock_refund',
          amount: 3000, // $30.00 in cents
          status: 'pending',
          payment_intent: 'pi_test_payment_intent_123'
        }),
        retrieve: jest.fn().mockResolvedValue({
          id: 're_test_mock_refund',
          status: 'succeeded'
        })
      }
    };

    return mockStripe;
  }

  /**
   * 创建GraphQL请求上下文
   */
  createGraphQLContext(restaurantId = null) {
    const restaurant = restaurantId || (this.testData.restaurant && this.testData.restaurant._id);
    
    return {
      req: {
        restaurantId: restaurant ? restaurant.toString() : null,
        isAuth: !!restaurant
      },
      res: {}
    };
  }

  /**
   * 验证退款记录
   */
  async verifyRefundRecord(refundId, expectedStatus = REFUND_STATUS.SUCCEEDED) {
    const refund = await Refund.findOne({ refundId });
    expect(refund).toBeTruthy();
    expect(refund.status).toBe(expectedStatus);
    return refund;
  }

  /**
   * 验证订单状态
   */
  async verifyOrderStatus(orderId, expectedStatus, expectedRefundStatus = null) {
    const order = await Order.findById(orderId);
    expect(order).toBeTruthy();
    expect(order.orderStatus).toBe(expectedStatus);
    
    if (expectedRefundStatus) {
      expect(order.refundStatus).toBe(expectedRefundStatus);
    }
    
    return order;
  }

  /**
   * 计算预期手续费
   */
  calculateExpectedFee(orderAmount, refundAmount = null) {
    const feeRate = 0.029; // 2.9%
    const fixedFee = 0.30; // €0.30
    
    if (refundAmount) {
      // 部分退款按比例计算
      const ratio = refundAmount / orderAmount;
      return (orderAmount * feeRate + fixedFee) * ratio;
    } else {
      // 全额退款
      return orderAmount * feeRate + fixedFee;
    }
  }

  /**
   * 生成测试用的Webhook事件
   */
  generateWebhookEvent(type, refundId, status = 'succeeded') {
    return {
      type,
      data: {
        object: {
          id: refundId,
          status,
          amount: 3000,
          payment_intent: 'pi_test_payment_intent_123',
          failure_reason: status === 'failed' ? 'insufficient_funds' : null
        }
      }
    };
  }
}

module.exports = RefundTestHelpers;
