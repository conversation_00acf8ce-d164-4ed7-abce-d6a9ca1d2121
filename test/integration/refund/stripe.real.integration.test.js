/**
 * 真实Stripe API集成测试
 * 
 * 本测试使用真实的Stripe测试环境，而不是Mock
 * 需要配置真实的Stripe测试密钥
 */

const RefundTestHelpers = require('./refund.test.helpers');
const stripeRefundService = require('../../../services/stripeRefundService');
const refundService = require('../../../services/refundService');
const { REFUND_STATUS, REFUND_REASON } = require('../../../helpers/enum');

// 只在有真实Stripe密钥时运行这些测试
const hasRealStripeKey = process.env.STRIPE_SECRET_KEY && 
  process.env.STRIPE_SECRET_KEY.startsWith('sk_test_');

const describeIf = (condition, ...args) => condition ? describe(...args) : describe.skip(...args);

describeIf(hasRealStripeKey, 'Real Stripe Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
    
    console.log('🔑 Using real Stripe test environment');
    console.log('📝 Stripe key:', process.env.STRIPE_SECRET_KEY?.substring(0, 20) + '...');
  }, 60000);

  afterAll(async () => {
    await testHelpers.stopContainers();
  }, 30000);

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
  });

  describe('Real Stripe Payment Intent Creation', () => {
    test('should create real payment intent for testing', async () => {
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      // 创建真实的支付意图
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 10000, // $100.00
        currency: 'eur',
        payment_method_types: ['card'],
        metadata: {
          test_order_id: 'TEST-ORDER-001',
          integration_test: 'true'
        }
      });

      expect(paymentIntent).toBeTruthy();
      expect(paymentIntent.id).toMatch(/^pi_/);
      expect(paymentIntent.amount).toBe(10000);
      expect(paymentIntent.currency).toBe('eur');
      expect(paymentIntent.status).toBe('requires_payment_method');

      console.log('✅ Created payment intent:', paymentIntent.id);
    });
  });

  describe('Real Stripe Refund Creation', () => {
    let paymentIntentId;

    beforeEach(async () => {
      // 创建并确认支付意图
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 10000,
        currency: 'eur',
        payment_method_types: ['card'],
        confirm: true,
        payment_method: 'pm_card_visa', // 测试卡
        return_url: 'https://example.com/return',
        metadata: {
          test_refund: 'true'
        }
      });

      paymentIntentId = paymentIntent.id;
      console.log('💳 Created and confirmed payment intent:', paymentIntentId);
    });

    test('should create real refund successfully', async () => {
      // 使用真实的Stripe服务创建退款
      const refund = await stripeRefundService.createRefund(
        paymentIntentId,
        30.00, // $30.00 refund
        'requested_by_customer',
        { test_refund: 'integration_test' }
      );

      expect(refund).toBeTruthy();
      expect(refund.id).toMatch(/^re_/);
      expect(refund.amount).toBe(3000); // $30.00 in cents
      expect(refund.payment_intent).toBe(paymentIntentId);
      expect(refund.status).toMatch(/pending|succeeded/);

      console.log('💰 Created real refund:', refund.id, 'Status:', refund.status);

      // 验证退款状态
      const refundStatus = await stripeRefundService.getRefundStatus(refund.id);
      expect(refundStatus.id).toBe(refund.id);
      
      console.log('📊 Refund status:', refundStatus.status);
    });

    test('should handle partial refund correctly', async () => {
      // 创建部分退款
      const refund1 = await stripeRefundService.createRefund(
        paymentIntentId,
        25.00
      );

      expect(refund1.amount).toBe(2500);

      // 创建第二次部分退款
      const refund2 = await stripeRefundService.createRefund(
        paymentIntentId,
        15.00
      );

      expect(refund2.amount).toBe(1500);

      // 验证总退款金额
      const refunds = await stripeRefundService.listRefunds(paymentIntentId);
      const totalRefunded = refunds.reduce((sum, r) => sum + r.amount, 0);
      
      expect(totalRefunded).toBe(4000); // $40.00 total
      expect(refunds).toHaveLength(2);

      console.log('📈 Multiple refunds created successfully');
      console.log('💵 Total refunded:', totalRefunded / 100, 'EUR');
    });

    test('should validate refund amount against payment intent', async () => {
      // 验证退款金额
      const validation = await stripeRefundService.validateRefundAmount(
        paymentIntentId,
        30.00
      );

      expect(validation.isValid).toBe(true);
      expect(validation.availableAmount).toBe(100.00);
      expect(validation.requestedAmount).toBe(30.00);
      expect(validation.originalAmount).toBe(100.00);

      console.log('✅ Refund amount validation passed');
    });

    test('should reject refund exceeding available amount', async () => {
      // 先创建一个大额退款
      await stripeRefundService.createRefund(paymentIntentId, 80.00);

      // 尝试创建超额退款
      const validation = await stripeRefundService.validateRefundAmount(
        paymentIntentId,
        30.00 // 超过剩余的20.00
      );

      expect(validation.isValid).toBe(false);
      expect(validation.availableAmount).toBe(20.00);

      console.log('❌ Correctly rejected excessive refund amount');
    });
  });

  describe('Real Stripe Error Handling', () => {
    test('should handle invalid payment intent ID', async () => {
      await expect(
        stripeRefundService.createRefund('pi_invalid_id', 30.00)
      ).rejects.toThrow();

      console.log('❌ Correctly handled invalid payment intent');
    });

    test('should handle refund on uncaptured payment', async () => {
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      // 创建未捕获的支付意图
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 5000,
        currency: 'eur',
        payment_method_types: ['card'],
        capture_method: 'manual'
      });

      await expect(
        stripeRefundService.createRefund(paymentIntent.id, 30.00)
      ).rejects.toThrow();

      console.log('❌ Correctly handled uncaptured payment refund attempt');
    });
  });

  describe('Integration with Refund Service', () => {
    test('should integrate with refund service using real Stripe', async () => {
      // 创建测试订单
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      
      // 创建真实的支付意图并更新订单
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 10000,
        currency: 'eur',
        payment_method_types: ['card'],
        confirm: true,
        payment_method: 'pm_card_visa',
        return_url: 'https://example.com/return'
      });

      // 更新订单的支付ID
      order.paymentId = paymentIntent.id;
      await order.save();

      // 使用退款服务发起退款
      const result = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        '真实Stripe集成测试',
        'PARTIAL',
        order.restaurantId
      );

      expect(result.success).toBe(true);
      expect(result.refund).toBeTruthy();
      expect(result.refund.stripeRefundId).toMatch(/^re_/);

      console.log('🔗 Successfully integrated with real Stripe');
      console.log('💳 Stripe refund ID:', result.refund.stripeRefundId);

      // 验证Stripe中的退款记录
      const stripeRefund = await stripeRefundService.getRefundStatus(
        result.refund.stripeRefundId
      );
      expect(stripeRefund.payment_intent).toBe(paymentIntent.id);
    });
  });

  describe('Webhook Simulation', () => {
    test('should handle real webhook events', async () => {
      // 创建退款
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 10000,
        currency: 'eur',
        payment_method_types: ['card'],
        confirm: true,
        payment_method: 'pm_card_visa',
        return_url: 'https://example.com/return'
      });

      order.paymentId = paymentIntent.id;
      await order.save();

      // 发起退款
      const refundResult = await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OTHER,
        null,
        'PARTIAL',
        order.restaurantId
      );

      // 模拟webhook处理
      await refundService.processRefundWebhook(
        refundResult.refund.stripeRefundId,
        'succeeded',
        {
          id: refundResult.refund.stripeRefundId,
          status: 'succeeded',
          amount: 3000
        }
      );

      // 验证退款状态更新
      const updatedRefund = await testHelpers.verifyRefundRecord(
        refundResult.refund.refundId,
        REFUND_STATUS.SUCCEEDED
      );

      expect(updatedRefund.completedAt).toBeTruthy();

      console.log('🎣 Successfully processed webhook event');
    });
  });
});

// 如果没有真实Stripe密钥，显示跳过信息
if (!hasRealStripeKey) {
  console.log('⚠️  Skipping real Stripe tests - no test API key found');
  console.log('💡 Set STRIPE_SECRET_KEY environment variable to run real Stripe tests');
}
