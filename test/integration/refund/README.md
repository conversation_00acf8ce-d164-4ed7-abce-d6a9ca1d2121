# 退款系统集成测试

## 概述

本目录包含退款系统的完整集成测试套件，使用Testcontainers运行真实的MongoDB和Redis实例，确保测试环境与生产环境的一致性。

## 测试结构

```
test/integration/refund/
├── refund.integration.test.js          # 主测试套件
├── cancelOrder.integration.test.js     # 全额退款测试
├── partialRefund.integration.test.js   # 部分退款测试
├── stripe.integration.test.js          # Stripe集成测试
├── queries.integration.test.js         # 查询接口测试
├── refund.test.helpers.js              # 测试工具类
├── jest.config.js                      # Jest配置
├── setup.js                            # 测试环境设置
└── README.md                           # 本文档
```

## 测试覆盖范围

### 1. 全额退款（取消订单）测试
- ✅ R-CANCEL-001: 商家原因全额退款 - 缺货
- ✅ R-CANCEL-002: 客户原因全额退款
- ✅ R-CANCEL-003: 取消未支付订单
- ✅ R-CANCEL-004: 权限验证失败

### 2. 部分退款测试
- ✅ R-PARTIAL-001: 商家原因部分退款
- ✅ R-PARTIAL-002: 客户原因部分退款
- ✅ R-PARTIAL-003: 多次部分退款
- ✅ R-PARTIAL-004: 超额退款验证

### 3. Stripe集成测试
- ✅ R-STRIPE-001: Stripe退款API调用成功
- ✅ R-STRIPE-002: Stripe退款API调用失败
- ✅ R-STRIPE-003: Webhook事件处理

### 4. 查询接口测试
- ✅ R-QUERY-001: 查询退款记录
- ✅ R-QUERY-002: 查询订单退款历史

### 5. 错误处理测试
- ✅ R-ERROR-001: 无效退款金额
- ✅ R-ERROR-002: 并发退款请求
- ✅ R-ERROR-003: 网络超时处理

## 运行测试

### 前置要求

1. **Docker**: 用于运行Testcontainers
2. **Node.js**: 版本 >= 14
3. **依赖包**: 安装项目依赖

```bash
npm install
```

### 运行所有退款集成测试

```bash
# 运行完整的退款集成测试套件
npm run test:refund:integration

# 或者使用Jest直接运行
npx jest --config test/integration/refund/jest.config.js
```

### 运行特定测试文件

```bash
# 只运行全额退款测试
npx jest test/integration/refund/cancelOrder.integration.test.js

# 只运行部分退款测试
npx jest test/integration/refund/partialRefund.integration.test.js

# 只运行Stripe集成测试
npx jest test/integration/refund/stripe.integration.test.js
```

### 运行带覆盖率报告的测试

```bash
# 生成覆盖率报告
npx jest --config test/integration/refund/jest.config.js --coverage

# 查看HTML覆盖率报告
open coverage/refund-integration/lcov-report/index.html
```

### 详细模式运行

```bash
# 启用详细日志输出
VERBOSE_TESTS=true npx jest --config test/integration/refund/jest.config.js --verbose
```

## 测试环境

### Testcontainers配置

测试使用以下容器：

- **MongoDB 5.0**: 数据库存储
- **Redis 7**: 缓存和会话存储

容器会在测试开始时自动启动，测试结束时自动清理。

### 环境变量

测试环境使用以下配置：

```javascript
NODE_ENV=test
MONGODB_URI=*********************************************************************
REDIS_URL=redis://localhost:6379
STRIPE_SECRET_KEY=sk_test_...
```

## 测试数据

### 测试工具类 (RefundTestHelpers)

提供以下功能：

- 🐳 **容器管理**: 启动/停止测试容器
- 🗄️ **数据清理**: 清理测试数据
- 🏪 **测试数据创建**: 创建餐厅、客户、订单
- 💳 **Stripe Mock**: 模拟Stripe服务
- 🔍 **验证工具**: 验证退款记录和订单状态

### 示例用法

```javascript
const testHelpers = new RefundTestHelpers();

// 启动容器
await testHelpers.startContainers();

// 创建测试数据
const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
const context = testHelpers.createGraphQLContext(order.restaurantId);

// 验证结果
await testHelpers.verifyRefundRecord(refundId, 'SUCCEEDED');
await testHelpers.verifyOrderStatus(orderId, 'CANCELLED');

// 清理
await testHelpers.stopContainers();
```

## 性能基准

### 测试执行时间

- **单个测试**: < 5秒
- **完整套件**: < 3分钟
- **容器启动**: < 30秒

### 覆盖率目标

- **分支覆盖率**: ≥ 70%
- **函数覆盖率**: ≥ 80%
- **行覆盖率**: ≥ 80%
- **语句覆盖率**: ≥ 80%

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查Docker是否运行
   docker ps
   
   # 清理旧容器
   docker system prune -f
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :27017
   lsof -i :6379
   ```

3. **内存不足**
   ```bash
   # 增加Docker内存限制
   # Docker Desktop > Settings > Resources > Memory
   ```

### 调试模式

```bash
# 启用详细日志
DEBUG=* VERBOSE_TESTS=true npx jest --config test/integration/refund/jest.config.js

# 运行单个测试并保持容器
npx jest --config test/integration/refund/jest.config.js --testNamePattern="should successfully cancel order"
```

## 持续集成

### GitHub Actions配置

```yaml
name: Refund Integration Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:refund:integration
```

### 测试报告

测试完成后会生成：

- **JUnit XML**: `test-results/refund-integration.xml`
- **覆盖率报告**: `coverage/refund-integration/`
- **测试日志**: `logs/refund-integration.log`

## 贡献指南

### 添加新测试

1. 在相应的测试文件中添加测试用例
2. 更新`refund_integration_tests.yaml`文档
3. 确保测试覆盖率达标
4. 添加必要的测试数据和Mock

### 测试命名规范

```javascript
describe('R-[MODULE]-[NUMBER]: [Description]', () => {
  test('should [expected behavior] when [condition]', async () => {
    // 测试实现
  });
});
```

### 最佳实践

- ✅ 使用真实的数据库和缓存
- ✅ 每个测试独立运行
- ✅ 清理测试数据
- ✅ Mock外部服务
- ✅ 验证副作用
- ✅ 测试错误场景
