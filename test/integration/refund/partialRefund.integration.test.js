const RefundTestHelpers = require('./refund.test.helpers');
const refundResolver = require('../../../graphql/resolvers/refund');
const refundService = require('../../../services/refundService');
const stripeRefundService = require('../../../services/stripeRefundService');
const { REFUND_REASON, ORDER_STATUS, REFUND_STATUS } = require('../../../helpers/enum');

describe('Partial Refund Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
  });

  afterAll(async () => {
    await testHelpers.stopContainers();
  });

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
    
    // Mock Stripe service
    jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
      id: 're_test_partial_refund',
      amount: 3000, // $30.00 in cents
      status: 'pending'
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('R-PARTIAL-001: 商家原因部分退款', () => {
    test('should successfully process merchant partial refund', async () => {
      // Precondition: 创建已支付订单（金额100.00）
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 调用refundOrder mutation
      const args = {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER,
        reasonText: '部分商品缺货'
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(true);
      expect(result.refund).toBeTruthy();
      expect(result.refund.finalRefundAmount).toBe(30.00);
      expect(result.refund.reason).toBe(REFUND_REASON.MERCHANT_OTHER);
      expect(result.refund.reasonText).toBe('部分商品缺货');
      expect(result.refund.feeBearer).toBe('MERCHANT');

      // 验证订单状态更新
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.PARTIALLY_REFUNDED,
        'PARTIAL'
      );

      expect(updatedOrder.totalRefunded).toBe(30.00);
      expect(updatedOrder.refunds).toHaveLength(1);

      // 验证Stripe API调用
      expect(stripeRefundService.createRefund).toHaveBeenCalledWith(
        order.paymentId,
        30.00,
        'requested_by_customer',
        expect.any(Object)
      );
    });
  });

  describe('R-PARTIAL-002: 客户原因部分退款', () => {
    test('should handle customer partial refund with fee deduction', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // 计算预期手续费
      const expectedFee = testHelpers.calculateExpectedFee(100.00, 25.00);
      const expectedRefundAmount = 25.00 - expectedFee;

      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_customer_partial',
        amount: Math.round(expectedRefundAmount * 100),
        status: 'pending'
      });

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 25.00,
        reason: REFUND_REASON.CUSTOMER_CANCELLED
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(true);
      expect(result.refund.feeBearer).toBe('CUSTOMER');
      expect(result.refund.finalRefundAmount).toBeLessThan(25.00);
      expect(result.refund.transactionFee).toBeGreaterThan(0);

      // 验证实际退款金额小于请求金额
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.PARTIALLY_REFUNDED
      );

      expect(updatedOrder.totalRefunded).toBeLessThan(25.00);
    });
  });

  describe('R-PARTIAL-003: 多次部分退款', () => {
    test('should handle multiple partial refunds correctly', async () => {
      // Precondition: 创建已有一次部分退款的订单
      const { order } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 发起第二次部分退款
      const args = {
        _id: order._id.toString(),
        amount: 20.00,
        reason: REFUND_REASON.MERCHANT_OTHER,
        reasonText: '再次退款部分商品'
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(true);

      // 验证订单总退款金额更新
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.PARTIALLY_REFUNDED
      );

      expect(updatedOrder.totalRefunded).toBe(50.00); // 30.00 + 20.00
      expect(updatedOrder.refunds).toHaveLength(2);

      // 验证可退款余额
      const availableAmount = order.orderAmount - updatedOrder.totalRefunded;
      expect(availableAmount).toBe(50.00);
    });
  });

  describe('R-PARTIAL-004: 超额退款验证', () => {
    test('should reject refund amount exceeding available balance', async () => {
      // Precondition: 创建已有部分退款的订单
      const { order } = await testHelpers.createPartiallyRefundedOrder(100.00, 80.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 尝试超额退款
      const args = {
        _id: order._id.toString(),
        amount: 30.00, // 超过可退款余额20.00
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results: 应该返回失败
      expect(result.success).toBe(false);
      expect(result.message).toContain('available amount');

      // 验证不创建退款记录
      expect(stripeRefundService.createRefund).not.toHaveBeenCalled();

      // 验证订单状态不变
      const unchangedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.PARTIALLY_REFUNDED
      );

      expect(unchangedOrder.totalRefunded).toBe(80.00); // 保持原有退款金额
    });
  });

  describe('Validation Tests', () => {
    test('should reject negative refund amount', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 传入负数金额
      const args = {
        _id: order._id.toString(),
        amount: -10.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('greater than 0');
    });

    test('should reject zero refund amount', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 0,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('greater than 0');
    });
  });

  describe('Permission Tests', () => {
    test('should deny access to other restaurant orders', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const otherRestaurant = await testHelpers.createTestRestaurant();
      const context = testHelpers.createGraphQLContext(otherRestaurant._id);

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      // Expected Results
      await expect(
        refundResolver.Mutation.refundOrder(null, args, context)
      ).rejects.toThrow('access denied');
    });

    test('should require authentication', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = { req: { restaurantId: null, isAuth: false }, res: {} };

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      // Expected Results
      await expect(
        refundResolver.Mutation.refundOrder(null, args, context)
      ).rejects.toThrow('Unauthenticated');
    });
  });

  describe('Stripe Integration Error Handling', () => {
    test('should handle Stripe API failure', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Mock Stripe failure
      jest.spyOn(stripeRefundService, 'createRefund').mockRejectedValue(
        new Error('Stripe API error: card_declined')
      );

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('Stripe refund failed');
    });
  });
});
