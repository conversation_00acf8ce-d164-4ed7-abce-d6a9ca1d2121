/**
 * 退款系统集成测试套件
 * 
 * 本测试套件包含了退款系统的完整集成测试，涵盖：
 * - 全额退款（取消订单）
 * - 部分退款
 * - Stripe集成
 * - 查询接口
 * - 错误处理
 * - 通知系统
 * 
 * 测试使用Testcontainers运行真实的MongoDB和Redis实例，
 * 确保测试环境与生产环境的一致性。
 */

const RefundTestHelpers = require('./refund.test.helpers');

describe('Refund System Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    console.log('🚀 Starting Refund Integration Test Suite...');
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
    console.log('✅ Test containers started successfully');
  }, 60000); // 增加超时时间用于容器启动

  afterAll(async () => {
    console.log('🧹 Cleaning up test environment...');
    await testHelpers.stopContainers();
    console.log('✅ Test cleanup completed');
  }, 30000);

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
  });

  describe('🏪 Restaurant Authentication Setup', () => {
    test('should create test restaurant and customer', async () => {
      const restaurant = await testHelpers.createTestRestaurant();
      const customer = await testHelpers.createTestCustomer();

      expect(restaurant).toBeTruthy();
      expect(restaurant.name).toBe('Test Restaurant');
      expect(restaurant.stripeAccountId).toBe('acct_test123');

      expect(customer).toBeTruthy();
      expect(customer.customerId).toBe('CUST-TEST-001');
    });
  });

  describe('💰 Order Creation and Payment Setup', () => {
    test('should create paid order with Stripe payment', async () => {
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');

      expect(order).toBeTruthy();
      expect(order.orderAmount).toBe(100.00);
      expect(order.paymentStatus).toBe('PAID');
      expect(order.paymentMethod).toBe('STRIPE');
      expect(order.paymentId).toBe('pi_test_payment_intent_123');
      expect(order.refunds).toHaveLength(0);
      expect(order.totalRefunded).toBe(0);
      expect(order.refundStatus).toBe('NONE');
    });



    test('should create partially refunded order', async () => {
      const { order, refund } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);

      expect(order.totalRefunded).toBe(30.00);
      expect(order.refundStatus).toBe('PARTIAL');
      expect(order.refunds).toHaveLength(1);

      expect(refund.finalRefundAmount).toBe(30.00);
      expect(refund.status).toBe('SUCCEEDED');
    });
  });

  describe('🧮 Fee Calculation Tests', () => {
    test('should calculate transaction fees correctly', async () => {
      const orderAmount = 100.00;
      const refundAmount = 30.00;

      // 全额退款手续费
      const fullRefundFee = testHelpers.calculateExpectedFee(orderAmount);
      expect(fullRefundFee).toBeCloseTo(3.20, 2); // 2.9% + €0.30

      // 部分退款手续费
      const partialRefundFee = testHelpers.calculateExpectedFee(orderAmount, refundAmount);
      expect(partialRefundFee).toBeCloseTo(1.17, 2); // 按比例计算
    });
  });

  describe('🔧 Mock Services Setup', () => {
    test('should setup Stripe mock service correctly', () => {
      const mockStripe = testHelpers.mockStripeService();

      expect(mockStripe.refunds.create).toBeDefined();
      expect(mockStripe.refunds.retrieve).toBeDefined();
      expect(typeof mockStripe.refunds.create).toBe('function');
    });

    test('should create GraphQL context with authentication', () => {
      const restaurant = { _id: 'restaurant_id_123' };
      const context = testHelpers.createGraphQLContext(restaurant._id);

      expect(context.req.restaurantId).toBe('restaurant_id_123');
      expect(context.req.isAuth).toBe(true);
    });
  });

  describe('📊 Data Verification Helpers', () => {
    test('should verify refund record creation', async () => {
      const { refund } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);

      const verifiedRefund = await testHelpers.verifyRefundRecord(
        refund.refundId,
        'SUCCEEDED'
      );

      expect(verifiedRefund).toBeTruthy();
      expect(verifiedRefund.finalRefundAmount).toBe(30.00);
    });

    test('should verify order status updates', async () => {
      const { order } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);

      const verifiedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        'PARTIALLY_REFUNDED',
        'PARTIAL'
      );

      expect(verifiedOrder.totalRefunded).toBe(30.00);
    });
  });

  describe('🌐 Webhook Event Generation', () => {
    test('should generate webhook events correctly', () => {
      const webhookEvent = testHelpers.generateWebhookEvent(
        'refund.updated',
        're_test_refund_123',
        'succeeded'
      );

      expect(webhookEvent.type).toBe('refund.updated');
      expect(webhookEvent.data.object.id).toBe('re_test_refund_123');
      expect(webhookEvent.data.object.status).toBe('succeeded');
      expect(webhookEvent.data.object.amount).toBe(3000);
    });

    test('should generate failure webhook events', () => {
      const failureEvent = testHelpers.generateWebhookEvent(
        'refund.updated',
        're_test_refund_failed',
        'failed'
      );

      expect(failureEvent.data.object.status).toBe('failed');
      expect(failureEvent.data.object.failure_reason).toBe('insufficient_funds');
    });
  });

  describe('🔍 Test Environment Validation', () => {
    test('should have MongoDB connection', async () => {
      const mongoose = require('mongoose');
      expect(mongoose.connection.readyState).toBe(1); // Connected
    });

    test('should clean test data between tests', async () => {
      // 创建一些测试数据
      await testHelpers.createPaidOrder(100.00);
      
      // 清理数据
      await testHelpers.cleanupTestData();
      
      // 验证数据已清理
      const Order = require('../../../models/order');
      const orders = await Order.find({});
      expect(orders).toHaveLength(0);
    });
  });

  describe('📈 Performance and Load Tests', () => {
    test('should handle multiple concurrent order creations', async () => {
      const orderPromises = Array.from({ length: 5 }, (_, i) => 
        testHelpers.createPaidOrder(100.00 + i * 10)
      );

      const orders = await Promise.all(orderPromises);
      
      expect(orders).toHaveLength(5);
      orders.forEach((order, index) => {
        expect(order.orderAmount).toBe(100.00 + index * 10);
      });
    });

    test('should handle rapid refund operations', async () => {
      const order = await testHelpers.createPaidOrder(100.00);
      
      // 快速连续创建多个小额退款
      const refundPromises = [
        { amount: 10.00, reason: 'MERCHANT_OTHER' },
        { amount: 15.00, reason: 'MERCHANT_OTHER' },
        { amount: 20.00, reason: 'MERCHANT_OTHER' }
      ].map(async (refundData) => {
        const refundService = require('../../../services/refundService');
        return refundService.initiateRefund(
          order._id,
          refundData.amount,
          refundData.reason,
          null,
          'PARTIAL',
          order.restaurantId
        );
      });

      const results = await Promise.allSettled(refundPromises);
      
      // 至少应该有一些成功的退款
      const successfulRefunds = results.filter(r => r.status === 'fulfilled');
      expect(successfulRefunds.length).toBeGreaterThan(0);
    });
  });

  describe('🛡️ Security and Validation Tests', () => {
    test('should prevent unauthorized access to refund operations', async () => {
      const order = await testHelpers.createPaidOrder(100.00);
      const unauthorizedContext = { req: { restaurantId: null }, res: {} };

      const refundResolver = require('../../../graphql/resolvers/refund');

      await expect(
        refundResolver.Mutation.refundOrder(null, {
          _id: order._id.toString(),
          amount: 30.00,
          reason: 'MERCHANT_OTHER'
        }, unauthorizedContext)
      ).rejects.toThrow('Unauthenticated');
    });

    test('should validate refund amounts properly', async () => {
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);
      const refundResolver = require('../../../graphql/resolvers/refund');

      // 测试负数金额
      const negativeResult = await refundResolver.Mutation.refundOrder(null, {
        _id: order._id.toString(),
        amount: -10.00,
        reason: 'MERCHANT_OTHER'
      }, context);

      expect(negativeResult.success).toBe(false);
      expect(negativeResult.message).toContain('greater than 0');
    });
  });
});

// 导入并运行所有子测试套件
require('./cancelOrder.integration.test');
require('./partialRefund.integration.test');
require('./stripe.integration.test');
require('./queries.integration.test');
