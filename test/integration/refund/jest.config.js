module.exports = {
  displayName: 'Refund Integration Tests',
  testEnvironment: 'node',
  rootDir: '/home/<USER>/firespoon/Firespoon_API_TF',
  testMatch: [
    '<rootDir>/test/integration/refund/**/*.test.js'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/test/integration/refund/setup.js'
  ],
  testTimeout: 60000, // 60秒超时，用于容器启动
  verbose: true,
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/refund-integration',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    '<rootDir>/services/refund*.js',
    '<rootDir>/services/stripeRefundService.js',
    '<rootDir>/graphql/resolvers/refund.js',
    '<rootDir>/models/refund.js',
    '<rootDir>/routes/stripe.js'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
