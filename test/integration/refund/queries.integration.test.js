const RefundTestHelpers = require('./refund.test.helpers');
const refundResolver = require('../../../graphql/resolvers/refund');
const refundService = require('../../../services/refundService');
const { REFUND_REASON, REFUND_STATUS } = require('../../../helpers/enum');

describe('Refund Queries and Error Handling Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
  });

  afterAll(async () => {
    await testHelpers.stopContainers();
  });

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
  });

  describe('R-QUERY-001: 查询退款记录', () => {
    test('should retrieve refund record with proper authorization', async () => {
      // Precondition: 创建多条退款记录
      const { order, refund } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 调用getRefund query
      const args = { refundId: refund.refundId };

      const result = await refundResolver.Query.getRefund(null, args, context);

      // Expected Results
      expect(result).toBeTruthy();
      expect(result.refundId).toBe(refund.refundId);
      expect(result.finalRefundAmount).toBe(30.00);
      expect(result.reason).toBe(REFUND_REASON.MERCHANT_OUT_OF_STOCK);
      expect(result.status).toBe(REFUND_STATUS.SUCCEEDED);

      // 验证包含关联订单信息
      expect(result.orderId).toBe(order.orderId);
      expect(result.originalOrderId).toBeTruthy();
    });

    test('should deny access to other restaurant refunds', async () => {
      // Precondition
      const { order, refund } = await testHelpers.createPartiallyRefundedOrder(100.00, 30.00);
      const otherRestaurant = await testHelpers.createTestRestaurant();
      const context = testHelpers.createGraphQLContext(otherRestaurant._id);

      // Steps: 尝试查询其他餐厅的退款
      const args = { refundId: refund.refundId };

      // Expected Results: 应该抛出权限错误
      await expect(
        refundResolver.Query.getRefund(null, args, context)
      ).rejects.toThrow('Access denied');
    });

    test('should handle non-existent refund', async () => {
      // Precondition
      await testHelpers.createTestRestaurant();
      const context = testHelpers.createGraphQLContext(testHelpers.testData.restaurant._id);

      // Steps
      const args = { refundId: 'REF-NONEXISTENT-123' };

      // Expected Results
      await expect(
        refundResolver.Query.getRefund(null, args, context)
      ).rejects.toThrow('Refund not found');
    });
  });

  describe('R-QUERY-002: 查询订单退款历史', () => {
    test('should retrieve all refunds for an order', async () => {
      // Precondition: 创建有多次退款的订单
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // 创建多次退款
      await refundService.initiateRefund(
        order._id,
        30.00,
        REFUND_REASON.MERCHANT_OUT_OF_STOCK,
        '第一次退款',
        'PARTIAL',
        order.restaurantId
      );

      await refundService.initiateRefund(
        order._id,
        20.00,
        REFUND_REASON.MERCHANT_OTHER,
        '第二次退款',
        'PARTIAL',
        order.restaurantId
      );

      // Steps: 调用getOrderRefunds query
      const args = { orderId: order._id.toString() };

      const result = await refundResolver.Query.getOrderRefunds(null, args, context);

      // Expected Results
      expect(result).toHaveLength(2);
      
      // 验证按时间倒序排列
      expect(result[0].createdAt >= result[1].createdAt).toBe(true);
      
      // 验证包含退款状态和金额
      expect(result[0].finalRefundAmount).toBe(20.00);
      expect(result[1].finalRefundAmount).toBe(30.00);
      
      expect(result[0].reasonText).toBe('第二次退款');
      expect(result[1].reasonText).toBe('第一次退款');
    });

    test('should return empty array for order with no refunds', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps
      const args = { orderId: order._id.toString() };

      const result = await refundResolver.Query.getOrderRefunds(null, args, context);

      // Expected Results
      expect(result).toHaveLength(0);
    });
  });

  describe('R-ERROR-001: 无效退款金额验证', () => {
    test('should reject negative refund amount', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 传入负数金额
      const args = {
        _id: order._id.toString(),
        amount: -10.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('greater than 0');
      expect(result.refund).toBeNull();
    });

    test('should reject zero refund amount', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps
      const args = {
        _id: order._id.toString(),
        amount: 0,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('greater than 0');
    });

    test('should reject amount exceeding order total', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 尝试退款超过订单金额
      const args = {
        _id: order._id.toString(),
        amount: 150.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('exceed order amount');
    });
  });

  describe('R-ERROR-002: 并发退款请求', () => {
    test('should handle concurrent refund requests safely', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 同时发起多个退款请求
      const refundPromises = [
        refundResolver.Mutation.refundOrder(null, {
          _id: order._id.toString(),
          amount: 60.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        }, context),
        refundResolver.Mutation.refundOrder(null, {
          _id: order._id.toString(),
          amount: 50.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        }, context)
      ];

      const results = await Promise.allSettled(refundPromises);

      // Expected Results: 只有一个应该成功
      const successfulResults = results.filter(r => 
        r.status === 'fulfilled' && r.value.success
      );
      const failedResults = results.filter(r => 
        r.status === 'fulfilled' && !r.value.success
      );

      expect(successfulResults.length).toBe(1);
      expect(failedResults.length).toBe(1);

      // 验证失败的请求包含冲突错误信息
      const failedResult = failedResults[0].value;
      expect(failedResult.message).toContain('available amount');
    });
  });

  describe('R-ERROR-003: 网络超时处理', () => {
    test('should handle Stripe API timeout', async () => {
      // Precondition: Mock Stripe API超时
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'ETIMEDOUT';

      jest.spyOn(require('../../../services/stripeRefundService'), 'createRefund')
        .mockRejectedValue(timeoutError);

      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 发起退款请求
      const args = {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      };

      const result = await refundResolver.Mutation.refundOrder(null, args, context);

      // Expected Results
      expect(result.success).toBe(false);
      expect(result.message).toContain('timeout');
    });
  });

  describe('Authentication and Authorization', () => {
    test('should require authentication for all refund operations', async () => {
      // Precondition: 无认证上下文
      const order = await testHelpers.createPaidOrder(100.00);
      const unauthenticatedContext = { req: { restaurantId: null }, res: {} };

      // Test refund mutation
      await expect(
        refundResolver.Mutation.refundOrder(null, {
          _id: order._id.toString(),
          amount: 30.00,
          reason: REFUND_REASON.MERCHANT_OTHER
        }, unauthenticatedContext)
      ).rejects.toThrow('Unauthenticated');

      // Test refund query
      await expect(
        refundResolver.Query.getRefund(null, {
          refundId: 'REF-TEST-123'
        }, unauthenticatedContext)
      ).rejects.toThrow('Unauthenticated');

      // Test order refunds query
      await expect(
        refundResolver.Query.getOrderRefunds(null, {
          orderId: order._id.toString()
        }, unauthenticatedContext)
      ).rejects.toThrow('Unauthenticated');
    });
  });

  describe('Data Consistency', () => {
    test('should maintain data consistency during refund operations', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 执行多次退款操作
      const refund1 = await refundResolver.Mutation.refundOrder(null, {
        _id: order._id.toString(),
        amount: 30.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      }, context);

      const refund2 = await refundResolver.Mutation.refundOrder(null, {
        _id: order._id.toString(),
        amount: 20.00,
        reason: REFUND_REASON.MERCHANT_OTHER
      }, context);

      // Expected Results: 验证数据一致性
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        'PARTIALLY_REFUNDED'
      );

      expect(updatedOrder.totalRefunded).toBe(50.00);
      expect(updatedOrder.refunds).toHaveLength(2);

      // 验证退款记录总和与订单退款金额一致
      const allRefunds = await refundService.getOrderRefunds(order.orderId);
      const totalRefundAmount = allRefunds.reduce((sum, refund) => 
        sum + refund.finalRefundAmount, 0
      );

      expect(totalRefundAmount).toBe(updatedOrder.totalRefunded);
    });
  });
});
