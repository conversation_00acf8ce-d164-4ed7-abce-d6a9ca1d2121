/**
 * 基础退款系统测试
 * 验证基本功能和配置
 */

describe('Basic Refund System Tests', () => {
  test('should load refund models correctly', () => {
    const Refund = require('../../../models/refund');
    const Order = require('../../../models/order');
    
    expect(Refund).toBeDefined();
    expect(Order).toBeDefined();
    expect(typeof Refund).toBe('function');
    expect(typeof Order).toBe('function');
  });

  test('should load refund services correctly', () => {
    const refundService = require('../../../services/refundService');
    const refundCalculationService = require('../../../services/refundCalculationService');
    const stripeRefundService = require('../../../services/stripeRefundService');
    
    expect(refundService).toBeDefined();
    expect(refundCalculationService).toBeDefined();
    expect(stripeRefundService).toBeDefined();
  });

  test('should load refund resolvers correctly', () => {
    const refundResolver = require('../../../graphql/resolvers/refund');
    
    expect(refundResolver).toBeDefined();
    expect(refundResolver.Query).toBeDefined();
    expect(refundResolver.Mutation).toBeDefined();
    expect(refundResolver.Query.getRefund).toBeDefined();
    expect(refundResolver.Mutation.refundOrder).toBeDefined();
  });

  test('should have correct enum values', () => {
    const { REFUND_REASON, REFUND_STATUS, REFUND_TYPE } = require('../../../helpers/enum');
    
    expect(REFUND_REASON).toBeDefined();
    expect(REFUND_REASON.MERCHANT_OUT_OF_STOCK).toBe('MERCHANT_OUT_OF_STOCK');
    expect(REFUND_REASON.CUSTOMER_CANCELLED).toBe('CUSTOMER_CANCELLED');
    
    expect(REFUND_STATUS).toBeDefined();
    expect(REFUND_STATUS.PENDING).toBe('PENDING');
    expect(REFUND_STATUS.SUCCEEDED).toBe('SUCCEEDED');
    
    expect(REFUND_TYPE).toBeDefined();
    expect(REFUND_TYPE.FULL).toBe('FULL');
    expect(REFUND_TYPE.PARTIAL).toBe('PARTIAL');
  });

  test('should calculate fees correctly', () => {
    const refundCalculationService = require('../../../services/refundCalculationService');
    
    const mockOrder = {
      orderAmount: 100.00
    };
    
    const calculation = refundCalculationService.calculateRefundAmount(
      mockOrder,
      30.00,
      'MERCHANT_OUT_OF_STOCK'
    );
    
    expect(calculation).toBeDefined();
    expect(calculation.customerReceives).toBe(30.00);
    expect(calculation.feeBearer).toBe('MERCHANT');
    expect(calculation.finalRefundAmount).toBe(30.00);
  });

  test('should validate refund amounts correctly', () => {
    const refundCalculationService = require('../../../services/refundCalculationService');
    
    const mockOrder = {
      orderAmount: 100.00,
      totalRefunded: 0
    };
    
    // 有效金额
    const validResult = refundCalculationService.validateRefundAmount(mockOrder, 50.00);
    expect(validResult.isValid).toBe(true);
    
    // 无效金额（超过订单金额）
    const invalidResult = refundCalculationService.validateRefundAmount(mockOrder, 150.00);
    expect(invalidResult.isValid).toBe(false);
    expect(invalidResult.errors).toContain('Refund amount cannot exceed order amount');
    
    // 负数金额
    const negativeResult = refundCalculationService.validateRefundAmount(mockOrder, -10.00);
    expect(negativeResult.isValid).toBe(false);
    expect(negativeResult.errors).toContain('Refund amount must be greater than 0');
  });
});
