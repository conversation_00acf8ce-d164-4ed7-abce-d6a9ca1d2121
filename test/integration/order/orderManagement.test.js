/**
 * Order Management Integration Tests
 * 测试订单管理系统的集成功能 - 使用现有的GraphQL API
 */

const request = require('supertest');
const { createTestApp } = require('../../helpers/testApp');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('Order Management Integration Tests', () => {
  let app;
  let server;
  let authToken;

  beforeAll(async () => {
    const testAppResult = await createTestApp();
    app = testAppResult.app;
    server = testAppResult.server;
  });

  afterAll(async () => {
    if (server && server.httpServer) {
      await new Promise((resolve) => {
        server.httpServer.close(resolve);
      });
    }
  });

  beforeEach(async () => {
    // Skip database operations for schema tests
    authToken = 'test-token';
  });

  describe('Order GraphQL Schema Tests', () => {
    test('should have order query in schema', async () => {
      const introspectionQuery = `
        query {
          __type(name: "Query") {
            fields {
              name
              args {
                name
                type {
                  name
                }
              }
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: introspectionQuery
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__type).toBeDefined();

      const queries = response.body.data.__type.fields;
      const orderQuery = queries.find(field => field.name === 'order');
      expect(orderQuery).toBeDefined();
    });

    test('should have orders query in schema', async () => {
      const introspectionQuery = `
        query {
          __type(name: "Query") {
            fields {
              name
              args {
                name
                type {
                  name
                }
              }
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: introspectionQuery
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__type).toBeDefined();

      const queries = response.body.data.__type.fields;
      const ordersQuery = queries.find(field => field.name === 'orders');
      expect(ordersQuery).toBeDefined();
    });

  });
});
