/**
 * PayPal Payment Integration Tests
 * 测试PayPal支付系统的集成功能 (GraphQL Schema Tests)
 */

const request = require('supertest');
const { createTestApp } = require('../../helpers/testApp');

describe('PayPal Payment Integration Tests (GraphQL Schema)', () => {
  let app;
  let server;
  let authToken;

  beforeAll(async () => {
    const testAppResult = await createTestApp();
    app = testAppResult.app;
    server = testAppResult.server;
  });

  afterAll(async () => {
    if (server && server.httpServer) {
      await new Promise((resolve) => {
        server.httpServer.close(resolve);
      });
    }
  });

  beforeEach(async () => {
    authToken = 'test-token';
  });

  describe('Payment Related GraphQL Schema', () => {
    test('should have payment related types in schema', async () => {
      const introspectionQuery = `
        query {
          __schema {
            types {
              name
              kind
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: introspectionQuery
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__schema).toBeDefined();

      const types = response.body.data.__schema.types;
      const paymentTypes = types.filter(type =>
        type.name.toLowerCase().includes('payment') ||
        type.name.toLowerCase().includes('order')
      );
      expect(paymentTypes.length).toBeGreaterThan(0);
    });

    test('should have query types in schema', async () => {
      const introspectionQuery = `
        query {
          __type(name: "Query") {
            fields {
              name
              type {
                name
              }
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          query: introspectionQuery
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__type).toBeDefined();

      const queries = response.body.data.__type.fields;
      expect(Array.isArray(queries)).toBe(true);
      expect(queries.length).toBeGreaterThan(0);
    });
  });
});
