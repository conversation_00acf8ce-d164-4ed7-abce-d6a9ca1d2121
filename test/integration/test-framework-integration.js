/**
 * 测试框架整合验证脚本
 * 验证GraphQL schema冲突是否已解决
 */

const { loadSchema } = require('@graphql-tools/load');
const { GraphQLFileLoader } = require('@graphql-tools/graphql-file-loader');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const path = require('path');

async function testSchemaIntegration() {
  try {
    console.log('🔍 Testing GraphQL schema integration...');
    
    // 加载所有GraphQL schema文件
    const schemaPath = path.join(__dirname, '../../graphql/schema/types/*.graphql');
    console.log('📁 Loading schema from:', schemaPath);
    
    const typeDefs = await loadSchema(schemaPath, {
      loaders: [new GraphQLFileLoader()]
    });
    
    console.log('✅ Schema loaded successfully');
    
    // 尝试合并类型定义
    const mergedSchema = mergeTypeDefs(typeDefs);
    console.log('✅ Schema merged successfully');
    
    // 检查特定类型
    const schemaString = mergedSchema.toString();
    
    // 检查OrderBrief类型
    const orderBriefMatches = schemaString.match(/type OrderBrief \{[^}]+\}/g);
    if (orderBriefMatches) {
      console.log('📋 OrderBrief type definitions found:', orderBriefMatches.length);
      orderBriefMatches.forEach((match, index) => {
        console.log(`   ${index + 1}:`, match.replace(/\s+/g, ' '));
      });
    }
    
    // 检查退款相关类型
    const refundTypes = ['RefundReason', 'RefundType', 'RefundStatus', 'Refund'];
    refundTypes.forEach(type => {
      if (schemaString.includes(`enum ${type}`) || schemaString.includes(`type ${type}`)) {
        console.log(`✅ ${type} type found in schema`);
      } else {
        console.log(`❌ ${type} type NOT found in schema`);
      }
    });
    
    console.log('🎉 Schema integration test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Schema integration test failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('Unable to merge')) {
      console.error('🔧 This appears to be a schema merge conflict.');
      console.error('💡 Check for duplicate type definitions or conflicting field types.');
    }
    
    return false;
  }
}

async function testRefundIntegration() {
  try {
    console.log('\n🔍 Testing refund module integration...');
    
    // 测试模块加载
    const refundService = require('../../services/refundService');
    const refundResolver = require('../../graphql/resolvers/refund');
    const restaurantResolver = require('../../graphql/resolvers/restaurant');
    
    console.log('✅ Refund service loaded');
    console.log('✅ Refund resolver loaded');
    console.log('✅ Restaurant resolver loaded');
    
    // 检查关键函数
    const requiredFunctions = [
      { module: refundService, name: 'refundService', functions: ['initiateRefund', 'processRefundWebhook'] },
      { module: refundResolver.Query, name: 'refundResolver.Query', functions: ['getRefund', 'getOrderRefunds'] },
      { module: refundResolver.Mutation, name: 'refundResolver.Mutation', functions: ['refundOrder'] },
      { module: restaurantResolver.Mutation, name: 'restaurantResolver.Mutation', functions: ['cancelOrder'] }
    ];
    
    requiredFunctions.forEach(({ module, name, functions }) => {
      functions.forEach(func => {
        if (typeof module[func] === 'function') {
          console.log(`✅ ${name}.${func} is available`);
        } else {
          console.log(`❌ ${name}.${func} is NOT available`);
        }
      });
    });
    
    console.log('🎉 Refund integration test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Refund integration test failed:');
    console.error('Error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting framework integration tests...\n');
  
  const schemaTest = await testSchemaIntegration();
  const refundTest = await testRefundIntegration();
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   GraphQL Schema Integration: ${schemaTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Refund Module Integration:  ${refundTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (schemaTest && refundTest) {
    console.log('\n🎉 All integration tests passed! Framework is ready for merge.');
    process.exit(0);
  } else {
    console.log('\n❌ Some integration tests failed. Please fix issues before merging.');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = {
  testSchemaIntegration,
  testRefundIntegration
};
