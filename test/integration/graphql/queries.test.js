/**
 * GraphQL Queries Integration Tests
 * 测试所有GraphQL查询操作
 */

const request = require('supertest');
const { createTestApp } = require('../../helpers/testApp');
const { clearTestDB } = require('../../helpers/testDatabase');
const { createCustomer } = require('../../factories/customerFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('GraphQL Queries Integration Tests', () => {
  let app;
  let server;
  let testUser;
  let authToken;

  beforeAll(async () => {
    const testAppResult = await createTestApp();
    app = testAppResult.app;
    server = testAppResult.server;
  });

  beforeEach(async () => {
    // Skip database operations for schema introspection tests
    // These tests only check GraphQL schema, not data operations
    authToken = 'test-token';
  });

  afterAll(async () => {
    if (server && server.httpServer) {
      await new Promise((resolve) => {
        server.httpServer.close(resolve);
      });
    }
  });

  describe('GraphQL Schema Introspection', () => {
    test('should respond to GraphQL introspection query', async () => {
      const introspectionQuery = `
        query IntrospectionQuery {
          __schema {
            types {
              name
              kind
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({
          query: introspectionQuery
        })
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.__schema).toBeDefined();
      expect(response.body.data.__schema.types).toBeInstanceOf(Array);
    });

    test('should have Restaurant type in schema', async () => {
      const typeQuery = `
        query {
          __type(name: "Restaurant") {
            name
            kind
            fields {
              name
              type {
                name
              }
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({
          query: typeQuery
        })
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('Restaurant');
    });

  });
});
