module.exports = {
  testEnvironment: 'node',
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  moduleFileExtensions: ['js', 'json', 'node'],
  testMatch: ['**/__tests__/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[tj]s?(x)'],
  transformIgnorePatterns: ['node_modules/'],
  setupFilesAfterEnv: ['./jest.setup.js'],
  // 禁用并行测试执行，按顺序运行测试
  maxWorkers: 1,
  // 增加测试超时时间
  testTimeout: 30000,
  // 在每次测试运行后强制清理
  forceExit: true,
  // 检测内存泄漏
  detectLeaks: true,
  // 在测试之间清理模块缓存
  resetModules: true
};
